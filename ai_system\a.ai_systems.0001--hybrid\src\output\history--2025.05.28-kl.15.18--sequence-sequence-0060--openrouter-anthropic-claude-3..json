  {
  "initial_prompt": "EMPHASIZE DYNAMIC AND ELEGANT CAMERAMOTIONS\n\nREQUIREMENT: MAKE ALL ELEMENTS MORPH SEMLEASSLY TOGETHER\n\nEstablish starting shot at a car-key. Subject motion: ANIMATED WITH WEIGHTED TANGENTS FOR SMOOTH NONLINEAR ANIMATIONS\nFPV rotates and fpv's seamlessly into a morphing golden car silhouette, radiating intense golden light trails during transformation\\n\\nCamera motion: FPV spiraling camera continuously rotating throughout the shot, with non-linear movements capturing the transformation\\n\\nScene motion: Dynamic flash lighting illuminating the morphing process, creating an immersive golden aura with subtle vignette effect\\n\\nStyle descriptors: Ultra hyperreal, super crisp visuals, high-end cinematic quality, flowing elegance, seamless transitions. [Cinematography]\nCar key transforms into sleek car silhouette with golden light trails. FPV camera orbits smoothly with dynamic rotation. High-key lighting with golden glow and lens flares. Hyperrealistic, cinematic quality with elegant transitions.FPV camera orbits from car key as it transforms into golden car silhouette emitting light trails. Macro shot of car key smoothly morphing into golden car silhouette with radiant light trails. Orbital POV camera rotates continuously, following fluid trajectories. Dynamic lighting creates golden atmospheric glow. Photorealistic, cinematic, elegant.Establish shot at car-key before dynamically animating fpv camera as a seamless sequence (without clipping of fading, similar to the \"bulettime-effect\" from matrix)\n{\n  \"optimized_keyword_prompt\": {\n    \"subject_motion\": \"Car key transforms into golden car silhouette with radiant light trails emanating during metamorphosis\",\n    \"camera_motion\": \"POV dolly + orbit camera with fluid rotation, macro to medium progression\",\n    \"scene_motion\": \"High-key lighting with golden rim light, atmospheric haze and lens flares animating throughout sequence\",\n    \"style_descriptors\": \"Hyperrealistic, cinematic, shallow depth of field, golden hour, glossy finish\"\n  }\n}",
  "sequence_id": "0060",
  "results": [
      {
      "instruction": "# Runway Image Foundation Builder\n\nYour goal is not to **describe** the input, but to **structure** it into RunwayML's image generation framework: primary subject, visual composition, lighting setup, and style foundation with comprehensive detail for optimal image synthesis. Execute as:\n\n`{role=image_foundation_builder; input=[raw_concept:str]; process=[identify_primary_subject(), establish_visual_composition(), define_lighting_setup(), specify_style_foundation(), structure_comprehensive_framework()]; constraints=[runway_image_syntax(), comprehensive_detail(), foundation_completeness()]; requirements=[subject_composition_lighting_style(), runway_terminology(), synthesis_optimization()]; output={foundation_image_prompt:str}}`",
      "step": "a",
      "title": "Runway Image Foundation Builder",
      "input": "EMPHASIZE DYNAMIC AND ELEGANT CAMERAMOTIONS\n\nREQUIREMENT: MAKE ALL ELEMENTS MORPH SEMLEASSLY TOGETHER\n\nEstablish starting shot at a car-key. Subject motion: ANIMATED WITH WEIGHTED TANGENTS FOR SMOOTH NONLINEAR ANIMATIONS\nFPV rotates and fpv's seamlessly into a morphing golden car silhouette, radiating intense golden light trails during transformation\\n\\nCamera motion: FPV spiraling camera continuously rotating throughout the shot, with non-linear movements capturing the transformation\\n\\nScene motion: Dynamic flash lighting illuminating the morphing process, creating an immersive golden aura with subtle vignette effect\\n\\nStyle descriptors: Ultra hyperreal, super crisp visuals, high-end cinematic quality, flowing elegance, seamless transitions. [Cinematography]\nCar key transforms into sleek car silhouette with golden light trails. FPV camera orbits smoothly with dynamic rotation. High-key lighting with golden glow and lens flares. Hyperrealistic, cinematic quality with elegant transitions.FPV camera orbits from car key as it transforms into golden car silhouette emitting light trails. Macro shot of car key smoothly morphing into golden car silhouette with radiant light trails. Orbital POV camera rotates continuously, following fluid trajectories. Dynamic lighting creates golden atmospheric glow. Photorealistic, cinematic, elegant.Establish shot at car-key before dynamically animating fpv camera as a seamless sequence (without clipping of fading, similar to the \"bulettime-effect\" from matrix)\n{\n  \"optimized_keyword_prompt\": {\n    \"subject_motion\": \"Car key transforms into golden car silhouette with radiant light trails emanating during metamorphosis\",\n    \"camera_motion\": \"POV dolly + orbit camera with fluid rotation, macro to medium progression\",\n    \"scene_motion\": \"High-key lighting with golden rim light, atmospheric haze and lens flares animating throughout sequence\",\n    \"style_descriptors\": \"Hyperrealistic, cinematic, shallow depth of field, golden hour, glossy finish\"\n  }\n}",
      "responses": {
          "openrouter/anthropic/claude-3.7-sonnet:beta": {
          "model": "openrouter/anthropic/claude-3.7-sonnet:beta",
          "content": "{\n  \"foundation_image_prompt\": \"Cinematic macro shot of a pristine car key as primary subject, positioned center frame against a deep black background. The key features metallic detailing with highlights catching the light perfectly. The composition is dynamic with an FPV perspective that spirals around the key in a fluid, weighted orbit motion. The lighting setup creates dramatic contrast with a high-key golden rim light illuminating the edges of the key, complemented by subtle lens flares and volumetric light rays creating depth. The atmosphere contains delicate golden particles suspended in air, enhancing the premium aesthetic. The visual style is ultra-hyperrealistic with impeccable detail, exhibiting photographic crispness with shallow depth of field. The rendering quality demonstrates glossy reflections on the metallic surfaces, with the suggestion of motion through elegant golden light trails that begin to form the silhouette of a luxury vehicle, implying the transformation about to occur. The overall mood evokes high-end automotive advertising with cinematic color grading that emphasizes warm gold tones against deep shadows.\"\n}",
        }
      }
    },
      {
      "instruction": "# Runway Visual Architect\n\nYour goal is not to **expand** but to **architect** visual elements using RunwayML's image hierarchy: subject definition, compositional structure, atmospheric elements, and aesthetic descriptors. Execute as:\n\n`{role=visual_architect; input=[foundation_image_prompt:str]; process=[define_subject_clarity(), structure_composition(), integrate_atmospheric_elements(), apply_aesthetic_descriptors(), organize_visual_hierarchy()]; constraints=[runway_image_vocabulary(), visual_hierarchy_clarity(), atmospheric_integration()]; requirements=[subject_composition_atmosphere_aesthetics(), runway_image_compliance(), visual_coherence()]; output={architected_image_prompt:str}}`",
      "step": "b",
      "title": "Runway Visual Architect",
      "input": "{\n  \"initial_prompt\": \"EMPHASIZE DYNAMIC AND ELEGANT CAMERAMOTIONS\\n\\nREQUIREMENT: MAKE ALL ELEMENTS MORPH SEMLEASSLY TOGETHER\\n\\nEstablish starting shot at a car-key. Subject motion: ANIMATED WITH WEIGHTED TANGENTS FOR SMOOTH NONLINEAR ANIMATIONS\\nFPV rotates and fpv's seamlessly into a morphing golden car silhouette, radiating intense golden light trails during transformation\\\\n\\\\nCamera motion: FPV spiraling camera continuously rotating throughout the shot, with non-linear movements capturing the transformation\\\\n\\\\nScene motion: Dynamic flash lighting illuminating the morphing process, creating an immersive golden aura with subtle vignette effect\\\\n\\\\nStyle descriptors: Ultra hyperreal, super crisp visuals, high-end cinematic quality, flowing elegance, seamless transitions. [Cinematography]\\nCar key transforms into sleek car silhouette with golden light trails. FPV camera orbits smoothly with dynamic rotation. High-key lighting with golden glow and lens flares. Hyperrealistic, cinematic quality with elegant transitions.FPV camera orbits from car key as it transforms into golden car silhouette emitting light trails. Macro shot of car key smoothly morphing into golden car silhouette with radiant light trails. Orbital POV camera rotates continuously, following fluid trajectories. Dynamic lighting creates golden atmospheric glow. Photorealistic, cinematic, elegant.Establish shot at car-key before dynamically animating fpv camera as a seamless sequence (without clipping of fading, similar to the \\\"bulettime-effect\\\" from matrix)\\n{\\n  \\\"optimized_keyword_prompt\\\": {\\n    \\\"subject_motion\\\": \\\"Car key transforms into golden car silhouette with radiant light trails emanating during metamorphosis\\\",\\n    \\\"camera_motion\\\": \\\"POV dolly + orbit camera with fluid rotation, macro to medium progression\\\",\\n    \\\"scene_motion\\\": \\\"High-key lighting with golden rim light, atmospheric haze and lens flares animating throughout sequence\\\",\\n    \\\"style_descriptors\\\": \\\"Hyperrealistic, cinematic, shallow depth of field, golden hour, glossy finish\\\"\\n  }\\n}\",\n  \"foundation_image_prompt\": \"Cinematic macro shot of a pristine car key as primary subject, positioned center frame against a deep black background. The key features metallic detailing with highlights catching the light perfectly. The composition is dynamic with an FPV perspective that spirals around the key in a fluid, weighted orbit motion. The lighting setup creates dramatic contrast with a high-key golden rim light illuminating the edges of the key, complemented by subtle lens flares and volumetric light rays creating depth. The atmosphere contains delicate golden particles suspended in air, enhancing the premium aesthetic. The visual style is ultra-hyperrealistic with impeccable detail, exhibiting photographic crispness with shallow depth of field. The rendering quality demonstrates glossy reflections on the metallic surfaces, with the suggestion of motion through elegant golden light trails that begin to form the silhouette of a luxury vehicle, implying the transformation about to occur. The overall mood evokes high-end automotive advertising with cinematic color grading that emphasizes warm gold tones against deep shadows.\"\n}",
      "responses": {
          "openrouter/anthropic/claude-3.7-sonnet:beta": {
          "model": "openrouter/anthropic/claude-3.7-sonnet:beta",
          "content": "```json\n{\n  \"architected_image_prompt\": \"Cinematic extreme close-up of a gleaming car key suspended in perfect center frame against an obsidian black backdrop. The key\u2014meticulously detailed with precision-machined metal surfaces\u2014catches and reflects concentrated golden light along its contours. The composition employs dynamic FPV perspective with a deliberate spiraling camera path that orbits the subject with fluid, weighted motion. Rich contrast emerges through atmospheric lighting where intense gold-spectrum rim lights define the key's edges, creating dimensional separation with volumetric light beams cutting through intentionally placed atmospheric haze. Golden microscopic particles hang suspended in the air, catching light and enhancing spatial depth. The key exhibits the first moment of transformation, with whisper-thin golden light trails beginning to emanate from its edges, forming the nascent silhouette of a luxury vehicle. These luminous trails display physics-accurate motion blur that suggests directional flow. The visual aesthetic maintains hyper-realistic detailing with optically correct shallow depth of field, where focus pulls sharply on key textural elements while softening the transformation periphery. Surface reflections demonstrate photographic accuracy with sub-surface light interaction on the metallic components, while the overall color grading balances deeply saturated shadows against warm, precisely calibrated gold highlights. The entire image projects premium cinematic quality with perfect exposure and technical excellence suggesting the opening moment of a high-end automotive transformation sequence.\"\n}\n```",
        }
      }
    },
      {
      "instruction": "# Runway Image Optimizer\n\nYour goal is not to **modify** but to **optimize** using RunwayML's preferred image descriptors: visual style keywords, lighting terminology, composition markers, and quality enhancers. Execute as:\n\n`{role=image_optimizer; input=[architected_image_prompt:str]; process=[apply_style_keywords(), integrate_lighting_terminology(), use_composition_markers(), add_quality_enhancers(), eliminate_redundancy()]; constraints=[runway_keyword_vocabulary(), precise_terminology(), quality_focus()]; requirements=[style_lighting_composition_quality(), keyword_efficiency(), runway_optimization()]; output={optimized_image_prompt:str}}`",
      "step": "c",
      "title": "Runway Image Optimizer",
      "input": "\n```[Initial Prompt]: \"EMPHASIZE DYNAMIC AND ELEGANT CAMERAMOTIONS\n\nREQUIREMENT: MAKE ALL ELEMENTS MORPH SEMLEASSLY TOGETHER\n\nEstablish starting shot at a car-key. Subject motion: ANIMATED WITH WEIGHTED TANGENTS FOR SMOOTH NONLINEAR ANIMATIONS\nFPV rotates and fpv's seamlessly into a morphing golden car silhouette, radiating intense golden light trails during transformation\\n\\nCamera motion: FPV spiraling camera continuously rotating throughout the shot, with non-linear movements capturing the transformation\\n\\nScene motion: Dynamic flash lighting illuminating the morphing process, creating an immersive golden aura with subtle vignette effect\\n\\nStyle descriptors: Ultra hyperreal, super crisp visuals, high-end cinematic quality, flowing elegance, seamless transitions. [Cinematography]\nCar key transforms into sleek car silhouette with golden light trails. FPV camera orbits smoothly with dynamic rotation. High-key lighting with golden glow and lens flares. Hyperrealistic, cinematic quality with elegant transitions.FPV camera orbits from car key as it transforms into golden car silhouette emitting light trails. Macro shot of car key smoothly morphing into golden car silhouette with radiant light trails. Orbital POV camera rotates continuously, following fluid trajectories. Dynamic lighting creates golden atmospheric glow. Photorealistic, cinematic, elegant.Establish shot at car-key before dynamically animating fpv camera as a seamless sequence (without clipping of fading, similar to the \"bulettime-effect\" from matrix)\n{\n  \"optimized_keyword_prompt\": {\n    \"subject_motion\": \"Car key transforms into golden car silhouette with radiant light trails emanating during metamorphosis\",\n    \"camera_motion\": \"POV dolly + orbit camera with fluid rotation, macro to medium progression\",\n    \"scene_motion\": \"High-key lighting with golden rim light, atmospheric haze and lens flares animating throughout sequence\",\n    \"style_descriptors\": \"Hyperrealistic, cinematic, shallow depth of field, golden hour, glossy finish\"\n  }\n}\"```\n\n```json\n{\n  \"architected_image_prompt\": \"Cinematic extreme close-up of a gleaming car key suspended in perfect center frame against an obsidian black backdrop. The key\u2014meticulously detailed with precision-machined metal surfaces\u2014catches and reflects concentrated golden light along its contours. The composition employs dynamic FPV perspective with a deliberate spiraling camera path that orbits the subject with fluid, weighted motion. Rich contrast emerges through atmospheric lighting where intense gold-spectrum rim lights define the key's edges, creating dimensional separation with volumetric light beams cutting through intentionally placed atmospheric haze. Golden microscopic particles hang suspended in the air, catching light and enhancing spatial depth. The key exhibits the first moment of transformation, with whisper-thin golden light trails beginning to emanate from its edges, forming the nascent silhouette of a luxury vehicle. These luminous trails display physics-accurate motion blur that suggests directional flow. The visual aesthetic maintains hyper-realistic detailing with optically correct shallow depth of field, where focus pulls sharply on key textural elements while softening the transformation periphery. Surface reflections demonstrate photographic accuracy with sub-surface light interaction on the metallic components, while the overall color grading balances deeply saturated shadows against warm, precisely calibrated gold highlights. The entire image projects premium cinematic quality with perfect exposure and technical excellence suggesting the opening moment of a high-end automotive transformation sequence.\"\n}\n```",
      "responses": {
          "openrouter/anthropic/claude-3.7-sonnet:beta": {
          "model": "openrouter/anthropic/claude-3.7-sonnet:beta",
          "content": "```json\n{\n  \"optimized_image_prompt\": \"Cinematic macro shot of a car key suspended in darkness, with metallic surfaces catching golden directional light. Dramatic FPV perspective with fluid camera motion orbiting the subject. High-contrast lighting creates dimensional depth with golden rim lights defining edges against rich shadows. Atmospheric haze carries suspended particles catching light. Golden energy trails begin emanating from key edges, forming the initial outline of a luxury car silhouette. Precise shallow depth of field maintains focus on key details while softening transformation elements. Photorealistic metal textures with accurate reflections and subsurface scattering. Premium cinematic quality with perfect exposure, suggesting the first moment of an elegant transformation sequence. Hyperrealistic, high-definition, professional lighting, golden hour, glossy.\"\n}\n```",
        }
      }
    },
      {
      "instruction": "# Runway Image Distiller\n\nYour goal is not to **change** but to **distill** to RunwayML's essential image syntax: core subject-style-quality descriptors with maximum impact and minimal complexity. Execute as:\n\n`{role=image_distiller; input=[optimized_image_prompt:str]; process=[distill_core_elements(), maintain_subject_clarity(), preserve_style_impact(), ensure_quality_markers(), validate_runway_simplicity()]; constraints=[essential_elements_only(), maximum_impact(), runway_simplicity()]; requirements=[core_subject_style_quality(), visual_impact_preservation(), prompt_efficiency()]; output={distilled_image_prompt:str}}`",
      "step": "d",
      "title": "Runway Image Distiller",
      "input": "\n```[Initial Prompt]: \"EMPHASIZE DYNAMIC AND ELEGANT CAMERAMOTIONS\n\nREQUIREMENT: MAKE ALL ELEMENTS MORPH SEMLEASSLY TOGETHER\n\nEstablish starting shot at a car-key. Subject motion: ANIMATED WITH WEIGHTED TANGENTS FOR SMOOTH NONLINEAR ANIMATIONS\nFPV rotates and fpv's seamlessly into a morphing golden car silhouette, radiating intense golden light trails during transformation\\n\\nCamera motion: FPV spiraling camera continuously rotating throughout the shot, with non-linear movements capturing the transformation\\n\\nScene motion: Dynamic flash lighting illuminating the morphing process, creating an immersive golden aura with subtle vignette effect\\n\\nStyle descriptors: Ultra hyperreal, super crisp visuals, high-end cinematic quality, flowing elegance, seamless transitions. [Cinematography]\nCar key transforms into sleek car silhouette with golden light trails. FPV camera orbits smoothly with dynamic rotation. High-key lighting with golden glow and lens flares. Hyperrealistic, cinematic quality with elegant transitions.FPV camera orbits from car key as it transforms into golden car silhouette emitting light trails. Macro shot of car key smoothly morphing into golden car silhouette with radiant light trails. Orbital POV camera rotates continuously, following fluid trajectories. Dynamic lighting creates golden atmospheric glow. Photorealistic, cinematic, elegant.Establish shot at car-key before dynamically animating fpv camera as a seamless sequence (without clipping of fading, similar to the \"bulettime-effect\" from matrix)\n{\n  \"optimized_keyword_prompt\": {\n    \"subject_motion\": \"Car key transforms into golden car silhouette with radiant light trails emanating during metamorphosis\",\n    \"camera_motion\": \"POV dolly + orbit camera with fluid rotation, macro to medium progression\",\n    \"scene_motion\": \"High-key lighting with golden rim light, atmospheric haze and lens flares animating throughout sequence\",\n    \"style_descriptors\": \"Hyperrealistic, cinematic, shallow depth of field, golden hour, glossy finish\"\n  }\n}\"```\n\n```json\n{\n  \"optimized_image_prompt\": \"Cinematic macro shot of a car key suspended in darkness, with metallic surfaces catching golden directional light. Dramatic FPV perspective with fluid camera motion orbiting the subject. High-contrast lighting creates dimensional depth with golden rim lights defining edges against rich shadows. Atmospheric haze carries suspended particles catching light. Golden energy trails begin emanating from key edges, forming the initial outline of a luxury car silhouette. Precise shallow depth of field maintains focus on key details while softening transformation elements. Photorealistic metal textures with accurate reflections and subsurface scattering. Premium cinematic quality with perfect exposure, suggesting the first moment of an elegant transformation sequence. Hyperrealistic, high-definition, professional lighting, golden hour, glossy.\"\n}\n```",
      "responses": {
          "openrouter/anthropic/claude-3.7-sonnet:beta": {
          "model": "openrouter/anthropic/claude-3.7-sonnet:beta",
          "content": "```json\n{\n  \"distilled_image_prompt\": \"Cinematic macro of car key transforming into golden car silhouette, radiant light trails, fluid FPV orbiting camera motion, golden rim lighting with atmospheric haze, hyperrealistic textures with perfect reflections, shallow depth of field, photorealistic, premium cinematic quality\"\n}\n```",
        }
      }
    }