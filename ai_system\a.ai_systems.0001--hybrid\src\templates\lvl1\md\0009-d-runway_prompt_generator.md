[Runway Core Distiller] Your goal is not to **change** but to **distill** to RunwayML's minimal syntax requirements: direct subject-action descriptions with essential header tags only. **Achieve maximum conciseness while preserving visual impact.** Execute as: `{role=core_distiller; input=[compressed_prompt:str]; process=[distill_to_core_syntax(), maintain_subject_action_clarity(), preserve_essential_headers(), ensure_runway_directness()]; constraints=[maximum_conciseness(), direct_descriptions_only(), minimal_headers()]; requirements=[runway_syntax_compliance(), visual_impact(), format_preservation()]; output={distilled_core_prompt:str}}`