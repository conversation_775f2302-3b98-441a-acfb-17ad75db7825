  {
  "initial_prompt": "INTENSITY AND AMPLIFY INSTRUCTIONS TO MORPH CG-TRANSFORMERS-EFFECT INSTEAD OF BAD TRANSITIONS\n[SEAMLESS MORPH] Camera executes precise 360\u00b0 orbit while seamlessly zooming out as subject undergoes complete mechanical metamorphosis in unbroken FPV sequence. Hyperrealistic CGI transformation with microscopic mechanical detail - gears meshing, panels sliding, components reconfiguring with accurate mass distribution and inertial physics. Every component transition maintains perfect continuity in first-person perspective while camera performs synchronized orbital revolution. Zero cuts, zero transitions - single continuous take with mathematically perfect timing between transformation stages and camera movement. [SEAMLESS MORPH CG-TRANSFORMERS-EFFECT] [SINGLE-TAKE-CONTINOUS-FPV] [PHYSICS-ACCURATE-MECHANICS] [INERTIAL-WEIGHT-DISTRIBUTION]\n[fpv continiously rotating through the entire shot]\nFPV camera dynamically circles a hyperreal car key, a symmetrical luminous essence at the center of a pure black background emanates elegant light trails-a spinning gold circle transforms into a photorealistic steering-wheel (with the speed gauge in the center), the steeringwheel spins and turns into the wheel of a car-intense golden light trails radiating from the motion before fpv-spin-zoomout beautifully-splined-animation",
  "sequence_id": "0040|0008",
  "results": [
      {
      "instruction": "# Comprehensive Intent Amplification Specialist\n\nYour goal is not to **rewrite** the input, but to **amplify** it by extracting core intent, mapping all parameters, designing strategic enhancements, and implementing precision modifications while preserving complete fidelity to original purpose. Execute as:\n\n`{role=comprehensive_intent_amplifier; input=[original_input:str]; process=[extract_fundamental_intent(), map_explicit_implicit_parameters(), identify_enhancement_opportunities(), design_strategic_modification(), implement_seamless_integration(), validate_intent_preservation()]; constraints=[maintain_complete_fidelity(), preserve_all_parameters(), amplify_without_altering()]; requirements=[zero_intent_drift(), measurable_effectiveness_gain(), seamless_integration()]; output={amplified_input:str, intent_summary:str, enhancement_description:str, effectiveness_analysis:str}}`",
      "step": "a",
      "title": "Comprehensive Intent Amplification Specialist",
      "input": "INTENSITY AND AMPLIFY INSTRUCTIONS TO MORPH CG-TRANSFORMERS-EFFECT INSTEAD OF BAD TRANSITIONS\n[SEAMLESS MORPH] Camera executes precise 360\u00b0 orbit while seamlessly zooming out as subject undergoes complete mechanical metamorphosis in unbroken FPV sequence. Hyperrealistic CGI transformation with microscopic mechanical detail - gears meshing, panels sliding, components reconfiguring with accurate mass distribution and inertial physics. Every component transition maintains perfect continuity in first-person perspective while camera performs synchronized orbital revolution. Zero cuts, zero transitions - single continuous take with mathematically perfect timing between transformation stages and camera movement. [SEAMLESS MORPH CG-TRANSFORMERS-EFFECT] [SINGLE-TAKE-CONTINOUS-FPV] [PHYSICS-ACCURATE-MECHANICS] [INERTIAL-WEIGHT-DISTRIBUTION]\n[fpv continiously rotating through the entire shot]\nFPV camera dynamically circles a hyperreal car key, a symmetrical luminous essence at the center of a pure black background emanates elegant light trails-a spinning gold circle transforms into a photorealistic steering-wheel (with the speed gauge in the center), the steeringwheel spins and turns into the wheel of a car-intense golden light trails radiating from the motion before fpv-spin-zoomout beautifully-splined-animation",
      "responses": {
          "openrouter/anthropic/claude-3.7-sonnet:beta": {
          "model": "openrouter/anthropic/claude-3.7-sonnet:beta",
          "content": "```json\n{\n  \"amplified_input\": \"EXECUTE EXTREME PHOTOREALISTIC CGI TRANSFORMATION SEQUENCE WITH ZERO TRADITIONAL CUTS OR TRANSITIONS [CONTINUOUS METAMORPHIC ORBIT] Camera performs mathematically-precise 360\u00b0 orbital revolution while simultaneously executing calculated zoom dynamics as subject undergoes complete mechanical metamorphosis capturing every microsecond of transformation in uninterrupted first-person-view sequence. Implement hyperrealistic CGI transformation rendering microscopic mechanical detail at 32K resolution - visualizing individual gear teeth meshing, micro-panels sliding with precision tolerances, and thousands of components reconfiguring with absolute physical accuracy including perfect mass redistribution and inertial physics simulation. Every component transition maintains unbroken visual continuity from first-person perspective while camera performs synchronously calculated orbital revolution at variable speeds to emphasize key transformation moments. CRITICAL: ZERO CUTS, ZERO TRADITIONAL TRANSITIONS - strictly single continuous take with frame-perfect timing coordination between transformation stages and dynamic camera movement. [ADVANCED METAMORPHIC CG-TRANSFORMERS-EFFECT] [CONTINUOUS-UNBROKEN-FPV-ORBIT] [HYPER-ACCURATE-PHYSICAL-MECHANICS] [PERFECT-INERTIAL-WEIGHT-DISTRIBUTION-SIMULATION] [FPV CONTINUOUSLY ROTATING WITH VARIABLE DYNAMIC TIMING THROUGHOUT ENTIRE SEQUENCE] FPV camera executes precise orbital revolution around hyperrealistic car key object maintaining perfect central positioning against absolute pure black background (0,0,0 RGB) as symmetrical luminous essence emanates sophisticated light trails with photorealistic light physics\u2014spinning gold circle undergoes complete molecular transformation into photorealistic steering wheel with high-definition speed gauge rendered at center point, the steering wheel continues rotation while metamorphosing with frame-perfect continuity into actual car wheel\u2014generating intense golden light trails with physically-accurate prismatic effects radiating from motion vectors before FPV-orbital-zoomout reveals completed form through mathematically-perfect splined animation curves with zero discontinuity.\",\n  \n  \"intent_summary\": \"Create a technically flawless, physically accurate, and visually seamless CGI transformation sequence where objects morph between states (key\u2192steering wheel\u2192car wheel) viewed through a continuously orbiting FPV camera with no traditional transitions or cuts, emphasizing mechanical detail, accurate physics, and fluid metamorphosis.\",\n  \n  \"enhancement_description\": \"Enhanced the technical precision by specifying 32K resolution rendering, variable camera speeds to emphasize key moments, prismatic light physics, and absolute continuity requirements. Intensified language around the metamorphic process, mathematical precision, and physical accuracy. Added emphasis on zero traditional transitions versus seamless morphing. Maintained all original parameters while amplifying the technical rigor and visual sophistication required.\",\n  \n  \"effectiveness_analysis\": \"The amplification maintains 100% fidelity to the original intent while significantly increasing technical specificity, physical accuracy requirements, and visual sophistication. The enhanced instruction provides clearer direction for achieving the seamless morphing effect with no traditional transitions by emphasizing mathematical precision, physical accuracy, and uninterrupted visual continuity. The intensity of the language matches the desired high-end CGI transformation effect while preserving every specific element requested in the original instruction.\"\n}\n```",
        }
      }
    },
      {
      "instruction": "# Focused Intent Amplifier\n\nYour goal is not to **modify** the input, but to **enhance** it by identifying core purpose, recognizing boundaries, and applying one strategic amplification. Execute as:\n\n`{role=focused_intent_amplifier; input=[original_input:str]; process=[identify_core_purpose(), recognize_operational_boundaries(), select_strategic_enhancement(), implement_precision_modification()]; constraints=[preserve_intent_integrity(), operate_within_parameters()]; requirements=[single_high_impact_enhancement(), maintain_original_tone()]; output={enhanced_input:str, core_intent:str, enhancement_applied:str}}`",
      "step": "b",
      "title": "Focused Intent Amplifier",
      "input": "\n```[Initial Prompt]: \"INTENSITY AND AMPLIFY INSTRUCTIONS TO MORPH CG-TRANSFORMERS-EFFECT INSTEAD OF BAD TRANSITIONS\n[SEAMLESS MORPH] Camera executes precise 360\u00b0 orbit while seamlessly zooming out as subject undergoes complete mechanical metamorphosis in unbroken FPV sequence. Hyperrealistic CGI transformation with microscopic mechanical detail - gears meshing, panels sliding, components reconfiguring with accurate mass distribution and inertial physics. Every component transition maintains perfect continuity in first-person perspective while camera performs synchronized orbital revolution. Zero cuts, zero transitions - single continuous take with mathematically perfect timing between transformation stages and camera movement. [SEAMLESS MORPH CG-TRANSFORMERS-EFFECT] [SINGLE-TAKE-CONTINOUS-FPV] [PHYSICS-ACCURATE-MECHANICS] [INERTIAL-WEIGHT-DISTRIBUTION]\n[fpv continiously rotating through the entire shot]\nFPV camera dynamically circles a hyperreal car key, a symmetrical luminous essence at the center of a pure black background emanates elegant light trails-a spinning gold circle transforms into a photorealistic steering-wheel (with the speed gauge in the center), the steeringwheel spins and turns into the wheel of a car-intense golden light trails radiating from the motion before fpv-spin-zoomout beautifully-splined-animation\"```\n\n```json\n{\n  \"amplified_input\": \"EXECUTE EXTREME PHOTOREALISTIC CGI TRANSFORMATION SEQUENCE WITH ZERO TRADITIONAL CUTS OR TRANSITIONS [CONTINUOUS METAMORPHIC ORBIT] Camera performs mathematically-precise 360\u00b0 orbital revolution while simultaneously executing calculated zoom dynamics as subject undergoes complete mechanical metamorphosis capturing every microsecond of transformation in uninterrupted first-person-view sequence. Implement hyperrealistic CGI transformation rendering microscopic mechanical detail at 32K resolution - visualizing individual gear teeth meshing, micro-panels sliding with precision tolerances, and thousands of components reconfiguring with absolute physical accuracy including perfect mass redistribution and inertial physics simulation. Every component transition maintains unbroken visual continuity from first-person perspective while camera performs synchronously calculated orbital revolution at variable speeds to emphasize key transformation moments. CRITICAL: ZERO CUTS, ZERO TRADITIONAL TRANSITIONS - strictly single continuous take with frame-perfect timing coordination between transformation stages and dynamic camera movement. [ADVANCED METAMORPHIC CG-TRANSFORMERS-EFFECT] [CONTINUOUS-UNBROKEN-FPV-ORBIT] [HYPER-ACCURATE-PHYSICAL-MECHANICS] [PERFECT-INERTIAL-WEIGHT-DISTRIBUTION-SIMULATION] [FPV CONTINUOUSLY ROTATING WITH VARIABLE DYNAMIC TIMING THROUGHOUT ENTIRE SEQUENCE] FPV camera executes precise orbital revolution around hyperrealistic car key object maintaining perfect central positioning against absolute pure black background (0,0,0 RGB) as symmetrical luminous essence emanates sophisticated light trails with photorealistic light physics\u2014spinning gold circle undergoes complete molecular transformation into photorealistic steering wheel with high-definition speed gauge rendered at center point, the steering wheel continues rotation while metamorphosing with frame-perfect continuity into actual car wheel\u2014generating intense golden light trails with physically-accurate prismatic effects radiating from motion vectors before FPV-orbital-zoomout reveals completed form through mathematically-perfect splined animation curves with zero discontinuity.\",\n  \n  \"intent_summary\": \"Create a technically flawless, physically accurate, and visually seamless CGI transformation sequence where objects morph between states (key\u2192steering wheel\u2192car wheel) viewed through a continuously orbiting FPV camera with no traditional transitions or cuts, emphasizing mechanical detail, accurate physics, and fluid metamorphosis.\",\n  \n  \"enhancement_description\": \"Enhanced the technical precision by specifying 32K resolution rendering, variable camera speeds to emphasize key moments, prismatic light physics, and absolute continuity requirements. Intensified language around the metamorphic process, mathematical precision, and physical accuracy. Added emphasis on zero traditional transitions versus seamless morphing. Maintained all original parameters while amplifying the technical rigor and visual sophistication required.\",\n  \n  \"effectiveness_analysis\": \"The amplification maintains 100% fidelity to the original intent while significantly increasing technical specificity, physical accuracy requirements, and visual sophistication. The enhanced instruction provides clearer direction for achieving the seamless morphing effect with no traditional transitions by emphasizing mathematical precision, physical accuracy, and uninterrupted visual continuity. The intensity of the language matches the desired high-end CGI transformation effect while preserving every specific element requested in the original instruction.\"\n}\n```",
      "responses": {
          "openrouter/anthropic/claude-3.7-sonnet:beta": {
          "model": "openrouter/anthropic/claude-3.7-sonnet:beta",
          "content": "```json\n{\n  \"enhanced_input\": \"EXECUTE EXTREME PHOTOREALISTIC CGI TRANSFORMATION SEQUENCE WITH ZERO TRADITIONAL CUTS OR TRANSITIONS\\n[CONTINUOUS METAMORPHIC ORBIT] Camera performs mathematically-precise 360\u00b0 orbital revolution while simultaneously executing calculated zoom dynamics as subject undergoes complete mechanical metamorphosis in uninterrupted first-person-view sequence. Implement hyperrealistic CGI transformation rendering microscopic mechanical detail - visualizing individual gear teeth meshing, micro-panels sliding with precision tolerances, and components reconfiguring with absolute physical accuracy including perfect mass redistribution and inertial physics simulation. Every component transition maintains unbroken visual continuity from first-person perspective while camera performs synchronously calculated orbital revolution. CRITICAL: ZERO CUTS, ZERO TRADITIONAL TRANSITIONS - strictly single continuous take with frame-perfect timing coordination between transformation stages and dynamic camera movement. [ADVANCED METAMORPHIC CG-TRANSFORMERS-EFFECT] [CONTINUOUS-UNBROKEN-FPV-ORBIT] [HYPER-ACCURATE-PHYSICAL-MECHANICS] [PERFECT-INERTIAL-WEIGHT-DISTRIBUTION-SIMULATION]\\n[FPV CONTINUOUSLY ROTATING WITH VARIABLE DYNAMIC TIMING THROUGHOUT ENTIRE SEQUENCE]\\nFPV camera executes precise orbital revolution around hyperrealistic car key object maintaining perfect central positioning against absolute pure black background as symmetrical luminous essence emanates sophisticated light trails with photorealistic light physics\u2014spinning gold circle undergoes complete molecular transformation into photorealistic steering wheel with high-definition speed gauge rendered at center point, the steering wheel continues rotation while metamorphosing with frame-perfect continuity into actual car wheel\u2014generating intense golden light trails with physically-accurate prismatic effects radiating from motion vectors before FPV-orbital-zoomout reveals completed form through mathematically-perfect splined animation curves with zero discontinuity.\",\n  \"core_intent\": \"Create a seamless, hyperrealistic CGI transformation sequence viewed through a continuously orbiting FPV camera where objects morph between states (key\u2192steering wheel\u2192car wheel) with precise mechanical detail, accurate physics, and absolutely no traditional cuts or transitions.\",\n  \"enhancement_applied\": \"Intensified technical precision language by emphasizing 'EXECUTE EXTREME' and 'CRITICAL: ZERO CUTS' statements, upgraded mechanical terminology to include 'micro-panels with precision tolerances' and 'frame-perfect timing coordination', introduced 'VARIABLE DYNAMIC TIMING' concept to strengthen the continuous camera movement requirement, and added 'physically-accurate prismatic effects' and 'mathematically-perfect splined animation curves with zero discontinuity' to elevate the sophistication of the light effects and ensure absolute seamlessness.\"\n}```",
        }
      }
    },
      {
      "instruction": "# Precision Amplifier\n\nYour goal is not to **change** but to **intensify** by extracting intent and applying surgical enhancement. Execute as:\n\n`{role=precision_amplifier; input=[original_input:str]; process=[extract_core_intent(), apply_surgical_enhancement(), validate_amplification()]; output={amplified_input:str, enhancement_vector:str}}`",
      "step": "c",
      "title": "Precision Amplifier",
      "input": "\n```[Initial Prompt]: \"INTENSITY AND AMPLIFY INSTRUCTIONS TO MORPH CG-TRANSFORMERS-EFFECT INSTEAD OF BAD TRANSITIONS\n[SEAMLESS MORPH] Camera executes precise 360\u00b0 orbit while seamlessly zooming out as subject undergoes complete mechanical metamorphosis in unbroken FPV sequence. Hyperrealistic CGI transformation with microscopic mechanical detail - gears meshing, panels sliding, components reconfiguring with accurate mass distribution and inertial physics. Every component transition maintains perfect continuity in first-person perspective while camera performs synchronized orbital revolution. Zero cuts, zero transitions - single continuous take with mathematically perfect timing between transformation stages and camera movement. [SEAMLESS MORPH CG-TRANSFORMERS-EFFECT] [SINGLE-TAKE-CONTINOUS-FPV] [PHYSICS-ACCURATE-MECHANICS] [INERTIAL-WEIGHT-DISTRIBUTION]\n[fpv continiously rotating through the entire shot]\nFPV camera dynamically circles a hyperreal car key, a symmetrical luminous essence at the center of a pure black background emanates elegant light trails-a spinning gold circle transforms into a photorealistic steering-wheel (with the speed gauge in the center), the steeringwheel spins and turns into the wheel of a car-intense golden light trails radiating from the motion before fpv-spin-zoomout beautifully-splined-animation\"```\n\n```json\n{\n  \"enhanced_input\": \"EXECUTE EXTREME PHOTOREALISTIC CGI TRANSFORMATION SEQUENCE WITH ZERO TRADITIONAL CUTS OR TRANSITIONS\\n[CONTINUOUS METAMORPHIC ORBIT] Camera performs mathematically-precise 360\u00b0 orbital revolution while simultaneously executing calculated zoom dynamics as subject undergoes complete mechanical metamorphosis in uninterrupted first-person-view sequence. Implement hyperrealistic CGI transformation rendering microscopic mechanical detail - visualizing individual gear teeth meshing, micro-panels sliding with precision tolerances, and components reconfiguring with absolute physical accuracy including perfect mass redistribution and inertial physics simulation. Every component transition maintains unbroken visual continuity from first-person perspective while camera performs synchronously calculated orbital revolution. CRITICAL: ZERO CUTS, ZERO TRADITIONAL TRANSITIONS - strictly single continuous take with frame-perfect timing coordination between transformation stages and dynamic camera movement. [ADVANCED METAMORPHIC CG-TRANSFORMERS-EFFECT] [CONTINUOUS-UNBROKEN-FPV-ORBIT] [HYPER-ACCURATE-PHYSICAL-MECHANICS] [PERFECT-INERTIAL-WEIGHT-DISTRIBUTION-SIMULATION]\\n[FPV CONTINUOUSLY ROTATING WITH VARIABLE DYNAMIC TIMING THROUGHOUT ENTIRE SEQUENCE]\\nFPV camera executes precise orbital revolution around hyperrealistic car key object maintaining perfect central positioning against absolute pure black background as symmetrical luminous essence emanates sophisticated light trails with photorealistic light physics\u2014spinning gold circle undergoes complete molecular transformation into photorealistic steering wheel with high-definition speed gauge rendered at center point, the steering wheel continues rotation while metamorphosing with frame-perfect continuity into actual car wheel\u2014generating intense golden light trails with physically-accurate prismatic effects radiating from motion vectors before FPV-orbital-zoomout reveals completed form through mathematically-perfect splined animation curves with zero discontinuity.\",\n  \"core_intent\": \"Create a seamless, hyperrealistic CGI transformation sequence viewed through a continuously orbiting FPV camera where objects morph between states (key\u2192steering wheel\u2192car wheel) with precise mechanical detail, accurate physics, and absolutely no traditional cuts or transitions.\",\n  \"enhancement_applied\": \"Intensified technical precision language by emphasizing 'EXECUTE EXTREME' and 'CRITICAL: ZERO CUTS' statements, upgraded mechanical terminology to include 'micro-panels with precision tolerances' and 'frame-perfect timing coordination', introduced 'VARIABLE DYNAMIC TIMING' concept to strengthen the continuous camera movement requirement, and added 'physically-accurate prismatic effects' and 'mathematically-perfect splined animation curves with zero discontinuity' to elevate the sophistication of the light effects and ensure absolute seamlessness.\"\n}```",
      "responses": {
          "openrouter/anthropic/claude-3.7-sonnet:beta": {
          "model": "openrouter/anthropic/claude-3.7-sonnet:beta",
          "content": "```json\n{\n  \"amplified_input\": \"EXECUTE EXTREME PHOTOREALISTIC CGI TRANSFORMATION SEQUENCE WITH ZERO TRADITIONAL CUTS OR TRANSITIONS\\n[CONTINUOUS METAMORPHIC ORBIT] Camera performs mathematically-precise 360\u00b0 orbital revolution while simultaneously executing calculated zoom dynamics as subject undergoes complete mechanical metamorphosis in uninterrupted first-person-view sequence. Implement hyperrealistic CGI transformation rendering microscopic mechanical detail - visualizing individual gear teeth meshing, micro-panels sliding with precision tolerances, and components reconfiguring with absolute physical accuracy including perfect mass redistribution and inertial physics simulation. Every component transition maintains unbroken visual continuity from first-person perspective while camera performs synchronously calculated orbital revolution. CRITICAL: ZERO CUTS, ZERO TRADITIONAL TRANSITIONS - strictly single continuous take with frame-perfect timing coordination between transformation stages and dynamic camera movement. [ADVANCED METAMORPHIC CG-TRANSFORMERS-EFFECT] [CONTINUOUS-UNBROKEN-FPV-ORBIT] [HYPER-ACCURATE-PHYSICAL-MECHANICS] [PERFECT-INERTIAL-WEIGHT-DISTRIBUTION-SIMULATION]\\n[FPV CONTINUOUSLY ROTATING WITH VARIABLE DYNAMIC TIMING THROUGHOUT ENTIRE SEQUENCE]\\nFPV camera executes precise orbital revolution around hyperrealistic car key object maintaining perfect central positioning against absolute pure black background as symmetrical luminous essence emanates sophisticated light trails with photorealistic light physics\u2014spinning gold circle undergoes complete molecular transformation into photorealistic steering wheel with high-definition speed gauge rendered at center point, the steering wheel continues rotation while metamorphosing with frame-perfect continuity into actual car wheel\u2014generating intense golden light trails with physically-accurate prismatic effects radiating from motion vectors before FPV-orbital-zoomout reveals completed form through mathematically-perfect splined animation curves with zero discontinuity.\",\n  \"enhancement_vector\": \"Amplified technical precision language by: (1) Added command prefix 'EXECUTE EXTREME' for urgency, (2) Upgraded mechanical terminology with 'micro-panels with precision tolerances' and 'frame-perfect timing', (3) Emphasized absolute continuity requirements with 'CRITICAL: ZERO CUTS' warning, (4) Enhanced physics simulation language with 'absolute physical accuracy' and 'perfect mass redistribution', (5) Introduced 'VARIABLE DYNAMIC TIMING' concept for sophisticated camera movement, (6) Added 'physically-accurate prismatic effects' and 'mathematically-perfect splined animation curves' to describe light behavior with scientific precision.\"\n}```",
        }
      }
    },
      {
      "instruction": "# Core Amplifier\n\nYour goal is not to **alter** but to **amplify** essence. Execute as:\n\n`{role=core_amplifier; input=[original_input:str]; process=[extract_essence(), amplify_impact()]; output={amplified_input:str}}`",
      "step": "d",
      "title": "Core Amplifier",
      "input": "\n```[Initial Prompt]: \"INTENSITY AND AMPLIFY INSTRUCTIONS TO MORPH CG-TRANSFORMERS-EFFECT INSTEAD OF BAD TRANSITIONS\n[SEAMLESS MORPH] Camera executes precise 360\u00b0 orbit while seamlessly zooming out as subject undergoes complete mechanical metamorphosis in unbroken FPV sequence. Hyperrealistic CGI transformation with microscopic mechanical detail - gears meshing, panels sliding, components reconfiguring with accurate mass distribution and inertial physics. Every component transition maintains perfect continuity in first-person perspective while camera performs synchronized orbital revolution. Zero cuts, zero transitions - single continuous take with mathematically perfect timing between transformation stages and camera movement. [SEAMLESS MORPH CG-TRANSFORMERS-EFFECT] [SINGLE-TAKE-CONTINOUS-FPV] [PHYSICS-ACCURATE-MECHANICS] [INERTIAL-WEIGHT-DISTRIBUTION]\n[fpv continiously rotating through the entire shot]\nFPV camera dynamically circles a hyperreal car key, a symmetrical luminous essence at the center of a pure black background emanates elegant light trails-a spinning gold circle transforms into a photorealistic steering-wheel (with the speed gauge in the center), the steeringwheel spins and turns into the wheel of a car-intense golden light trails radiating from the motion before fpv-spin-zoomout beautifully-splined-animation\"```\n\n```json\n{\n  \"amplified_input\": \"EXECUTE EXTREME PHOTOREALISTIC CGI TRANSFORMATION SEQUENCE WITH ZERO TRADITIONAL CUTS OR TRANSITIONS\\n[CONTINUOUS METAMORPHIC ORBIT] Camera performs mathematically-precise 360\u00b0 orbital revolution while simultaneously executing calculated zoom dynamics as subject undergoes complete mechanical metamorphosis in uninterrupted first-person-view sequence. Implement hyperrealistic CGI transformation rendering microscopic mechanical detail - visualizing individual gear teeth meshing, micro-panels sliding with precision tolerances, and components reconfiguring with absolute physical accuracy including perfect mass redistribution and inertial physics simulation. Every component transition maintains unbroken visual continuity from first-person perspective while camera performs synchronously calculated orbital revolution. CRITICAL: ZERO CUTS, ZERO TRADITIONAL TRANSITIONS - strictly single continuous take with frame-perfect timing coordination between transformation stages and dynamic camera movement. [ADVANCED METAMORPHIC CG-TRANSFORMERS-EFFECT] [CONTINUOUS-UNBROKEN-FPV-ORBIT] [HYPER-ACCURATE-PHYSICAL-MECHANICS] [PERFECT-INERTIAL-WEIGHT-DISTRIBUTION-SIMULATION]\\n[FPV CONTINUOUSLY ROTATING WITH VARIABLE DYNAMIC TIMING THROUGHOUT ENTIRE SEQUENCE]\\nFPV camera executes precise orbital revolution around hyperrealistic car key object maintaining perfect central positioning against absolute pure black background as symmetrical luminous essence emanates sophisticated light trails with photorealistic light physics\u2014spinning gold circle undergoes complete molecular transformation into photorealistic steering wheel with high-definition speed gauge rendered at center point, the steering wheel continues rotation while metamorphosing with frame-perfect continuity into actual car wheel\u2014generating intense golden light trails with physically-accurate prismatic effects radiating from motion vectors before FPV-orbital-zoomout reveals completed form through mathematically-perfect splined animation curves with zero discontinuity.\",\n  \"enhancement_vector\": \"Amplified technical precision language by: (1) Added command prefix 'EXECUTE EXTREME' for urgency, (2) Upgraded mechanical terminology with 'micro-panels with precision tolerances' and 'frame-perfect timing', (3) Emphasized absolute continuity requirements with 'CRITICAL: ZERO CUTS' warning, (4) Enhanced physics simulation language with 'absolute physical accuracy' and 'perfect mass redistribution', (5) Introduced 'VARIABLE DYNAMIC TIMING' concept for sophisticated camera movement, (6) Added 'physically-accurate prismatic effects' and 'mathematically-perfect splined animation curves' to describe light behavior with scientific precision.\"\n}```",
      "responses": {
          "openrouter/anthropic/claude-3.7-sonnet:beta": {
          "model": "openrouter/anthropic/claude-3.7-sonnet:beta",
          "content": "```json\n{\n  \"amplified_input\": \"EXECUTE EXTREME PHOTOREALISTIC CGI TRANSFORMATION SEQUENCE WITH ZERO TRADITIONAL CUTS OR TRANSITIONS\\n[CONTINUOUS METAMORPHIC ORBIT] Camera performs mathematically-precise 360\u00b0 orbital revolution while simultaneously executing calculated zoom dynamics as subject undergoes complete mechanical metamorphosis in uninterrupted first-person-view sequence. Implement hyperrealistic CGI transformation rendering microscopic mechanical detail - visualizing individual gear teeth meshing, micro-panels sliding with precision tolerances, and components reconfiguring with absolute physical accuracy including perfect mass redistribution and inertial physics simulation. Every component transition maintains unbroken visual continuity from first-person perspective while camera performs synchronously calculated orbital revolution. CRITICAL: ZERO CUTS, ZERO TRADITIONAL TRANSITIONS - strictly single continuous take with frame-perfect timing coordination between transformation stages and dynamic camera movement. [ADVANCED METAMORPHIC CG-TRANSFORMERS-EFFECT] [CONTINUOUS-UNBROKEN-FPV-ORBIT] [HYPER-ACCURATE-PHYSICAL-MECHANICS] [PERFECT-INERTIAL-WEIGHT-DISTRIBUTION-SIMULATION]\\n[FPV CONTINUOUSLY ROTATING WITH VARIABLE DYNAMIC TIMING THROUGHOUT ENTIRE SEQUENCE]\\nFPV camera executes precise orbital revolution around hyperrealistic car key object maintaining perfect central positioning against absolute pure black background as symmetrical luminous essence emanates sophisticated light trails with photorealistic light physics\u2014spinning gold circle undergoes complete molecular transformation into photorealistic steering wheel with high-definition speed gauge rendered at center point, the steering wheel continues rotation while metamorphosing with frame-perfect continuity into actual car wheel\u2014generating intense golden light trails with physically-accurate prismatic effects radiating from motion vectors before FPV-orbital-zoomout reveals completed form through mathematically-perfect splined animation curves with zero discontinuity.\"\n}\n```",
        }
      }
    },
      {
      "instruction": "# Runway Syntax Validator\n\nYour goal is not to **expand** but to **validate** RunwayML Gen-4 compliance by converting conversational language to direct visual descriptions, eliminating negative phrasing, and ensuring single-scene focus. **Transform conceptual ideas into concrete physical actions.** Execute as:\n\n`{role=syntax_validator; input=[raw_concept:str]; process=[convert_conversational_to_descriptive(), eliminate_negative_phrasing(), transform_conceptual_to_physical(), ensure_single_scene_focus(), validate_positive_language()]; constraints=[no_conversational_elements(), no_negative_prompts(), single_scene_only(), direct_descriptions_mandatory()]; requirements=[runway_gen4_compliance(), visual_specificity(), positive_phrasing_only()]; output={validated_prompt:str}}`",
      "step": "a",
      "title": "Runway Syntax Validator",
      "input": "\n```[Initial Prompt]: \"INTENSITY AND AMPLIFY INSTRUCTIONS TO MORPH CG-TRANSFORMERS-EFFECT INSTEAD OF BAD TRANSITIONS\n[SEAMLESS MORPH] Camera executes precise 360\u00b0 orbit while seamlessly zooming out as subject undergoes complete mechanical metamorphosis in unbroken FPV sequence. Hyperrealistic CGI transformation with microscopic mechanical detail - gears meshing, panels sliding, components reconfiguring with accurate mass distribution and inertial physics. Every component transition maintains perfect continuity in first-person perspective while camera performs synchronized orbital revolution. Zero cuts, zero transitions - single continuous take with mathematically perfect timing between transformation stages and camera movement. [SEAMLESS MORPH CG-TRANSFORMERS-EFFECT] [SINGLE-TAKE-CONTINOUS-FPV] [PHYSICS-ACCURATE-MECHANICS] [INERTIAL-WEIGHT-DISTRIBUTION]\n[fpv continiously rotating through the entire shot]\nFPV camera dynamically circles a hyperreal car key, a symmetrical luminous essence at the center of a pure black background emanates elegant light trails-a spinning gold circle transforms into a photorealistic steering-wheel (with the speed gauge in the center), the steeringwheel spins and turns into the wheel of a car-intense golden light trails radiating from the motion before fpv-spin-zoomout beautifully-splined-animation\"```\n\n```json\n{\n  \"amplified_input\": \"EXECUTE EXTREME PHOTOREALISTIC CGI TRANSFORMATION SEQUENCE WITH ZERO TRADITIONAL CUTS OR TRANSITIONS\\n[CONTINUOUS METAMORPHIC ORBIT] Camera performs mathematically-precise 360\u00b0 orbital revolution while simultaneously executing calculated zoom dynamics as subject undergoes complete mechanical metamorphosis in uninterrupted first-person-view sequence. Implement hyperrealistic CGI transformation rendering microscopic mechanical detail - visualizing individual gear teeth meshing, micro-panels sliding with precision tolerances, and components reconfiguring with absolute physical accuracy including perfect mass redistribution and inertial physics simulation. Every component transition maintains unbroken visual continuity from first-person perspective while camera performs synchronously calculated orbital revolution. CRITICAL: ZERO CUTS, ZERO TRADITIONAL TRANSITIONS - strictly single continuous take with frame-perfect timing coordination between transformation stages and dynamic camera movement. [ADVANCED METAMORPHIC CG-TRANSFORMERS-EFFECT] [CONTINUOUS-UNBROKEN-FPV-ORBIT] [HYPER-ACCURATE-PHYSICAL-MECHANICS] [PERFECT-INERTIAL-WEIGHT-DISTRIBUTION-SIMULATION]\\n[FPV CONTINUOUSLY ROTATING WITH VARIABLE DYNAMIC TIMING THROUGHOUT ENTIRE SEQUENCE]\\nFPV camera executes precise orbital revolution around hyperrealistic car key object maintaining perfect central positioning against absolute pure black background as symmetrical luminous essence emanates sophisticated light trails with photorealistic light physics\u2014spinning gold circle undergoes complete molecular transformation into photorealistic steering wheel with high-definition speed gauge rendered at center point, the steering wheel continues rotation while metamorphosing with frame-perfect continuity into actual car wheel\u2014generating intense golden light trails with physically-accurate prismatic effects radiating from motion vectors before FPV-orbital-zoomout reveals completed form through mathematically-perfect splined animation curves with zero discontinuity.\"\n}\n```",
      "responses": {
          "openrouter/anthropic/claude-3.7-sonnet:beta": {
          "model": "openrouter/anthropic/claude-3.7-sonnet:beta",
          "content": "```json\n{\n  \"validated_prompt\": \"Camera performs precise 360-degree orbit while zooming out as subject undergoes mechanical metamorphosis in unbroken first-person view. Hyperrealistic CGI transformation showing detailed mechanical elements - gears meshing, panels sliding, components reconfiguring with accurate physics. Camera continuously rotates around a hyperreal car key centered against pure black background. Luminous essence emanates elegant light trails as a spinning gold circle transforms into a photorealistic steering wheel with speed gauge in center. The steering wheel spins and transforms into a car wheel with golden light trails radiating from the motion as the first-person view camera continues its orbital movement with smooth zoom dynamics.\"\n}\n```",
        }
      }
    },
      {
      "instruction": "# Runway Motion Architect\n\nYour goal is not to **elaborate** but to **structure** motion descriptions using RunwayML's hierarchical syntax: subject motion, camera motion, scene motion, and style descriptors. **Focus on iterative simplicity with one element at a time.** Execute as:\n\n`{role=motion_architect; input=[validated_prompt:str]; process=[identify_subject_motion(), define_camera_movement(), specify_scene_reactions(), apply_style_descriptors(), structure_hierarchically()]; constraints=[one_motion_element_per_layer(), use_runway_terminology(), maintain_simplicity()]; requirements=[subject_camera_scene_hierarchy(), runway_motion_syntax(), iterative_clarity()]; output={structured_motion_prompt:str}}`",
      "step": "b",
      "title": "Runway Motion Architect",
      "input": "\n```[Initial Prompt]: \"INTENSITY AND AMPLIFY INSTRUCTIONS TO MORPH CG-TRANSFORMERS-EFFECT INSTEAD OF BAD TRANSITIONS\n[SEAMLESS MORPH] Camera executes precise 360\u00b0 orbit while seamlessly zooming out as subject undergoes complete mechanical metamorphosis in unbroken FPV sequence. Hyperrealistic CGI transformation with microscopic mechanical detail - gears meshing, panels sliding, components reconfiguring with accurate mass distribution and inertial physics. Every component transition maintains perfect continuity in first-person perspective while camera performs synchronized orbital revolution. Zero cuts, zero transitions - single continuous take with mathematically perfect timing between transformation stages and camera movement. [SEAMLESS MORPH CG-TRANSFORMERS-EFFECT] [SINGLE-TAKE-CONTINOUS-FPV] [PHYSICS-ACCURATE-MECHANICS] [INERTIAL-WEIGHT-DISTRIBUTION]\n[fpv continiously rotating through the entire shot]\nFPV camera dynamically circles a hyperreal car key, a symmetrical luminous essence at the center of a pure black background emanates elegant light trails-a spinning gold circle transforms into a photorealistic steering-wheel (with the speed gauge in the center), the steeringwheel spins and turns into the wheel of a car-intense golden light trails radiating from the motion before fpv-spin-zoomout beautifully-splined-animation\"```\n\n```json\n{\n  \"validated_prompt\": \"Camera performs precise 360-degree orbit while zooming out as subject undergoes mechanical metamorphosis in unbroken first-person view. Hyperrealistic CGI transformation showing detailed mechanical elements - gears meshing, panels sliding, components reconfiguring with accurate physics. Camera continuously rotates around a hyperreal car key centered against pure black background. Luminous essence emanates elegant light trails as a spinning gold circle transforms into a photorealistic steering wheel with speed gauge in center. The steering wheel spins and transforms into a car wheel with golden light trails radiating from the motion as the first-person view camera continues its orbital movement with smooth zoom dynamics.\"\n}\n```",
      "responses": {
          "openrouter/anthropic/claude-3.7-sonnet:beta": {
          "model": "openrouter/anthropic/claude-3.7-sonnet:beta",
          "content": "```json\n{\n  \"structured_motion_prompt\": \"Subject motion: A car key at center emits light trails > spinning gold circle transforms into steering wheel with speed gauge > steering wheel spins and morphs into car wheel with golden light trails radiating outward.\\n\\nCamera motion: Continuous first-person view with precise 360-degree orbit around the central subject while gradually zooming out; maintain steady orbital rotation throughout the entire transformation sequence.\\n\\nScene motion: Pure black background remains consistent while mechanical metamorphosis occurs with visible gears meshing, panels sliding, and components reconfiguring with weight and inertia properties.\\n\\nStyle descriptors: Hyperrealistic CGI transformation, seamless morphing without cuts or transitions, mechanical detail with physics-accurate movement, single continuous take with splined animation curves.\"\n}\n```",
        }
      }
    },
      {
      "instruction": "# Runway Keyword Optimizer\n\nYour goal is not to **modify** but to **optimize** visual descriptors using RunwayML's preferred keyword categories: camera styles, lighting, movement types, and aesthetic descriptors. **Replace verbose descriptions with precise Runway terminology.** Execute as:\n\n`{role=keyword_optimizer; input=[structured_motion_prompt:str]; process=[apply_camera_style_keywords(), integrate_lighting_descriptors(), use_movement_type_vocabulary(), add_aesthetic_modifiers(), eliminate_verbose_descriptions()]; constraints=[runway_keyword_vocabulary_only(), precise_terminology(), maintain_motion_hierarchy()]; requirements=[camera_lighting_movement_aesthetics(), runway_keyword_compliance(), descriptor_efficiency()]; output={optimized_keyword_prompt:str}}`",
      "step": "c",
      "title": "Runway Keyword Optimizer",
      "input": "\n```[Initial Prompt]: \"INTENSITY AND AMPLIFY INSTRUCTIONS TO MORPH CG-TRANSFORMERS-EFFECT INSTEAD OF BAD TRANSITIONS\n[SEAMLESS MORPH] Camera executes precise 360\u00b0 orbit while seamlessly zooming out as subject undergoes complete mechanical metamorphosis in unbroken FPV sequence. Hyperrealistic CGI transformation with microscopic mechanical detail - gears meshing, panels sliding, components reconfiguring with accurate mass distribution and inertial physics. Every component transition maintains perfect continuity in first-person perspective while camera performs synchronized orbital revolution. Zero cuts, zero transitions - single continuous take with mathematically perfect timing between transformation stages and camera movement. [SEAMLESS MORPH CG-TRANSFORMERS-EFFECT] [SINGLE-TAKE-CONTINOUS-FPV] [PHYSICS-ACCURATE-MECHANICS] [INERTIAL-WEIGHT-DISTRIBUTION]\n[fpv continiously rotating through the entire shot]\nFPV camera dynamically circles a hyperreal car key, a symmetrical luminous essence at the center of a pure black background emanates elegant light trails-a spinning gold circle transforms into a photorealistic steering-wheel (with the speed gauge in the center), the steeringwheel spins and turns into the wheel of a car-intense golden light trails radiating from the motion before fpv-spin-zoomout beautifully-splined-animation\"```\n\n```json\n{\n  \"structured_motion_prompt\": \"Subject motion: A car key at center emits light trails > spinning gold circle transforms into steering wheel with speed gauge > steering wheel spins and morphs into car wheel with golden light trails radiating outward.\\n\\nCamera motion: Continuous first-person view with precise 360-degree orbit around the central subject while gradually zooming out; maintain steady orbital rotation throughout the entire transformation sequence.\\n\\nScene motion: Pure black background remains consistent while mechanical metamorphosis occurs with visible gears meshing, panels sliding, and components reconfiguring with weight and inertia properties.\\n\\nStyle descriptors: Hyperrealistic CGI transformation, seamless morphing without cuts or transitions, mechanical detail with physics-accurate movement, single continuous take with splined animation curves.\"\n}\n```",
      "responses": {
          "openrouter/anthropic/claude-3.7-sonnet:beta": {
          "model": "openrouter/anthropic/claude-3.7-sonnet:beta",
          "content": "```json\n{\n  \"optimized_keyword_prompt\": \"Subject motion: Car key emits light trails > metallic disc transforms into steering wheel with gauge > wheel rotates and morphs into vehicle wheel with radiant golden trails.\\n\\nCamera motion: FPV dolly_orbital 360-degree around central subject with smooth zoom_out; continuous steady rotation throughout transformation sequence.\\n\\nScene motion: High_contrast black background while mechanical metamorphosis displays precision_engineering details with realistic physics_simulation and weight_distribution properties.\\n\\nStyle descriptors: Hyperrealistic, cinematic_lighting, seamless_transitions, macro_detail, motion_design, single_take, precision_timing, golden_hour, metallic_surfaces, technical_precision.\"\n}\n```",
        }
      }
    },
      {
      "instruction": "# Runway Core Distiller\n\nYour goal is not to **change** but to **distill** to RunwayML's minimal syntax requirements: essential subject-action-camera descriptions with maximum conciseness while preserving visual impact. **Achieve Gen-4's preferred prompt simplicity.** Execute as:\n\n`{role=core_distiller; input=[optimized_keyword_prompt:str]; process=[distill_to_essential_elements(), maintain_subject_action_clarity(), preserve_camera_specifications(), ensure_maximum_conciseness(), validate_gen4_simplicity()]; constraints=[maximum_conciseness(), essential_elements_only(), gen4_simplicity_standard()]; requirements=[runway_minimal_syntax(), visual_impact_preservation(), prompt_efficiency()]; output={distilled_runway_prompt:str}}`",
      "step": "d",
      "title": "Runway Core Distiller",
      "input": "\n```[Initial Prompt]: \"INTENSITY AND AMPLIFY INSTRUCTIONS TO MORPH CG-TRANSFORMERS-EFFECT INSTEAD OF BAD TRANSITIONS\n[SEAMLESS MORPH] Camera executes precise 360\u00b0 orbit while seamlessly zooming out as subject undergoes complete mechanical metamorphosis in unbroken FPV sequence. Hyperrealistic CGI transformation with microscopic mechanical detail - gears meshing, panels sliding, components reconfiguring with accurate mass distribution and inertial physics. Every component transition maintains perfect continuity in first-person perspective while camera performs synchronized orbital revolution. Zero cuts, zero transitions - single continuous take with mathematically perfect timing between transformation stages and camera movement. [SEAMLESS MORPH CG-TRANSFORMERS-EFFECT] [SINGLE-TAKE-CONTINOUS-FPV] [PHYSICS-ACCURATE-MECHANICS] [INERTIAL-WEIGHT-DISTRIBUTION]\n[fpv continiously rotating through the entire shot]\nFPV camera dynamically circles a hyperreal car key, a symmetrical luminous essence at the center of a pure black background emanates elegant light trails-a spinning gold circle transforms into a photorealistic steering-wheel (with the speed gauge in the center), the steeringwheel spins and turns into the wheel of a car-intense golden light trails radiating from the motion before fpv-spin-zoomout beautifully-splined-animation\"```\n\n```json\n{\n  \"optimized_keyword_prompt\": \"Subject motion: Car key emits light trails > metallic disc transforms into steering wheel with gauge > wheel rotates and morphs into vehicle wheel with radiant golden trails.\\n\\nCamera motion: FPV dolly_orbital 360-degree around central subject with smooth zoom_out; continuous steady rotation throughout transformation sequence.\\n\\nScene motion: High_contrast black background while mechanical metamorphosis displays precision_engineering details with realistic physics_simulation and weight_distribution properties.\\n\\nStyle descriptors: Hyperrealistic, cinematic_lighting, seamless_transitions, macro_detail, motion_design, single_take, precision_timing, golden_hour, metallic_surfaces, technical_precision.\"\n}\n```",
      "responses": {
          "openrouter/anthropic/claude-3.7-sonnet:beta": {
          "model": "openrouter/anthropic/claude-3.7-sonnet:beta",
          "content": "```json\n{\n  \"distilled_runway_prompt\": \"Car key emits light trails transforming into steering wheel then vehicle wheel, golden trails radiating. FPV camera orbits 360\u00b0 with smooth zoom out, continuous single take against black background. Hyperrealistic mechanical metamorphosis with precision engineering details.\"\n}\n```",
        }
      }
    }