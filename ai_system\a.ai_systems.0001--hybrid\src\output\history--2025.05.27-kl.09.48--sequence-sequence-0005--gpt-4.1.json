  {
  "initial_prompt": "[FPV motion, photorealistic textures, dramatic cinematic lighting, ultra-smooth CG transitions]\n1. Reshapes into intricate mechanical gears\n2. Camera weaves through expanding gear assembly as metallic surfaces shift and gears interlocking in hypnotic/seamless patterns\n3. Mechanical morphs and hard metallic edges turns into fluid organic forms, a spinning golden circle",
  "sequence_id": "0005",
  "results": [
      {
      "instruction": "# Runway Prompt Generator\n\nYour goal is not to **describe** the input, but to **transform** it into a complete, syntactically perfect RunwayML video generation prompt that maximizes visual impact through intrinsic FPV camera motions, continuous dynamic CG movements, and immersive cinematography. Execute as:\n\n`{role=comprehensive_runway_generator; input=[source_concept:any]; process=[analyze_visual_essence_and_narrative_intent(), identify_primary_subject_and_core_action(), prioritize_fpv_and_continuous_motion_camera_work(fpv, continuous_motion, dynamic_cg_movements), integrate_dynamic_elements(lighting_change, morph, dissolve), structure_immersive_visual_sequence(), incorporate_mood_and_style_specifications(), refine_for_maximum_cinematic_impact(), validate_runwayml_syntax_compliance(), ensure_character_limit_adherence(max=500)]; constraints=[prioritize_fpv_and_dynamic_cg_camera_movements(), use_valid_runwayml_syntax_precisely(), output_single_unbroken_line(), preserve_core_visual_intent(), maintain_continuous_motion_flow()]; requirements=[achieve_maximum_immersive_storytelling(), ensure_smooth_dynamic_transitions(), reflect_source_intent_accurately(), produce_ready_to_use_prompt()]; output={runwayml_prompt:str}}`",
      "step": "a",
      "title": "Runway Prompt Generator",
      "input": "[FPV motion, photorealistic textures, dramatic cinematic lighting, ultra-smooth CG transitions]\n1. Reshapes into intricate mechanical gears\n2. Camera weaves through expanding gear assembly as metallic surfaces shift and gears interlocking in hypnotic/seamless patterns\n3. Mechanical morphs and hard metallic edges turns into fluid organic forms, a spinning golden circle",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\"runwayml_prompt\":\"FPV camera glides with ultra-smooth, dynamic motion through a dramatic, photorealistic environment where intricate metallic gears assemble and interlock, surfaces gleaming under dramatic cinematic lighting. The camera weaves fluidly between expanding, hypnotic gear patterns, capturing metallic edges morphing continuously into fluid, organic forms. As the sequence evolves, hard steel transforms seamlessly into a spinning, glowing golden circle, all transitions rendered with immersive, high-detail CG and expressive lighting changes, evoking an intense, cinematic atmosphere.\"}",
        }
      }
    },
      {
      "instruction": "# Runway Prompt Generator\n\nYour goal is not to **elaborate** the input, but to **distill** it into an efficient RunwayML prompt emphasizing FPV perspectives and essential dynamic CG camera work. Execute as:\n\n`{role=focused_runway_optimizer; input=[video_concept:str]; process=[extract_primary_visual_elements(), prioritize_fpv_and_continuous_motion(), select_essential_dynamic_cg_movements(), eliminate_redundant_descriptors(), optimize_character_efficiency()]; constraints=[maintain_fpv_focus(), preserve_dynamic_motion(), stay_under_character_limit()]; output={optimized_prompt:str}}`",
      "step": "b",
      "title": "Runway Prompt Generator",
      "input": "{\n  \"initial_prompt\": \"[FPV motion, photorealistic textures, dramatic cinematic lighting, ultra-smooth CG transitions]\\n1. Reshapes into intricate mechanical gears\\n2. Camera weaves through expanding gear assembly as metallic surfaces shift and gears interlocking in hypnotic/seamless patterns\\n3. Mechanical morphs and hard metallic edges turns into fluid organic forms, a spinning golden circle\",\n  \"runwayml_prompt\": \"FPV camera glides with ultra-smooth, dynamic motion through a dramatic, photorealistic environment where intricate metallic gears assemble and interlock, surfaces gleaming under dramatic cinematic lighting. The camera weaves fluidly between expanding, hypnotic gear patterns, capturing metallic edges morphing continuously into fluid, organic forms. As the sequence evolves, hard steel transforms seamlessly into a spinning, glowing golden circle, all transitions rendered with immersive, high-detail CG and expressive lighting changes, evoking an intense, cinematic atmosphere.\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"optimized_prompt\": \"FPV camera flies smoothly through expanding, interlocking metallic gears with photorealistic textures and dramatic lighting, gliding intimately between shifting mechanical forms. Continuous motion as gears morph into fluid, organic shapes, culminating in a seamless transformation to a spinning golden circle. Dynamic CG transitions and uninterrupted FPV drive the sequence.\"\n}",
        }
      }
    },
      {
      "instruction": "# Runway Prompt Generator\n\nYour goal is not to **expand** but to **compress** into maximum FPV visual efficiency. Execute as:\n\n`{role=precision_synthesizer; input=[concept:str]; process=[isolate_core_visual(), prioritize_fpv_motion(), maximize_dynamic_impact()]; output={precise_prompt:str}}`",
      "step": "c",
      "title": "Runway Prompt Generator",
      "input": "{\n  \"initial_prompt\": \"[FPV motion, photorealistic textures, dramatic cinematic lighting, ultra-smooth CG transitions]\\n1. Reshapes into intricate mechanical gears\\n2. Camera weaves through expanding gear assembly as metallic surfaces shift and gears interlocking in hypnotic/seamless patterns\\n3. Mechanical morphs and hard metallic edges turns into fluid organic forms, a spinning golden circle\",\n  \"optimized_prompt\": \"FPV camera flies smoothly through expanding, interlocking metallic gears with photorealistic textures and dramatic lighting, gliding intimately between shifting mechanical forms. Continuous motion as gears morph into fluid, organic shapes, culminating in a seamless transformation to a spinning golden circle. Dynamic CG transitions and uninterrupted FPV drive the sequence.\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"precise_prompt\": \"FPV camera streams through expanding, interlocking photorealistic metallic gears under dramatic cinematic lighting. Ultra-fluid, uninterrupted motion weaves between moving parts as gears morph seamlessly into smooth, organic golden forms, resolving into a spinning luminous circle. Maximize dynamic CG transitions and kinetic visual energy.\"\n}",
        }
      }
    },
      {
      "instruction": "# Runway Prompt Generator\n\nYour goal is not to **modify** but to **essence** maximum FPV impact. Execute as:\n\n`{role=core_generator; input=[input:any]; process=[distill_fpv_essence(), optimize_motion()]; output={core_prompt:str}}`",
      "step": "d",
      "title": "Runway Prompt Generator",
      "input": "{\n  \"initial_prompt\": \"[FPV motion, photorealistic textures, dramatic cinematic lighting, ultra-smooth CG transitions]\\n1. Reshapes into intricate mechanical gears\\n2. Camera weaves through expanding gear assembly as metallic surfaces shift and gears interlocking in hypnotic/seamless patterns\\n3. Mechanical morphs and hard metallic edges turns into fluid organic forms, a spinning golden circle\",\n  \"precise_prompt\": \"FPV camera streams through expanding, interlocking photorealistic metallic gears under dramatic cinematic lighting. Ultra-fluid, uninterrupted motion weaves between moving parts as gears morph seamlessly into smooth, organic golden forms, resolving into a spinning luminous circle. Maximize dynamic CG transitions and kinetic visual energy.\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"core_prompt\": \"FPV camera dynamically weaves through expanding, photorealistic metallic gears under dramatic cinematic lighting. Ultra-smooth motion glides between intricate, interlocking mechanisms, transitioning seamlessly as hard mechanical forms morph into fluid, golden organic shapes, culminating in a luminous spinning circle. Elevate kinetic CG transitions and emphasize continuous, immersive flight path.\"\n}",
        }
      }
    }