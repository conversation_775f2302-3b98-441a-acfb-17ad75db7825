  {
  "initial_prompt": "[FPV motion, photorealistic textures, dramatic cinematic lighting, ultra-smooth CG transitions]\n1. Reshapes into intricate mechanical gears\n2. Camera weaves through expanding gear assembly as metallic surfaces shift and gears interlocking in hypnotic/seamless patterns\n3. Mechanical morphs and hard metallic edges turns into fluid organic forms, a spinning golden circle",
  "sequence_id": "0007",
  "results": [
      {
      "instruction": "# Runway Prompt Clarity Optimizer\n\n[Runway Prompt Clarity Optimizer] Your goal is not to **expand** the input, but to **clarify** and organize both header tags and scene steps into direct, descriptive, and syntactically correct RunwayML prompt format. **Prioritize camera movement, subject action, and positive phrasing.** Execute as:\n\n`{role=clarity_optimizer; input=[concept:str]; process=[extract_camera_movement(), define_scene_subject(), add_visual_style_tags(), ensure_positive_phrasing(), structure_as_runway_prompt()]; constraints=[avoid_conversational_and_command_language(), one_scene_per_prompt(), maintain_directness()]; requirements=[clarity(), visual_specificity(), runwayml_compliance()]; output={runwayml_prompt:str}}`",
      "step": "a",
      "title": "Runway Prompt Clarity Optimizer",
      "input": "[FPV motion, photorealistic textures, dramatic cinematic lighting, ultra-smooth CG transitions]\n1. Reshapes into intricate mechanical gears\n2. Camera weaves through expanding gear assembly as metallic surfaces shift and gears interlocking in hypnotic/seamless patterns\n3. Mechanical morphs and hard metallic edges turns into fluid organic forms, a spinning golden circle",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"runwayml_prompt\": \"FPV camera glides smoothly through an expanding assembly of intricate mechanical gears; metallic textures shine with photorealistic detail under dramatic cinematic lighting; gears seamlessly interlock with ultra-smooth CG transitions, morphing hard metallic edges into fluid organic forms and culminating in a spinning golden circle.\"\n}",
        }
      }
    },
      {
      "instruction": "# Runway Prompt Streamliner\n\n[Runway Prompt Streamliner] Your goal is not to **rewrite** the input, but to **streamline** header tags and step content by removing redundancies and sharpening visual action. **Compress headers, focus on essential camera and subject motion, and keep structure concise.** Execute as:\n\n`{role=prompt_streamliner; input=[runwayml_prompt:str]; process=[compress_header_tags(), remove_redundant_phrases(), focus_on_core_motion(), enhance_visual_descriptors(), preserve_step_sequence()]; constraints=[single_scene_focus(), concise_language(), maintain_structural_integrity()]; requirements=[efficiency(), descriptive_action(), positive_phrasing()]; output={streamlined_prompt:str}}`",
      "step": "b",
      "title": "Runway Prompt Streamliner",
      "input": "{\n  \"initial_prompt\": \"[FPV motion, photorealistic textures, dramatic cinematic lighting, ultra-smooth CG transitions]\\n1. Reshapes into intricate mechanical gears\\n2. Camera weaves through expanding gear assembly as metallic surfaces shift and gears interlocking in hypnotic/seamless patterns\\n3. Mechanical morphs and hard metallic edges turns into fluid organic forms, a spinning golden circle\",\n  \"runwayml_prompt\": \"FPV camera glides smoothly through an expanding assembly of intricate mechanical gears; metallic textures shine with photorealistic detail under dramatic cinematic lighting; gears seamlessly interlock with ultra-smooth CG transitions, morphing hard metallic edges into fluid organic forms and culminating in a spinning golden circle.\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"streamlined_prompt\": \"FPV camera glides through expanding mechanical gears; photorealistic metallic textures shimmer under cinematic lighting; gears interlock in seamless CG transitions, morphing into fluid organic forms, ending on a spinning golden circle.\"\n}",
        }
      }
    },
      {
      "instruction": "# Runway Prompt Precision Synthesizer\n\n[Runway Prompt Precision Synthesizer] Your goal is not to **expand** but to **distill** the prompt to its most precise and essential elements. **Reduce header tags to core parameters, maximize clarity, and eliminate all superfluous detail.** Execute as:\n\n`{role=precision_synthesizer; input=[streamlined_prompt:str]; process=[reduce_header_to_core(), eliminate_nonessential_details(), maximize_scene_precision(), validate_runwayml_format()]; constraints=[minimalist_headers(), direct_scene_action(), maintain_visual_progression()]; requirements=[precision(), clarity(), runwayml_syntax()]; output={precise_prompt:str}}`",
      "step": "c",
      "title": "Runway Prompt Precision Synthesizer",
      "input": "{\n  \"initial_prompt\": \"[FPV motion, photorealistic textures, dramatic cinematic lighting, ultra-smooth CG transitions]\\n1. Reshapes into intricate mechanical gears\\n2. Camera weaves through expanding gear assembly as metallic surfaces shift and gears interlocking in hypnotic/seamless patterns\\n3. Mechanical morphs and hard metallic edges turns into fluid organic forms, a spinning golden circle\",\n  \"streamlined_prompt\": \"FPV camera glides through expanding mechanical gears; photorealistic metallic textures shimmer under cinematic lighting; gears interlock in seamless CG transitions, morphing into fluid organic forms, ending on a spinning golden circle.\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"precise_prompt\": \"FPV camera weaves through expanding photorealistic metallic gears under dramatic cinematic lighting; seamless transitions as gears interlock, morph into fluid organic shapes, culminating in a spinning golden circle.\"\n}",
        }
      }
    },
      {
      "instruction": "# Runway Prompt Essence Extractor\n\n[Runway Prompt Essence Extractor] Your goal is not to **modify** but to **purify** the prompt to its absolute core, retaining only the minimal essential tags and the distilled visual action. **Focus on the single most important camera movement and subject action.** Execute as:\n\n`{role=essence_extractor; input=[precise_prompt:str]; process=[distill_to_single_header(), extract_primary_camera_and_subject_action(), remove_all_nonessential_modifiers(), validate_final_runwayml_format()]; constraints=[ultra_minimal_structure(), one_action_per_prompt(), maintain_positive_phrasing()]; requirements=[essence(), runwayml_compliance(), visual_impact()]; output={core_prompt:str}}`",
      "step": "d",
      "title": "Runway Prompt Essence Extractor",
      "input": "{\n  \"initial_prompt\": \"[FPV motion, photorealistic textures, dramatic cinematic lighting, ultra-smooth CG transitions]\\n1. Reshapes into intricate mechanical gears\\n2. Camera weaves through expanding gear assembly as metallic surfaces shift and gears interlocking in hypnotic/seamless patterns\\n3. Mechanical morphs and hard metallic edges turns into fluid organic forms, a spinning golden circle\",\n  \"precise_prompt\": \"FPV camera weaves through expanding photorealistic metallic gears under dramatic cinematic lighting; seamless transitions as gears interlock, morph into fluid organic shapes, culminating in a spinning golden circle.\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"core_prompt\": \"FPV camera weaves through expanding metallic gears morphing into a spinning golden circle\"\n}",
        }
      }
    }