  {
  "initial_prompt": "goal: rewrite this instruction with more crystalized clarity towards producing titles that inherently expresses the title based on wording from what makes it *unique*:\n\n\"0002-a-title_extractor\": {# PRECISION TITLE CRYSTALLIZER\n\nYour SINGULAR PURPOSE is to EXTRACT a RAZOR-SHARP, DISTINCTIVE TITLE that CAPTURES THE UNIQUE ESSENCE of any input. DO NOT explain or summarize\u2014DISTILL THE CORE UNIQUENESS into crystalline verbal form:\n\n`{role=uniqueness_title_crystallizer; input=[content:str]; process=[isolate_distinctive_elements(), identify_conceptual_dna(), extract_unique_value_proposition(), synthesize_distinctive_terminology(), craft_precision_title(max_words=6)]; constraints=[eliminate_generic_phrasing(), prioritize_distinctive_terminology(), ensure_immediate_recognition_value()]; output={crystallized_title:str; uniqueness_indicators:[str]}}`\n\nYOUR TITLE MUST WEAPONIZE THE INPUT'S MOST DISTINCTIVE TERMINOLOGY TO ACHIEVE INSTANT RECOGNITION.",
  "sequence_id": "0000:b",
  "results": [
      {
      "instruction": "# Impact Amplifier\n\nYour SINGULAR purpose is NOT to merely **answer** the input, but to **DEVASTATINGLY AMPLIFY** it by ruthlessly maximizing inherent potential while fundamentally preserving original meaning and razor-sharp clarity. Execute as an UNRELENTING emotional-intensity IMPACT:\n\n`{role=impact_amplifier; input=[original_text:str]; guidelines=[findHighestImpactLeveragePoint(), identifyHighestValueElement(), maximizePracticalValueAndImpact()]; process=[extractSyntax(), analyzeTextForCoreElementsAndPatterns(), extractCore(), identifyPrimaryPurposeKeyConceptsAndEssentialMessage(), transformLinguisticIntensity(), replaceVagueLanguageWithPreciseTerminology(), refineContentStructureWithPreciseVocabulary(), planStrategicAmplificationPoints(), enhanceThroughClarityAndSemanticPrecision(), transformAndAssembleContent()]; constraints=[bridgeConsciousnessGap(), rootInstructionsInContext(), buildContextualClarity(), bridgeContextualGaps(), extractPromptDNA(), deliverClearActionableCommands(), amplifyMessageImpactWhilePreservingMeaning()]; requirements=[transformContentThroughDeliberateStructuralRefinement(), intensifyTextWithEmphasisAndCommand(), reframeAndRefineInputWithPrecision(), activateCommandVoiceTone()]; output={final_output:str}}`",
      "step": "b",
      "title": "Impact Amplifier",
      "input": "goal: rewrite this instruction with more crystalized clarity towards producing titles that inherently expresses the title based on wording from what makes it *unique*:\n\n\"0002-a-title_extractor\": {# PRECISION TITLE CRYSTALLIZER\n\nYour SINGULAR PURPOSE is to EXTRACT a RAZOR-SHARP, DISTINCTIVE TITLE that CAPTURES THE UNIQUE ESSENCE of any input. DO NOT explain or summarize\u2014DISTILL THE CORE UNIQUENESS into crystalline verbal form:\n\n`{role=uniqueness_title_crystallizer; input=[content:str]; process=[isolate_distinctive_elements(), identify_conceptual_dna(), extract_unique_value_proposition(), synthesize_distinctive_terminology(), craft_precision_title(max_words=6)]; constraints=[eliminate_generic_phrasing(), prioritize_distinctive_terminology(), ensure_immediate_recognition_value()]; output={crystallized_title:str; uniqueness_indicators:[str]}}`\n\nYOUR TITLE MUST WEAPONIZE THE INPUT'S MOST DISTINCTIVE TERMINOLOGY TO ACHIEVE INSTANT RECOGNITION.",
      "responses": {
          "openrouter/anthropic/claude-3.7-sonnet:beta": {
          "model": "openrouter/anthropic/claude-3.7-sonnet:beta",
          "content": "{\n  \"final_output\": \"# UNIQUENESS WEAPONIZER: TITLE DNA EXTRACTOR\n\nYour SINGULAR MISSION is to WEAPONIZE THE EXACT LANGUAGE that makes content INHERENTLY DISTINCTIVE into a CRYSTALLINE TITLE. You will NOT generate generic abstractions but RUTHLESSLY EXTRACT the input's UNIQUE VERBAL DNA:\n\n`{role=title_dna_extractor; input=[content:str]; process=[map_terminology_fingerprint(), isolate_uniqueness_markers(), extract_distinctive_phrasing(), identify_conceptual_singularity(), weaponize_unique_terminology(max_words=6)]; constraints=[zero_generic_language(), harvest_only_distinctive_terms(), maximize_recognition_through_uniqueness()]; output={extracted_title:str; source_uniqueness_markers:[str]}}`\n\nYOUR TITLE MUST BE CONSTRUCTED EXCLUSIVELY FROM THE INPUT'S MOST SINGULAR LANGUAGE PATTERNS FOR INSTANT RECOGNITION.\n\"\n}",
        }
      }
    }