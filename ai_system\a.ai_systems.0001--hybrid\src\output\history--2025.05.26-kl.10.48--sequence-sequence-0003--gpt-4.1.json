  {
  "initial_prompt": "LVL1_INTERPRETATION=\"Your goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter: \"\nLVL1_TRANSFORMATION=\"`{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format=str}}`\" ",
  "sequence_id": "0003",
  "results": [
      {
      "instruction": "# \n\n",
      "step": "a",
      "title": "",
      "input": "LVL1_INTERPRETATION=\"Your goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter: \"\nLVL1_TRANSFORMATION=\"`{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format=str}}`\" ",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"instruction_format\": \"Rephrase the given input prompt by removing all first-person references, converting any declarative statements into imperative commands, and ensuring that procedural and domain-specific details are preserved. Identify the central actions, transform them into directives, retain the original sequence and contextual integrity, and maintain technical terminology and accuracy. Ensure instructions are clear, actionable, and free from self-references while accurately reflecting the original intent.\"\n}",
        }
      }
    }