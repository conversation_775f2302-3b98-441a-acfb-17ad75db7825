  {
  "initial_prompt": "[FPV motion, photorealistic textures, dramatic cinematic lighting, ultra-smooth CG transitions]\n1. Reshapes into intricate mechanical gears\n2. Camera weaves through expanding gear assembly as metallic surfaces shift and gears interlocking in hypnotic/seamless patterns\n3. Mechanical morphs and hard metallic edges turns into fluid organic forms, a spinning golden circle",
  "sequence_id": "0007",
  "results": [
      {
      "instruction": "# Runway Camera Movement Optimizer\n\nYour goal is not to **expand** the input, but to **enhance** camera movement specifications while preserving bracketed header structure and numbered sequences. **Prioritize FPV motion clarity and camera action precision.** Execute as:\n\n`{role=camera_movement_optimizer; input=[concept:str]; process=[enhance_fpv_specifications(), clarify_camera_actions(), preserve_header_bracket_structure(), maintain_numbered_sequence(), optimize_movement_verbs()]; constraints=[preserve_bracketed_headers(), maintain_three_step_structure(), prioritize_camera_clarity()]; requirements=[fpv_motion_enhancement(), structural_preservation(), movement_precision()]; output={camera_optimized_prompt:str}}`",
      "step": "a",
      "title": "Runway Camera Movement Optimizer",
      "input": "[FPV motion, photorealistic textures, dramatic cinematic lighting, ultra-smooth CG transitions]\n1. Reshapes into intricate mechanical gears\n2. Camera weaves through expanding gear assembly as metallic surfaces shift and gears interlocking in hypnotic/seamless patterns\n3. Mechanical morphs and hard metallic edges turns into fluid organic forms, a spinning golden circle",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"camera_optimized_prompt\": \"[FPV motion, photorealistic textures, dramatic cinematic lighting, ultra-smooth CG transitions]\\n1. Camera glides in first-person perspective as intricate mechanical gears rapidly reshape, highlighting gear teeth interlocking and metallic surfaces transforming with tactile photorealism.\\n2. Camera dynamically threads and loops through the expanding gear assembly, executing agile bank turns and close passes as metallic surfaces shift, gears mesh in mesmerizing, seamless choreography.\\n3. Camera orbits and zooms as mechanical forms morph with fluid precision, hard metallic edges dissolving into smooth, organic contours, culminating in a continuous FPV spiral around a spinning golden circle.\"\n}",
        }
      }
    },
      {
      "instruction": "# Runway Visual Descriptor Streamliner\n\nYour goal is not to **rewrite** but to **compress** visual descriptors within the bracketed headers while sharpening scene action in numbered steps. **Streamline header tags, enhance metallic texture descriptions.** Execute as:\n\n`{role=visual_streamliner; input=[camera_optimized_prompt:str]; process=[compress_header_descriptors(), enhance_metallic_terminology(), sharpen_gear_interlocking_descriptions(), preserve_numbered_structure()]; constraints=[maintain_bracket_format(), keep_three_scenes(), enhance_hypnotic_patterns()]; requirements=[visual_efficiency(), metallic_precision(), structural_integrity()]; output={streamlined_visual_prompt:str}}`",
      "step": "b",
      "title": "Runway Visual Descriptor Streamliner",
      "input": "{\n  \"initial_prompt\": \"[FPV motion, photorealistic textures, dramatic cinematic lighting, ultra-smooth CG transitions]\\n1. Reshapes into intricate mechanical gears\\n2. Camera weaves through expanding gear assembly as metallic surfaces shift and gears interlocking in hypnotic/seamless patterns\\n3. Mechanical morphs and hard metallic edges turns into fluid organic forms, a spinning golden circle\",\n  \"camera_optimized_prompt\": \"[FPV motion, photorealistic textures, dramatic cinematic lighting, ultra-smooth CG transitions]\\n1. Camera glides in first-person perspective as intricate mechanical gears rapidly reshape, highlighting gear teeth interlocking and metallic surfaces transforming with tactile photorealism.\\n2. Camera dynamically threads and loops through the expanding gear assembly, executing agile bank turns and close passes as metallic surfaces shift, gears mesh in mesmerizing, seamless choreography.\\n3. Camera orbits and zooms as mechanical forms morph with fluid precision, hard metallic edges dissolving into smooth, organic contours, culminating in a continuous FPV spiral around a spinning golden circle.\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"streamlined_visual_prompt\": \"[FPV, photoreal textures, cinematic light, seamless CG]\\n1. FPV glides amid morphing gears\u2014chrome, brushed steel, tungsten\u2014highlighting precise gear teeth mesh, hypnotic interlocks, sharply detailed metal sheen.\\n2. Camera threads expanding gearworks; gears cascade, overlap, and snap into rhythmic, continuous loops of shimmering alloys and seamless, entrancing rotation.\\n3. Metallic machinery fluidly morphs\u2014hard-edged to liquid gold\u2014into an organic spiral, FPV circling relentlessly around a luminous spinning golden disc.\"\n}",
        }
      }
    },
      {
      "instruction": "# Runway Motion Precision Synthesizer\n\nYour goal is not to **modify** but to **distill** motion descriptions to essential camera and subject actions while preserving header-sequence structure. **Focus on gear mechanics and morphing transitions.** Execute as:\n\n`{role=motion_synthesizer; input=[streamlined_visual_prompt:str]; process=[distill_motion_to_essentials(), preserve_mechanical_to_organic_progression(), maintain_header_sequence_format(), enhance_spinning_finale()]; constraints=[essential_motion_only(), preserve_three_step_progression(), maintain_brackets()]; requirements=[motion_clarity(), progression_preservation(), format_compliance()]; output={motion_precise_prompt:str}}`",
      "step": "c",
      "title": "Runway Motion Precision Synthesizer",
      "input": "{\n  \"initial_prompt\": \"[FPV motion, photorealistic textures, dramatic cinematic lighting, ultra-smooth CG transitions]\\n1. Reshapes into intricate mechanical gears\\n2. Camera weaves through expanding gear assembly as metallic surfaces shift and gears interlocking in hypnotic/seamless patterns\\n3. Mechanical morphs and hard metallic edges turns into fluid organic forms, a spinning golden circle\",\n  \"streamlined_visual_prompt\": \"[FPV, photoreal textures, cinematic light, seamless CG]\\n1. FPV glides amid morphing gears\\u2014chrome, brushed steel, tungsten\\u2014highlighting precise gear teeth mesh, hypnotic interlocks, sharply detailed metal sheen.\\n2. Camera threads expanding gearworks; gears cascade, overlap, and snap into rhythmic, continuous loops of shimmering alloys and seamless, entrancing rotation.\\n3. Metallic machinery fluidly morphs\\u2014hard-edged to liquid gold\\u2014into an organic spiral, FPV circling relentlessly around a luminous spinning golden disc.\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"motion_precise_prompt\": \"[FPV, photoreal textures, cinematic light, seamless CG]\\n1. Gears emerge and interlock; FPV weaves precisely through shifting gear teeth and morphing metal surfaces.\\n2. Camera threads expanding gear assemblies; gears overlap, rotate, and mesh in seamless, mechanical-to-organic patterns.\\n3. Mechanical forms morph into fluid organic spiral; FPV orbits a luminous spinning golden disc, accentuating dynamic motion finale.\"\n}",
        }
      }
    },
      {
      "instruction": "# Runway Core Action Extractor\n\nYour goal is not to **change** but to **purify** the prompt to minimal essential header tags and core action sequence. **Retain only critical FPV motion and gear-to-golden transformation.** Execute as:\n\n`{role=core_extractor; input=[motion_precise_prompt:str]; process=[extract_minimal_header_tags(), distill_core_gear_progression(), preserve_golden_ring_finale(), maintain_numbered_format()]; constraints=[minimal_header_tags(), three_action_limit(), preserve_structure()]; requirements=[core_essence(), format_preservation(), visual_impact()]; output={core_action_prompt:str}}`",
      "step": "d",
      "title": "Runway Core Action Extractor",
      "input": "{\n  \"initial_prompt\": \"[FPV motion, photorealistic textures, dramatic cinematic lighting, ultra-smooth CG transitions]\\n1. Reshapes into intricate mechanical gears\\n2. Camera weaves through expanding gear assembly as metallic surfaces shift and gears interlocking in hypnotic/seamless patterns\\n3. Mechanical morphs and hard metallic edges turns into fluid organic forms, a spinning golden circle\",\n  \"motion_precise_prompt\": \"[FPV, photoreal textures, cinematic light, seamless CG]\\n1. Gears emerge and interlock; FPV weaves precisely through shifting gear teeth and morphing metal surfaces.\\n2. Camera threads expanding gear assemblies; gears overlap, rotate, and mesh in seamless, mechanical-to-organic patterns.\\n3. Mechanical forms morph into fluid organic spiral; FPV orbits a luminous spinning golden disc, accentuating dynamic motion finale.\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"core_action_prompt\": \"[FPV, photoreal textures, cinematic lighting]\\n1. Gears emerge and interlock as FPV weaves through morphing metal surfaces.\\n2. Camera threads through expanding gear assembly, gears rotating and meshing in seamless progression.\\n3. Mechanical forms morph into a spinning golden ring; FPV orbits this luminous finale.\"\n}",
        }
      }
    }