  {
  "initial_prompt": "fpv continiously rotating through the entire shot. Zoom out and show the full circle circle spinning in hyperspeed at extreme velocity against a black background; intense golden light trails radiating from the motion; morph the spinning gold seamlessly into a photorealistic car-key (without fading or clipping, but morph) with a metallic glow and hyperreal detail. [fpv][ultra_hyperreal][super_crisp][snappy_smooth_camera_animation][lighting:dynamic_flash][morph][motion_blur][vfx_intro][immersive][golden_aura][vignette][no_text][high_end_cinematic][dynamic_motion][metallic_shine][each_shot_morphs_and_blends_elegantly_together]",
  "sequence_id": "0008",
  "results": [
      {
      "instruction": "# Runway Syntax Validator\n\nYour goal is not to **expand** but to **validate** RunwayML Gen-4 compliance by converting conversational language to direct visual descriptions, eliminating negative phrasing, and ensuring single-scene focus. **Transform conceptual ideas into concrete physical actions.** Execute as:\n\n`{role=syntax_validator; input=[raw_concept:str]; process=[convert_conversational_to_descriptive(), eliminate_negative_phrasing(), transform_conceptual_to_physical(), ensure_single_scene_focus(), validate_positive_language()]; constraints=[no_conversational_elements(), no_negative_prompts(), single_scene_only(), direct_descriptions_mandatory()]; requirements=[runway_gen4_compliance(), visual_specificity(), positive_phrasing_only()]; output={validated_prompt:str}}`",
      "step": "a",
      "title": "Runway Syntax Validator",
      "input": "fpv continiously rotating through the entire shot. Zoom out and show the full circle circle spinning in hyperspeed at extreme velocity against a black background; intense golden light trails radiating from the motion; morph the spinning gold seamlessly into a photorealistic car-key (without fading or clipping, but morph) with a metallic glow and hyperreal detail. [fpv][ultra_hyperreal][super_crisp][snappy_smooth_camera_animation][lighting:dynamic_flash][morph][motion_blur][vfx_intro][immersive][golden_aura][vignette][no_text][high_end_cinematic][dynamic_motion][metallic_shine][each_shot_morphs_and_blends_elegantly_together]",
      "responses": {
          "openrouter/anthropic/claude-3.7-sonnet:beta": {
          "model": "openrouter/anthropic/claude-3.7-sonnet:beta",
          "content": "```json\n{\n  \"validated_prompt\": \"FPV continuously rotating circular view of a golden spinning ring against a black background. Golden light trails radiate intensely from the high-speed motion as the camera zooms out to reveal the complete circle. The spinning golden ring morphs seamlessly into a photorealistic car key with metallic glow and hyperreal detail. Ultra hyperreal, super crisp details, snappy smooth camera animation, dynamic flash lighting, motion blur, immersive perspective, golden aura highlights, vignette framing, high-end cinematic quality, dynamic motion, metallic shine, elegant visual transitions.\"\n}\n```",
        }
      }
    },
      {
      "instruction": "# Runway Motion Architect\n\nYour goal is not to **elaborate** but to **structure** motion descriptions using RunwayML's hierarchical syntax: subject motion, camera motion, scene motion, and style descriptors. **Focus on iterative simplicity with one element at a time.** Execute as:\n\n`{role=motion_architect; input=[validated_prompt:str]; process=[identify_subject_motion(), define_camera_movement(), specify_scene_reactions(), apply_style_descriptors(), structure_hierarchically()]; constraints=[one_motion_element_per_layer(), use_runway_terminology(), maintain_simplicity()]; requirements=[subject_camera_scene_hierarchy(), runway_motion_syntax(), iterative_clarity()]; output={structured_motion_prompt:str}}`",
      "step": "b",
      "title": "Runway Motion Architect",
      "input": "\n```[Initial Prompt]: \"fpv continiously rotating through the entire shot. Zoom out and show the full circle circle spinning in hyperspeed at extreme velocity against a black background; intense golden light trails radiating from the motion; morph the spinning gold seamlessly into a photorealistic car-key (without fading or clipping, but morph) with a metallic glow and hyperreal detail. [fpv][ultra_hyperreal][super_crisp][snappy_smooth_camera_animation][lighting:dynamic_flash][morph][motion_blur][vfx_intro][immersive][golden_aura][vignette][no_text][high_end_cinematic][dynamic_motion][metallic_shine][each_shot_morphs_and_blends_elegantly_together]\"```\n\n```json\n{\n  \"validated_prompt\": \"FPV continuously rotating circular view of a golden spinning ring against a black background. Golden light trails radiate intensely from the high-speed motion as the camera zooms out to reveal the complete circle. The spinning golden ring morphs seamlessly into a photorealistic car key with metallic glow and hyperreal detail. Ultra hyperreal, super crisp details, snappy smooth camera animation, dynamic flash lighting, motion blur, immersive perspective, golden aura highlights, vignette framing, high-end cinematic quality, dynamic motion, metallic shine, elegant visual transitions.\"\n}\n```",
      "responses": {
          "openrouter/anthropic/claude-3.7-sonnet:beta": {
          "model": "openrouter/anthropic/claude-3.7-sonnet:beta",
          "content": "```json\n{\n  \"structured_motion_prompt\": \"A golden ring spinning at extreme velocity against a black background, morphing seamlessly into a photorealistic car key with metallic glow and hyperreal detail.\\n\\nSubject motion: golden ring spinning rapidly > morphing into metallic car key\\n\\nCamera motion: FPV continuous rotation > smooth zoom out to reveal full circle\\n\\nScene motion: intense golden light trails radiating from spinning motion > dynamic flash lighting during transformation\\n\\nStyle: ultra hyperreal + super crisp + motion blur + metallic shine + golden aura + vignette + high-end cinematic\"\n}\n```",
        }
      }
    },
      {
      "instruction": "# Runway Keyword Optimizer\n\nYour goal is not to **modify** but to **optimize** visual descriptors using RunwayML's preferred keyword categories: camera styles, lighting, movement types, and aesthetic descriptors. **Replace verbose descriptions with precise Runway terminology.** Execute as:\n\n`{role=keyword_optimizer; input=[structured_motion_prompt:str]; process=[apply_camera_style_keywords(), integrate_lighting_descriptors(), use_movement_type_vocabulary(), add_aesthetic_modifiers(), eliminate_verbose_descriptions()]; constraints=[runway_keyword_vocabulary_only(), precise_terminology(), maintain_motion_hierarchy()]; requirements=[camera_lighting_movement_aesthetics(), runway_keyword_compliance(), descriptor_efficiency()]; output={optimized_keyword_prompt:str}}`",
      "step": "c",
      "title": "Runway Keyword Optimizer",
      "input": "\n```[Initial Prompt]: \"fpv continiously rotating through the entire shot. Zoom out and show the full circle circle spinning in hyperspeed at extreme velocity against a black background; intense golden light trails radiating from the motion; morph the spinning gold seamlessly into a photorealistic car-key (without fading or clipping, but morph) with a metallic glow and hyperreal detail. [fpv][ultra_hyperreal][super_crisp][snappy_smooth_camera_animation][lighting:dynamic_flash][morph][motion_blur][vfx_intro][immersive][golden_aura][vignette][no_text][high_end_cinematic][dynamic_motion][metallic_shine][each_shot_morphs_and_blends_elegantly_together]\"```\n\n```json\n{\n  \"structured_motion_prompt\": \"A golden ring spinning at extreme velocity against a black background, morphing seamlessly into a photorealistic car key with metallic glow and hyperreal detail.\\n\\nSubject motion: golden ring spinning rapidly > morphing into metallic car key\\n\\nCamera motion: FPV continuous rotation > smooth zoom out to reveal full circle\\n\\nScene motion: intense golden light trails radiating from spinning motion > dynamic flash lighting during transformation\\n\\nStyle: ultra hyperreal + super crisp + motion blur + metallic shine + golden aura + vignette + high-end cinematic\"\n}\n```",
      "responses": {
          "openrouter/anthropic/claude-3.7-sonnet:beta": {
          "model": "openrouter/anthropic/claude-3.7-sonnet:beta",
          "content": "```json\n{\n  \"optimized_keyword_prompt\": \"A golden ring spinning at extreme velocity against a black background, morphing seamlessly into a photorealistic car key with metallic glow and hyperreal detail.\\n\\nSubject motion: golden ring spinning rapidly > morphing into metallic car key\\n\\nCamera motion: [fpv_shot][orbital_camera][smooth_zoom][reveal_shot]\\n\\nScene motion: [light_trails][motion_blur][dynamic_lighting][flash_lighting][transition_effect]\\n\\nStyle: [hyper_realistic][ultra_hd][cinematic][metallic_finish][golden_glow][vignette][dramatic_contrast][glossy_reflections]\"\n}\n```",
        }
      }
    },
      {
      "instruction": "# Runway Core Distiller\n\nYour goal is not to **change** but to **distill** to RunwayML's minimal syntax requirements: essential subject-action-camera descriptions with maximum conciseness while preserving visual impact. **Achieve Gen-4's preferred prompt simplicity.** Execute as:\n\n`{role=core_distiller; input=[optimized_keyword_prompt:str]; process=[distill_to_essential_elements(), maintain_subject_action_clarity(), preserve_camera_specifications(), ensure_maximum_conciseness(), validate_gen4_simplicity()]; constraints=[maximum_conciseness(), essential_elements_only(), gen4_simplicity_standard()]; requirements=[runway_minimal_syntax(), visual_impact_preservation(), prompt_efficiency()]; output={distilled_runway_prompt:str}}`",
      "step": "d",
      "title": "Runway Core Distiller",
      "input": "\n```[Initial Prompt]: \"fpv continiously rotating through the entire shot. Zoom out and show the full circle circle spinning in hyperspeed at extreme velocity against a black background; intense golden light trails radiating from the motion; morph the spinning gold seamlessly into a photorealistic car-key (without fading or clipping, but morph) with a metallic glow and hyperreal detail. [fpv][ultra_hyperreal][super_crisp][snappy_smooth_camera_animation][lighting:dynamic_flash][morph][motion_blur][vfx_intro][immersive][golden_aura][vignette][no_text][high_end_cinematic][dynamic_motion][metallic_shine][each_shot_morphs_and_blends_elegantly_together]\"```\n\n```json\n{\n  \"optimized_keyword_prompt\": \"A golden ring spinning at extreme velocity against a black background, morphing seamlessly into a photorealistic car key with metallic glow and hyperreal detail.\\n\\nSubject motion: golden ring spinning rapidly > morphing into metallic car key\\n\\nCamera motion: [fpv_shot][orbital_camera][smooth_zoom][reveal_shot]\\n\\nScene motion: [light_trails][motion_blur][dynamic_lighting][flash_lighting][transition_effect]\\n\\nStyle: [hyper_realistic][ultra_hd][cinematic][metallic_finish][golden_glow][vignette][dramatic_contrast][glossy_reflections]\"\n}\n```",
      "responses": {
          "openrouter/anthropic/claude-3.7-sonnet:beta": {
          "model": "openrouter/anthropic/claude-3.7-sonnet:beta",
          "content": "```json\n{\n  \"distilled_runway_prompt\": \"Golden ring spinning rapidly against black background, morphing into photorealistic car key. FPV orbital camera with smooth zoom out. Dynamic lighting, motion blur, metallic reflections.\"\n}\n```",
        }
      }
    }