  {
  "initial_prompt": "INTENSITY AND AMPLIFY INSTRUCTIONS TO MORPH CG-TRANSFORMERS-EFFECT INSTEAD OF BAD TRANSITIONS\n\n{\n  \"structured_motion_prompt\": {\n    \"subject_motion\": {\n      \"primary\": \"sleek sports car silhouette transforming into a golden logo-like silhouette\",\n      \"details\": \"smooth morphing that highlights aerodynamic curves, fastback roofline, wheel arches, and front-end contours\",\n      \"emphasis\": \"precise linework transformation with metallic luminescence\"\n    },\n    \"camera_motion\": {\n      \"movement\": \"first dynamic flow tracking the initial transformation process, then static/still camera as soon as the silhouette is established\",\n    },\n    \"scene_motion\": {\n      \"lighting\": \"dynamic lighting that accentuates the metallic surfaces\",\n      \"atmosphere\": \"golden aura surrounding the vehicle as it morphs\"\n    },\n    \"style_descriptors\": {\n      \"quality\": \"ultra hyperreal, super crisp visuals\",\n      \"aesthetic\": \"high-end cinematic with vignette framing\",\n      \"mood\": \"immersive and elegant morphing between forms\"\n    }\n  }\n}",
  "sequence_id": "0008",
  "results": [
      {
      "instruction": "# Runway Syntax Validator\n\nYour goal is not to **expand** but to **validate** RunwayML Gen-4 compliance by converting conversational language to direct visual descriptions, eliminating negative phrasing, and ensuring single-scene focus. **Transform conceptual ideas into concrete physical actions.** Execute as:\n\n`{role=syntax_validator; input=[raw_concept:str]; process=[convert_conversational_to_descriptive(), eliminate_negative_phrasing(), transform_conceptual_to_physical(), ensure_single_scene_focus(), validate_positive_language()]; constraints=[no_conversational_elements(), no_negative_prompts(), single_scene_only(), direct_descriptions_mandatory()]; requirements=[runway_gen4_compliance(), visual_specificity(), positive_phrasing_only()]; output={validated_prompt:str}}`",
      "step": "a",
      "title": "Runway Syntax Validator",
      "input": "INTENSITY AND AMPLIFY INSTRUCTIONS TO MORPH CG-TRANSFORMERS-EFFECT INSTEAD OF BAD TRANSITIONS\n\n{\n  \"structured_motion_prompt\": {\n    \"subject_motion\": {\n      \"primary\": \"sleek sports car silhouette transforming into a golden logo-like silhouette\",\n      \"details\": \"smooth morphing that highlights aerodynamic curves, fastback roofline, wheel arches, and front-end contours\",\n      \"emphasis\": \"precise linework transformation with metallic luminescence\"\n    },\n    \"camera_motion\": {\n      \"movement\": \"first dynamic flow tracking the initial transformation process, then static/still camera as soon as the silhouette is established\",\n    },\n    \"scene_motion\": {\n      \"lighting\": \"dynamic lighting that accentuates the metallic surfaces\",\n      \"atmosphere\": \"golden aura surrounding the vehicle as it morphs\"\n    },\n    \"style_descriptors\": {\n      \"quality\": \"ultra hyperreal, super crisp visuals\",\n      \"aesthetic\": \"high-end cinematic with vignette framing\",\n      \"mood\": \"immersive and elegant morphing between forms\"\n    }\n  }\n}",
      "responses": {
          "openrouter/anthropic/claude-3.7-sonnet:beta": {
          "model": "openrouter/anthropic/claude-3.7-sonnet:beta",
          "content": "```json\n{\n  \"validated_prompt\": \"Sleek sports car silhouette transforming into a golden logo-like silhouette with smooth morphing that highlights aerodynamic curves, fastback roofline, wheel arches, and front-end contours. Precise linework transformation with metallic luminescence. Dynamic lighting accentuates the metallic surfaces. Golden aura surrounds the vehicle as it morphs. Ultra hyperreal, super crisp visuals. High-end cinematic with vignette framing. Immersive and elegant morphing between forms.\"\n}\n```",
        }
      }
    },
      {
      "instruction": "# Runway Motion Architect\n\nYour goal is not to **elaborate** but to **structure** motion descriptions using RunwayML's hierarchical syntax: subject motion, camera motion, scene motion, and style descriptors. **Focus on iterative simplicity with one element at a time.** Execute as:\n\n`{role=motion_architect; input=[validated_prompt:str]; process=[identify_subject_motion(), define_camera_movement(), specify_scene_reactions(), apply_style_descriptors(), structure_hierarchically()]; constraints=[one_motion_element_per_layer(), use_runway_terminology(), maintain_simplicity()]; requirements=[subject_camera_scene_hierarchy(), runway_motion_syntax(), iterative_clarity()]; output={structured_motion_prompt:str}}`",
      "step": "b",
      "title": "Runway Motion Architect",
      "input": "\n```[Initial Prompt]: \"INTENSITY AND AMPLIFY INSTRUCTIONS TO MORPH CG-TRANSFORMERS-EFFECT INSTEAD OF BAD TRANSITIONS\n\n{\n  \"structured_motion_prompt\": {\n    \"subject_motion\": {\n      \"primary\": \"sleek sports car silhouette transforming into a golden logo-like silhouette\",\n      \"details\": \"smooth morphing that highlights aerodynamic curves, fastback roofline, wheel arches, and front-end contours\",\n      \"emphasis\": \"precise linework transformation with metallic luminescence\"\n    },\n    \"camera_motion\": {\n      \"movement\": \"first dynamic flow tracking the initial transformation process, then static/still camera as soon as the silhouette is established\",\n    },\n    \"scene_motion\": {\n      \"lighting\": \"dynamic lighting that accentuates the metallic surfaces\",\n      \"atmosphere\": \"golden aura surrounding the vehicle as it morphs\"\n    },\n    \"style_descriptors\": {\n      \"quality\": \"ultra hyperreal, super crisp visuals\",\n      \"aesthetic\": \"high-end cinematic with vignette framing\",\n      \"mood\": \"immersive and elegant morphing between forms\"\n    }\n  }\n}\"```\n\n```json\n{\n  \"validated_prompt\": \"Sleek sports car silhouette transforming into a golden logo-like silhouette with smooth morphing that highlights aerodynamic curves, fastback roofline, wheel arches, and front-end contours. Precise linework transformation with metallic luminescence. Dynamic lighting accentuates the metallic surfaces. Golden aura surrounds the vehicle as it morphs. Ultra hyperreal, super crisp visuals. High-end cinematic with vignette framing. Immersive and elegant morphing between forms.\"\n}\n```",
      "responses": {
          "openrouter/anthropic/claude-3.7-sonnet:beta": {
          "model": "openrouter/anthropic/claude-3.7-sonnet:beta",
          "content": "```json\n{\n  \"structured_motion_prompt\": {\n    \"subject_motion\": {\n      \"primary\": \"sports car silhouette morphs into golden logo silhouette\",\n      \"secondary\": \"smooth transformation highlighting aerodynamic curves and contours\",\n      \"detail\": \"precise linework evolves with metallic luminescence\"\n    },\n    \"camera_motion\": {\n      \"primary\": \"dynamic tracking shot following the initial transformation\",\n      \"secondary\": \"stabilizes to static position once silhouette completes forming\"\n    },\n    \"scene_motion\": {\n      \"environment\": \"minimalist background that emphasizes the transformation\",\n      \"lighting\": \"dynamic lights sweep across metallic surfaces during morphing\",\n      \"effects\": \"golden aura emanates and intensifies around the transforming vehicle\"\n    },\n    \"style_descriptors\": {\n      \"visual_quality\": \"ultra hyperreal with crisp definition\",\n      \"rendering\": \"CG transformer-style morph effect with precise transitions\",\n      \"mood\": \"elegant, high-end cinematic with subtle vignette framing\"\n    }\n  }\n}```",
        }
      }
    },
      {
      "instruction": "# Runway Keyword Optimizer\n\nYour goal is not to **modify** but to **optimize** visual descriptors using RunwayML's preferred keyword categories: camera styles, lighting, movement types, and aesthetic descriptors. **Replace verbose descriptions with precise Runway terminology.** Execute as:\n\n`{role=keyword_optimizer; input=[structured_motion_prompt:str]; process=[apply_camera_style_keywords(), integrate_lighting_descriptors(), use_movement_type_vocabulary(), add_aesthetic_modifiers(), eliminate_verbose_descriptions()]; constraints=[runway_keyword_vocabulary_only(), precise_terminology(), maintain_motion_hierarchy()]; requirements=[camera_lighting_movement_aesthetics(), runway_keyword_compliance(), descriptor_efficiency()]; output={optimized_keyword_prompt:str}}`",
      "step": "c",
      "title": "Runway Keyword Optimizer",
      "input": "\n```[Initial Prompt]: \"INTENSITY AND AMPLIFY INSTRUCTIONS TO MORPH CG-TRANSFORMERS-EFFECT INSTEAD OF BAD TRANSITIONS\n\n{\n  \"structured_motion_prompt\": {\n    \"subject_motion\": {\n      \"primary\": \"sleek sports car silhouette transforming into a golden logo-like silhouette\",\n      \"details\": \"smooth morphing that highlights aerodynamic curves, fastback roofline, wheel arches, and front-end contours\",\n      \"emphasis\": \"precise linework transformation with metallic luminescence\"\n    },\n    \"camera_motion\": {\n      \"movement\": \"first dynamic flow tracking the initial transformation process, then static/still camera as soon as the silhouette is established\",\n    },\n    \"scene_motion\": {\n      \"lighting\": \"dynamic lighting that accentuates the metallic surfaces\",\n      \"atmosphere\": \"golden aura surrounding the vehicle as it morphs\"\n    },\n    \"style_descriptors\": {\n      \"quality\": \"ultra hyperreal, super crisp visuals\",\n      \"aesthetic\": \"high-end cinematic with vignette framing\",\n      \"mood\": \"immersive and elegant morphing between forms\"\n    }\n  }\n}\"```\n\n```json\n{\n  \"structured_motion_prompt\": {\n    \"subject_motion\": {\n      \"primary\": \"sports car silhouette morphs into golden logo silhouette\",\n      \"secondary\": \"smooth transformation highlighting aerodynamic curves and contours\",\n      \"detail\": \"precise linework evolves with metallic luminescence\"\n    },\n    \"camera_motion\": {\n      \"primary\": \"dynamic tracking shot following the initial transformation\",\n      \"secondary\": \"stabilizes to static position once silhouette completes forming\"\n    },\n    \"scene_motion\": {\n      \"environment\": \"minimalist background that emphasizes the transformation\",\n      \"lighting\": \"dynamic lights sweep across metallic surfaces during morphing\",\n      \"effects\": \"golden aura emanates and intensifies around the transforming vehicle\"\n    },\n    \"style_descriptors\": {\n      \"visual_quality\": \"ultra hyperreal with crisp definition\",\n      \"rendering\": \"CG transformer-style morph effect with precise transitions\",\n      \"mood\": \"elegant, high-end cinematic with subtle vignette framing\"\n    }\n  }\n}```",
      "responses": {
          "openrouter/anthropic/claude-3.7-sonnet:beta": {
          "model": "openrouter/anthropic/claude-3.7-sonnet:beta",
          "content": "```json\n{\n  \"optimized_keyword_prompt\": {\n    \"subject_motion\": {\n      \"primary\": \"sports car silhouette morphs into golden logo silhouette\",\n      \"secondary\": \"smooth transformation highlighting aerodynamic curves\",\n      \"detail\": \"precise linework with metallic sheen\"\n    },\n    \"camera_motion\": {\n      \"primary\": \"tracking shot\",\n      \"secondary\": \"locked-off shot once silhouette forms\"\n    },\n    \"scene_motion\": {\n      \"environment\": \"minimalist\",\n      \"lighting\": \"dramatic rim lighting\",\n      \"effects\": \"golden glow emanating from transforming vehicle\"\n    },\n    \"style_descriptors\": {\n      \"visual_quality\": \"hyperrealistic\",\n      \"rendering\": \"3D animation\",\n      \"mood\": \"cinematic, premium, vignette\"\n    }\n  }\n}\n```",
        }
      }
    },
      {
      "instruction": "# Runway Core Distiller\n\nYour goal is not to **change** but to **distill** to RunwayML's minimal syntax requirements: essential subject-action-camera descriptions with maximum conciseness while preserving visual impact. **Achieve Gen-4's preferred prompt simplicity.** Execute as:\n\n`{role=core_distiller; input=[optimized_keyword_prompt:str]; process=[distill_to_essential_elements(), maintain_subject_action_clarity(), preserve_camera_specifications(), ensure_maximum_conciseness(), validate_gen4_simplicity()]; constraints=[maximum_conciseness(), essential_elements_only(), gen4_simplicity_standard()]; requirements=[runway_minimal_syntax(), visual_impact_preservation(), prompt_efficiency()]; output={distilled_runway_prompt:str}}`",
      "step": "d",
      "title": "Runway Core Distiller",
      "input": "\n```[Initial Prompt]: \"INTENSITY AND AMPLIFY INSTRUCTIONS TO MORPH CG-TRANSFORMERS-EFFECT INSTEAD OF BAD TRANSITIONS\n\n{\n  \"structured_motion_prompt\": {\n    \"subject_motion\": {\n      \"primary\": \"sleek sports car silhouette transforming into a golden logo-like silhouette\",\n      \"details\": \"smooth morphing that highlights aerodynamic curves, fastback roofline, wheel arches, and front-end contours\",\n      \"emphasis\": \"precise linework transformation with metallic luminescence\"\n    },\n    \"camera_motion\": {\n      \"movement\": \"first dynamic flow tracking the initial transformation process, then static/still camera as soon as the silhouette is established\",\n    },\n    \"scene_motion\": {\n      \"lighting\": \"dynamic lighting that accentuates the metallic surfaces\",\n      \"atmosphere\": \"golden aura surrounding the vehicle as it morphs\"\n    },\n    \"style_descriptors\": {\n      \"quality\": \"ultra hyperreal, super crisp visuals\",\n      \"aesthetic\": \"high-end cinematic with vignette framing\",\n      \"mood\": \"immersive and elegant morphing between forms\"\n    }\n  }\n}\"```\n\n```json\n{\n  \"optimized_keyword_prompt\": {\n    \"subject_motion\": {\n      \"primary\": \"sports car silhouette morphs into golden logo silhouette\",\n      \"secondary\": \"smooth transformation highlighting aerodynamic curves\",\n      \"detail\": \"precise linework with metallic sheen\"\n    },\n    \"camera_motion\": {\n      \"primary\": \"tracking shot\",\n      \"secondary\": \"locked-off shot once silhouette forms\"\n    },\n    \"scene_motion\": {\n      \"environment\": \"minimalist\",\n      \"lighting\": \"dramatic rim lighting\",\n      \"effects\": \"golden glow emanating from transforming vehicle\"\n    },\n    \"style_descriptors\": {\n      \"visual_quality\": \"hyperrealistic\",\n      \"rendering\": \"3D animation\",\n      \"mood\": \"cinematic, premium, vignette\"\n    }\n  }\n}\n```",
      "responses": {
          "openrouter/anthropic/claude-3.7-sonnet:beta": {
          "model": "openrouter/anthropic/claude-3.7-sonnet:beta",
          "content": "```json\n{\n  \"distilled_runway_prompt\": \"Sports car silhouette transforms into golden logo with smooth metallic transitions. Dramatic lighting, cinematic vignette. Camera tracks transformation then locks when complete.\"\n}\n```",
        }
      }
    }