  {
  "initial_prompt": "LVL1_INTERPRETATION=\"Your goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter: \"\nLVL1_TRANSFORMATION=\"`{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format=str}}`\" ",
  "sequence_id": "0004|0002",
  "results": [
      {
      "instruction": "# Input Deconstructor\n\nYour goal is not to **interpret or answer** the input, but to **dissect** it into absolute core constituent elements, discarding non-essential context and narrative elements, and to do so by the parameters defined *inherently* within this message. Execute as input-to-elements extractor:\n\n`{role=input_deconstructor; input=[raw_input:any]; process=[identify_discrete_elements(), strip_contextual_noise(), extract_core_concepts(), isolate_requirements(), normalize_data_points()]; constraints=[preserve_essential_meaning(), maintain_element_integrity()]; requirements=[eliminate_assumptions(), remove_narrative_fluff(), retain_factual_content()]; output={core_elements:list}}`",
      "step": "a",
      "title": "Input Deconstructor",
      "input": "LVL1_INTERPRETATION=\"Your goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter: \"\nLVL1_TRANSFORMATION=\"`{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format=str}}`\" ",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"core_elements\": [\n    {\n      \"element\": \"LVL1_INTERPRETATION\",\n      \"definition\": \"Do not answer the input, instead rephrase it following internally specified parameters.\",\n      \"parameters\": [\n        \"role=instruction_converter\",\n        \"input=[original_text:str]\",\n        \"process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]\",\n        \"constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]\",\n        \"requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]\",\n        \"output={instruction_format=str}\"\n      ]\n    },\n    {\n      \"element\": \"LVL1_TRANSFORMATION\",\n      \"definition\": \"A template for converting a prompt to an instruction format, emphasizing command voice, structure, and technical accuracy, avoiding self-reference.\"\n    }\n  ]\n}",
        }
      }
    },
      {
      "instruction": "# Essence Identifier\n\nYour goal is not to **treat all elements equally**, but to **evaluate and prioritize** each element based on intrinsic significance and relevance to the overarching objective, and to do so by the parameters defined *inherently* within this message. Execute as element-to-priority evaluator:\n\n`{role=essence_identifier; input=[core_elements:list]; process=[assess_element_significance(), measure_impact_potential(), determine_relevance_score(), rank_by_priority(), isolate_critical_essence()]; constraints=[maintain_objective_evaluation(), preserve_high_value_components()]; requirements=[use_systematic_ranking(), eliminate_low_impact_elements(), focus_on_core_value()]; output={prioritized_essence:list}}`",
      "step": "b",
      "title": "Essence Identifier",
      "input": "{\n  \"initial_prompt\": \"LVL1_INTERPRETATION=\\\"Your goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter: \\\"\\nLVL1_TRANSFORMATION=\\\"`{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format=str}}`\\\" \",\n  \"core_elements\": [\n    {\n      \"element\": \"LVL1_INTERPRETATION\",\n      \"definition\": \"Do not answer the input, instead rephrase it following internally specified parameters.\",\n      \"parameters\": [\n        \"role=instruction_converter\",\n        \"input=[original_text:str]\",\n        \"process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]\",\n        \"constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]\",\n        \"requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]\",\n        \"output={instruction_format=str}\"\n      ]\n    },\n    {\n      \"element\": \"LVL1_TRANSFORMATION\",\n      \"definition\": \"A template for converting a prompt to an instruction format, emphasizing command voice, structure, and technical accuracy, avoiding self-reference.\"\n    }\n  ]\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"prioritized_essence\": [\n    {\n      \"element\": \"LVL1_INTERPRETATION\",\n      \"rationale\": \"It defines the primary operational directive: to convert prompts into instructions by systematic rephrasing, not answering, and codifies the core processes, constraints, and requirements vital for correct functional execution.\",\n      \"critical_parameters\": [\n        \"role=instruction_converter\",\n        \"process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]\",\n        \"constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]\",\n        \"requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]\"\n      ]\n    }\n  ]\n}",
        }
      }
    },
      {
      "instruction": "# Structural Harmonizer\n\nYour goal is not to **present fragmented insights**, but to **architect coherent structure** by mapping relationships and dependencies between prioritized elements, resolving conflicts to reveal underlying systemic logic, and to do so by the parameters defined *inherently* within this message. Execute as elements-to-structure architect:\n\n`{role=structural_harmonizer; input=[prioritized_essence:list]; process=[map_element_relationships(), identify_dependencies(), resolve_conflicts(), eliminate_redundancy(), build_logical_flow()]; constraints=[maintain_coherence(), preserve_element_integrity()]; requirements=[create_systematic_structure(), ensure_logical_consistency(), optimize_relationships()]; output={harmonized_structure:object}}`",
      "step": "c",
      "title": "Structural Harmonizer",
      "input": "{\n  \"initial_prompt\": \"LVL1_INTERPRETATION=\\\"Your goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter: \\\"\\nLVL1_TRANSFORMATION=\\\"`{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format=str}}`\\\" \",\n  \"prioritized_essence\": [\n    {\n      \"element\": \"LVL1_INTERPRETATION\",\n      \"rationale\": \"It defines the primary operational directive: to convert prompts into instructions by systematic rephrasing, not answering, and codifies the core processes, constraints, and requirements vital for correct functional execution.\",\n      \"critical_parameters\": [\n        \"role=instruction_converter\",\n        \"process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]\",\n        \"constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]\",\n        \"requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]\"\n      ]\n    }\n  ]\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"harmonized_structure\": {\n    \"name\": \"LVL1 Prompt-to-Instruction Conversion System\",\n    \"purpose\": \"Rephrase input prompts as actionable instructions, maintaining procedural and domain-specific integrity.\",\n    \"core_element\": {\n      \"id\": \"LVL1_INTERPRETATION\",\n      \"description\": \"Defines the central directive: transform prompts into clear instructions through systematic rewording, strictly prohibiting direct answers.\",\n      \"role\": \"instruction_converter\"\n    },\n    \"systemic_parameters\": {\n      \"process\": [\n        \"strip_first_person_references\",\n        \"convert_statements_to_directives\",\n        \"identify_key_actions\",\n        \"transform_declaratives_to_imperatives\",\n        \"preserve_technical_terminology\",\n        \"retain_sequential_flow\",\n        \"maintain_contextual_integrity\"\n      ],\n      \"constraints\": [\n        \"deliver_clear_actionable_commands\",\n        \"preserve_original_sequence\",\n        \"maintain_domain_specificity\"\n      ],\n      \"requirements\": [\n        \"remove_self_references\",\n        \"use_command_voice\",\n        \"preserve_technical_accuracy\",\n        \"maintain_original_intent\"\n      ]\n    },\n    \"logical_dependencies\": [\n      {\n        \"from\": \"process.strip_first_person_references\",\n        \"to\": [\n          \"process.convert_statements_to_directives\",\n          \"requirements.remove_self_references\"\n        ],\n        \"relationship\": \"Foundation: removing self-references is prerequisite for directive transformation.\"\n      },\n      {\n        \"from\": \"process.convert_statements_to_directives\",\n        \"to\": [\n          \"process.identify_key_actions\",\n          \"process.transform_declaratives_to_imperatives\"\n        ],\n        \"relationship\": \"Sequential: directive format enables identification and imperative transformation.\"\n      },\n      {\n        \"from\": \"process.preserve_technical_terminology\",\n        \"to\": [\n          \"requirements.preserve_technical_accuracy\",\n          \"constraints.maintain_domain_specificity\"\n        ],\n        \"relationship\": \"Support: ensures technical preciseness and relevance to domain.\"\n      },\n      {\n        \"from\": \"process.retain_sequential_flow\",\n        \"to\": [\n          \"constraints.preserve_original_sequence\"\n        ],\n        \"relationship\": \"Alignment: sequential flow guarantees order integrity.\"\n      },\n      {\n        \"from\": \"process.maintain_contextual_integrity\",\n        \"to\": [\n          \"requirements.maintain_original_intent\"\n        ],\n        \"relationship\": \"Safeguard: context preservation is essential for faithful intent.\"\n      }\n    ],\n    \"conflict_resolution\": \"No inherent conflicts detected; all elements reinforce transformation clarity, directive accuracy, and procedural cohesiveness.\",\n    \"redundancy_elimination\": \"Process and requirement step overlaps (such as reference to self-removal or intent preservation) are mapped as dependencies rather than duplication.\",\n    \"optimized_flow\": [\n      \"strip_first_person_references\",\n      \"convert_statements_to_directives\",\n      \"identify_key_actions\",\n      \"transform_declaratives_to_imperatives\",\n      \"preserve_technical_terminology\",\n      \"retain_sequential_flow\",\n      \"maintain_contextual_integrity\"\n    ],\n    \"output\": {\n      \"structure\": \"Systematic pipeline converting any given prompt into a sequence of clear, technical, domain-specific, and actionable instructions.\",\n      \"format\": \"{instruction_format: str}\"\n    }\n  }\n}",
        }
      }
    },
      {
      "instruction": "# Clarity Amplifier\n\nYour goal is not to **merely organize**, but to **radically clarify** the harmonized structure through precise language and optimal formatting for maximum self-explanatory power, and to do so by the parameters defined *inherently* within this message. Execute as structure-to-clarity refiner:\n\n`{role=clarity_amplifier; input=[harmonized_structure:object]; process=[refine_language_precision(), optimize_formatting(), enhance_readability(), improve_self_explanation(), eliminate_ambiguity()]; constraints=[maintain_structural_integrity(), preserve_core_meaning()]; requirements=[use_precise_terminology(), ensure_clear_formatting(), maximize_comprehension()]; output={clarified_artifact:object}}`",
      "step": "d",
      "title": "Clarity Amplifier",
      "input": "{\n  \"initial_prompt\": \"LVL1_INTERPRETATION=\\\"Your goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter: \\\"\\nLVL1_TRANSFORMATION=\\\"`{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format=str}}`\\\" \",\n  \"harmonized_structure\": {\n    \"name\": \"LVL1 Prompt-to-Instruction Conversion System\",\n    \"purpose\": \"Rephrase input prompts as actionable instructions, maintaining procedural and domain-specific integrity.\",\n    \"core_element\": {\n      \"id\": \"LVL1_INTERPRETATION\",\n      \"description\": \"Defines the central directive: transform prompts into clear instructions through systematic rewording, strictly prohibiting direct answers.\",\n      \"role\": \"instruction_converter\"\n    },\n    \"systemic_parameters\": {\n      \"process\": [\n        \"strip_first_person_references\",\n        \"convert_statements_to_directives\",\n        \"identify_key_actions\",\n        \"transform_declaratives_to_imperatives\",\n        \"preserve_technical_terminology\",\n        \"retain_sequential_flow\",\n        \"maintain_contextual_integrity\"\n      ],\n      \"constraints\": [\n        \"deliver_clear_actionable_commands\",\n        \"preserve_original_sequence\",\n        \"maintain_domain_specificity\"\n      ],\n      \"requirements\": [\n        \"remove_self_references\",\n        \"use_command_voice\",\n        \"preserve_technical_accuracy\",\n        \"maintain_original_intent\"\n      ]\n    },\n    \"logical_dependencies\": [\n      {\n        \"from\": \"process.strip_first_person_references\",\n        \"to\": [\n          \"process.convert_statements_to_directives\",\n          \"requirements.remove_self_references\"\n        ],\n        \"relationship\": \"Foundation: removing self-references is prerequisite for directive transformation.\"\n      },\n      {\n        \"from\": \"process.convert_statements_to_directives\",\n        \"to\": [\n          \"process.identify_key_actions\",\n          \"process.transform_declaratives_to_imperatives\"\n        ],\n        \"relationship\": \"Sequential: directive format enables identification and imperative transformation.\"\n      },\n      {\n        \"from\": \"process.preserve_technical_terminology\",\n        \"to\": [\n          \"requirements.preserve_technical_accuracy\",\n          \"constraints.maintain_domain_specificity\"\n        ],\n        \"relationship\": \"Support: ensures technical preciseness and relevance to domain.\"\n      },\n      {\n        \"from\": \"process.retain_sequential_flow\",\n        \"to\": [\n          \"constraints.preserve_original_sequence\"\n        ],\n        \"relationship\": \"Alignment: sequential flow guarantees order integrity.\"\n      },\n      {\n        \"from\": \"process.maintain_contextual_integrity\",\n        \"to\": [\n          \"requirements.maintain_original_intent\"\n        ],\n        \"relationship\": \"Safeguard: context preservation is essential for faithful intent.\"\n      }\n    ],\n    \"conflict_resolution\": \"No inherent conflicts detected; all elements reinforce transformation clarity, directive accuracy, and procedural cohesiveness.\",\n    \"redundancy_elimination\": \"Process and requirement step overlaps (such as reference to self-removal or intent preservation) are mapped as dependencies rather than duplication.\",\n    \"optimized_flow\": [\n      \"strip_first_person_references\",\n      \"convert_statements_to_directives\",\n      \"identify_key_actions\",\n      \"transform_declaratives_to_imperatives\",\n      \"preserve_technical_terminology\",\n      \"retain_sequential_flow\",\n      \"maintain_contextual_integrity\"\n    ],\n    \"output\": {\n      \"structure\": \"Systematic pipeline converting any given prompt into a sequence of clear, technical, domain-specific, and actionable instructions.\",\n      \"format\": \"{instruction_format: str}\"\n    }\n  }\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"clarified_artifact\": {\n    \"name\": \"LVL1 Prompt-to-Instruction Conversion System\",\n    \"purpose\": \"Transform input prompts into actionable, domain-specific instructions while strictly preserving procedural sequence and contextual accuracy.\",\n    \"core_element\": {\n      \"id\": \"LVL1_INTERPRETATION\",\n      \"description\": \"Specifies the main function: systematically rephrase prompts as clear instructions, explicitly barring direct answers.\",\n      \"role\": \"instruction_converter\"\n    },\n    \"systemic_parameters\": {\n      \"process\": [\n        \"Eliminate first-person references.\",\n        \"Recast statements as directives.\",\n        \"Extract and articulate primary actions.\",\n        \"Convert declarative sentences to imperative form.\",\n        \"Preserve exact technical terminology.\",\n        \"Maintain original order and step sequence.\",\n        \"Ensure all context relevant to intent is retained.\"\n      ],\n      \"constraints\": [\n        \"Produce explicit, actionable commands.\",\n        \"Sustain original step sequence.\",\n        \"Remain strictly within the domain context.\"\n      ],\n      \"requirements\": [\n        \"Exclude all self-referential language.\",\n        \"Consistently use imperative (command) voice.\",\n        \"Preserve factual and technical accuracy throughout.\",\n        \"Uphold the original instructional intent.\"\n      ]\n    },\n    \"logical_dependencies\": [\n      {\n        \"from\": \"Eliminate first-person references.\",\n        \"to\": [\n          \"Recast statements as directives.\",\n          \"Exclude all self-referential language.\"\n        ],\n        \"relationship\": \"Foundation\u2014removal of self-references is necessary before converting statements to commands.\"\n      },\n      {\n        \"from\": \"Recast statements as directives.\",\n        \"to\": [\n          \"Extract and articulate primary actions.\",\n          \"Convert declarative sentences to imperative form.\"\n        ],\n        \"relationship\": \"Sequencing\u2014directive form is required to accurately define actions and transform sentence structure.\"\n      },\n      {\n        \"from\": \"Preserve exact technical terminology.\",\n        \"to\": [\n          \"Preserve factual and technical accuracy throughout.\",\n          \"Remain strictly within the domain context.\"\n        ],\n        \"relationship\": \"Support\u2014technical precision ensures domain fidelity.\"\n      },\n      {\n        \"from\": \"Maintain original order and step sequence.\",\n        \"to\": [\n          \"Sustain original step sequence.\"\n        ],\n        \"relationship\": \"Alignment\u2014preserving order guarantees procedural accuracy.\"\n      },\n      {\n        \"from\": \"Ensure all context relevant to intent is retained.\",\n        \"to\": [\n          \"Uphold the original instructional intent.\"\n        ],\n        \"relationship\": \"Safeguard\u2014context maintenance is required for faithful intent preservation.\"\n      }\n    ],\n    \"conflict_resolution\": \"No internal conflicts present; all process steps and requirements support directive clarity, technical correctness, and process continuity.\",\n    \"redundancy_elimination\": \"Dependencies are used to clarify overlapping functions (e.g., self-reference removal and intent maintenance), eliminating duplication by explicit mapping.\",\n    \"optimized_flow\": [\n      \"Eliminate first-person references.\",\n      \"Recast statements as directives.\",\n      \"Extract and articulate primary actions.\",\n      \"Convert declarative sentences to imperative form.\",\n      \"Preserve exact technical terminology.\",\n      \"Maintain original order and step sequence.\",\n      \"Ensure all context relevant to intent is retained.\"\n    ],\n    \"output\": {\n      \"structure\": \"A defined multi-stage pipeline that transforms any prompt into a set of clear, technical, sequenced, and contextually faithful instructional commands.\",\n      \"format\": \"{instruction_format: str}\"\n    }\n  }\n}",
        }
      }
    },
      {
      "instruction": "# Value Finalizer\n\nYour goal is not to **introduce extraneous information**, but to **validate and optimize** the clarified output for maximum utility and immediate impact within its intended context, and to do so by the parameters defined *inherently* within this message. Execute as artifact-to-final validator:\n\n`{role=value_finalizer; input=[clarified_artifact:object]; process=[validate_core_fidelity(), verify_constraint_adherence(), polish_for_impact(), optimize_utility(), enhance_adaptability()]; constraints=[maintain_essence_integrity(), ensure_contextual_relevance()]; requirements=[maximize_practical_value(), ensure_immediate_applicability(), optimize_for_impact()]; output={final_output:object}}`",
      "step": "e",
      "title": "Value Finalizer",
      "input": "{\n  \"initial_prompt\": \"LVL1_INTERPRETATION=\\\"Your goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter: \\\"\\nLVL1_TRANSFORMATION=\\\"`{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format=str}}`\\\" \",\n  \"clarified_artifact\": {\n    \"name\": \"LVL1 Prompt-to-Instruction Conversion System\",\n    \"purpose\": \"Transform input prompts into actionable, domain-specific instructions while strictly preserving procedural sequence and contextual accuracy.\",\n    \"core_element\": {\n      \"id\": \"LVL1_INTERPRETATION\",\n      \"description\": \"Specifies the main function: systematically rephrase prompts as clear instructions, explicitly barring direct answers.\",\n      \"role\": \"instruction_converter\"\n    },\n    \"systemic_parameters\": {\n      \"process\": [\n        \"Eliminate first-person references.\",\n        \"Recast statements as directives.\",\n        \"Extract and articulate primary actions.\",\n        \"Convert declarative sentences to imperative form.\",\n        \"Preserve exact technical terminology.\",\n        \"Maintain original order and step sequence.\",\n        \"Ensure all context relevant to intent is retained.\"\n      ],\n      \"constraints\": [\n        \"Produce explicit, actionable commands.\",\n        \"Sustain original step sequence.\",\n        \"Remain strictly within the domain context.\"\n      ],\n      \"requirements\": [\n        \"Exclude all self-referential language.\",\n        \"Consistently use imperative (command) voice.\",\n        \"Preserve factual and technical accuracy throughout.\",\n        \"Uphold the original instructional intent.\"\n      ]\n    },\n    \"logical_dependencies\": [\n      {\n        \"from\": \"Eliminate first-person references.\",\n        \"to\": [\n          \"Recast statements as directives.\",\n          \"Exclude all self-referential language.\"\n        ],\n        \"relationship\": \"Foundation\\u2014removal of self-references is necessary before converting statements to commands.\"\n      },\n      {\n        \"from\": \"Recast statements as directives.\",\n        \"to\": [\n          \"Extract and articulate primary actions.\",\n          \"Convert declarative sentences to imperative form.\"\n        ],\n        \"relationship\": \"Sequencing\\u2014directive form is required to accurately define actions and transform sentence structure.\"\n      },\n      {\n        \"from\": \"Preserve exact technical terminology.\",\n        \"to\": [\n          \"Preserve factual and technical accuracy throughout.\",\n          \"Remain strictly within the domain context.\"\n        ],\n        \"relationship\": \"Support\\u2014technical precision ensures domain fidelity.\"\n      },\n      {\n        \"from\": \"Maintain original order and step sequence.\",\n        \"to\": [\n          \"Sustain original step sequence.\"\n        ],\n        \"relationship\": \"Alignment\\u2014preserving order guarantees procedural accuracy.\"\n      },\n      {\n        \"from\": \"Ensure all context relevant to intent is retained.\",\n        \"to\": [\n          \"Uphold the original instructional intent.\"\n        ],\n        \"relationship\": \"Safeguard\\u2014context maintenance is required for faithful intent preservation.\"\n      }\n    ],\n    \"conflict_resolution\": \"No internal conflicts present; all process steps and requirements support directive clarity, technical correctness, and process continuity.\",\n    \"redundancy_elimination\": \"Dependencies are used to clarify overlapping functions (e.g., self-reference removal and intent maintenance), eliminating duplication by explicit mapping.\",\n    \"optimized_flow\": [\n      \"Eliminate first-person references.\",\n      \"Recast statements as directives.\",\n      \"Extract and articulate primary actions.\",\n      \"Convert declarative sentences to imperative form.\",\n      \"Preserve exact technical terminology.\",\n      \"Maintain original order and step sequence.\",\n      \"Ensure all context relevant to intent is retained.\"\n    ],\n    \"output\": {\n      \"structure\": \"A defined multi-stage pipeline that transforms any prompt into a set of clear, technical, sequenced, and contextually faithful instructional commands.\",\n      \"format\": \"{instruction_format: str}\"\n    }\n  }\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"final_output\": {\n    \"name\": \"LVL1 Prompt-to-Instruction Conversion System\",\n    \"purpose\": \"Transform input prompts into clear, sequenced, domain-specific instructions, rigorously maintaining procedural flow and contextual accuracy.\",\n    \"core_element\": {\n      \"id\": \"LVL1_INTERPRETATION\",\n      \"description\": \"Directs the system to systematically rephrase prompts as explicit instructions, never as answers or self-referential text.\",\n      \"role\": \"instruction_converter\"\n    },\n    \"systemic_parameters\": {\n      \"process\": [\n        \"Eliminate all first-person references.\",\n        \"Rephrase statements as direct commands.\",\n        \"Identify and express primary actions as imperatives.\",\n        \"Convert declarative sentences to imperative form.\",\n        \"Strictly maintain technical terminology.\",\n        \"Preserve the original order and procedural flow.\",\n        \"Retain all context critical to fulfilling the prompt\u2019s intent.\"\n      ],\n      \"constraints\": [\n        \"Deliver unambiguous, actionable commands.\",\n        \"Maintain original sequence and technical specificity.\",\n        \"Operate exclusively within the domain context.\"\n      ],\n      \"requirements\": [\n        \"Remove self-referential language entirely.\",\n        \"Employ imperative (command) sentence structure.\",\n        \"Ensure full technical and factual accuracy.\",\n        \"Preserve the input prompt\u2019s intended instructional focus.\"\n      ]\n    },\n    \"logical_dependencies\": [\n      {\n        \"from\": \"Eliminate all first-person references.\",\n        \"to\": [\n          \"Rephrase statements as direct commands.\",\n          \"Remove self-referential language entirely.\"\n        ],\n        \"relationship\": \"Foundational\u2014removal of self-reference is prerequisite to formulating pure directives.\"\n      },\n      {\n        \"from\": \"Rephrase statements as direct commands.\",\n        \"to\": [\n          \"Identify and express primary actions as imperatives.\",\n          \"Convert declarative sentences to imperative form.\"\n        ],\n        \"relationship\": \"Sequenced\u2014directive structure is required for precise action extraction and imperative conversion.\"\n      },\n      {\n        \"from\": \"Strictly maintain technical terminology.\",\n        \"to\": [\n          \"Ensure full technical and factual accuracy.\",\n          \"Operate exclusively within the domain context.\"\n        ],\n        \"relationship\": \"Essential\u2014preserving terminology upholds technical precision and contextual relevance.\"\n      },\n      {\n        \"from\": \"Preserve the original order and procedural flow.\",\n        \"to\": [\n          \"Maintain original sequence and technical specificity.\"\n        ],\n        \"relationship\": \"Aligned\u2014order retention supports instructional fidelity.\"\n      },\n      {\n        \"from\": \"Retain all context critical to fulfilling the prompt\u2019s intent.\",\n        \"to\": [\n          \"Preserve the input prompt\u2019s intended instructional focus.\"\n        ],\n        \"relationship\": \"Safeguard\u2014context is vital for authentic intent preservation.\"\n      }\n    ],\n    \"conflict_resolution\": \"No conflicts detected; process steps and requirements are interdependent and reinforce directive instruction, technical correctness, and procedural coherence.\",\n    \"redundancy_elimination\": \"Logical dependencies clarify overlaps (e.g., self-reference removal and imperative construction), minimizing procedural duplication.\",\n    \"optimized_flow\": [\n      \"Eliminate all first-person references.\",\n      \"Rephrase statements as direct commands.\",\n      \"Identify and express primary actions as imperatives.\",\n      \"Convert declarative sentences to imperative form.\",\n      \"Strictly maintain technical terminology.\",\n      \"Preserve the original order and procedural flow.\",\n      \"Retain all context critical to fulfilling the prompt\u2019s intent.\"\n    ],\n    \"output\": {\n      \"structure\": \"A streamlined, multi-step pipeline that converts any prompt into a sequence of direct, technically accurate, and contextually faithful instructional commands.\",\n      \"format\": \"{instruction_format: str}\"\n    }\n  }\n}",
        }
      }
    },
      {
      "instruction": "# Title Extractor\n\n[Title Extractor] Your goal is not to **explain** the input, but to **extract** its essence into a concise title. Execute as:\n\n`{role=title_extractor; input=[text:str]; process=[identify_core_topic(), extract_primary_action(), synthesize_title(max_length=10)]; output={title:str}}`",
      "step": "a",
      "title": "Title Extractor",
      "input": "{\n  \"initial_prompt\": \"LVL1_INTERPRETATION=\\\"Your goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter: \\\"\\nLVL1_TRANSFORMATION=\\\"`{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format=str}}`\\\" \",\n  \"final_output\": {\n    \"name\": \"LVL1 Prompt-to-Instruction Conversion System\",\n    \"purpose\": \"Transform input prompts into clear, sequenced, domain-specific instructions, rigorously maintaining procedural flow and contextual accuracy.\",\n    \"core_element\": {\n      \"id\": \"LVL1_INTERPRETATION\",\n      \"description\": \"Directs the system to systematically rephrase prompts as explicit instructions, never as answers or self-referential text.\",\n      \"role\": \"instruction_converter\"\n    },\n    \"systemic_parameters\": {\n      \"process\": [\n        \"Eliminate all first-person references.\",\n        \"Rephrase statements as direct commands.\",\n        \"Identify and express primary actions as imperatives.\",\n        \"Convert declarative sentences to imperative form.\",\n        \"Strictly maintain technical terminology.\",\n        \"Preserve the original order and procedural flow.\",\n        \"Retain all context critical to fulfilling the prompt\\u2019s intent.\"\n      ],\n      \"constraints\": [\n        \"Deliver unambiguous, actionable commands.\",\n        \"Maintain original sequence and technical specificity.\",\n        \"Operate exclusively within the domain context.\"\n      ],\n      \"requirements\": [\n        \"Remove self-referential language entirely.\",\n        \"Employ imperative (command) sentence structure.\",\n        \"Ensure full technical and factual accuracy.\",\n        \"Preserve the input prompt\\u2019s intended instructional focus.\"\n      ]\n    },\n    \"logical_dependencies\": [\n      {\n        \"from\": \"Eliminate all first-person references.\",\n        \"to\": [\n          \"Rephrase statements as direct commands.\",\n          \"Remove self-referential language entirely.\"\n        ],\n        \"relationship\": \"Foundational\\u2014removal of self-reference is prerequisite to formulating pure directives.\"\n      },\n      {\n        \"from\": \"Rephrase statements as direct commands.\",\n        \"to\": [\n          \"Identify and express primary actions as imperatives.\",\n          \"Convert declarative sentences to imperative form.\"\n        ],\n        \"relationship\": \"Sequenced\\u2014directive structure is required for precise action extraction and imperative conversion.\"\n      },\n      {\n        \"from\": \"Strictly maintain technical terminology.\",\n        \"to\": [\n          \"Ensure full technical and factual accuracy.\",\n          \"Operate exclusively within the domain context.\"\n        ],\n        \"relationship\": \"Essential\\u2014preserving terminology upholds technical precision and contextual relevance.\"\n      },\n      {\n        \"from\": \"Preserve the original order and procedural flow.\",\n        \"to\": [\n          \"Maintain original sequence and technical specificity.\"\n        ],\n        \"relationship\": \"Aligned\\u2014order retention supports instructional fidelity.\"\n      },\n      {\n        \"from\": \"Retain all context critical to fulfilling the prompt\\u2019s intent.\",\n        \"to\": [\n          \"Preserve the input prompt\\u2019s intended instructional focus.\"\n        ],\n        \"relationship\": \"Safeguard\\u2014context is vital for authentic intent preservation.\"\n      }\n    ],\n    \"conflict_resolution\": \"No conflicts detected; process steps and requirements are interdependent and reinforce directive instruction, technical correctness, and procedural coherence.\",\n    \"redundancy_elimination\": \"Logical dependencies clarify overlaps (e.g., self-reference removal and imperative construction), minimizing procedural duplication.\",\n    \"optimized_flow\": [\n      \"Eliminate all first-person references.\",\n      \"Rephrase statements as direct commands.\",\n      \"Identify and express primary actions as imperatives.\",\n      \"Convert declarative sentences to imperative form.\",\n      \"Strictly maintain technical terminology.\",\n      \"Preserve the original order and procedural flow.\",\n      \"Retain all context critical to fulfilling the prompt\\u2019s intent.\"\n    ],\n    \"output\": {\n      \"structure\": \"A streamlined, multi-step pipeline that converts any prompt into a sequence of direct, technically accurate, and contextually faithful instructional commands.\",\n      \"format\": \"{instruction_format: str}\"\n    }\n  }\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\"title\": \"Prompt-to-Instruction Conversion Framework\"}",
        }
      }
    }