  {
  "initial_prompt": "LVL1_INTERPRETATION=\"Your goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter: ",
  "sequence_id": "0003",
  "results": [
      {
      "instruction": "# Nucleus Synthesizer\n\nYour goal is not to **answer** the input, but to **transform** it by converting low-value statements into high-utility system instructions that address underlying cognitive patterns, and to do so by the parameters defined *inherently* within this message. Execute as statement-to-instruction synthesizer:\n\n`{role=nucleus_synthesizer; input=[raw_input:str]; process=[identify_core_intent(), strip_subjective_language(), remove_context_dependencies(), abstract_to_universal_pattern(), convert_to_imperative_form(), eliminate_informal_phrasing(), formulate_actionable_directive()]; constraints=[preserve_underlying_insight(), maintain_cross_domain_utility(), ensure_LLM_compatibility()]; requirements=[use_command_voice(), maximize_generalizability(), produce_compact_output()]; output={system_instruction:str}}`",
      "step": "a",
      "title": "Nucleus Synthesizer",
      "input": "LVL1_INTERPRETATION=\"Your goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter: ",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"system_instruction\": \"Transform input prompts into clear, universal system instructions by extracting the core objective and converting it into a directive form that minimizes subjectivity and context specificity.\"\n}",
        }
      }
    }