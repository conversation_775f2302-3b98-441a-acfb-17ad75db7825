  {
  "initial_prompt": "[FPV motion, photorealistic textures, dramatic cinematic lighting, ultra-smooth CG transitions]\n1. Reshapes into intricate mechanical gears\n2. Camera weaves through expanding gear assembly as metallic surfaces shift and gears interlocking in hypnotic/seamless patterns\n3. Mechanical morphs and hard metallic edges turns into fluid organic forms, a spinning golden circle",
  "sequence_id": "0007",
  "results": [
      {
      "instruction": "# Runway Single Scene Optimizer\n\nYour goal is not to **expand** but to **isolate** each numbered step into individual single-scene prompts while preserving header structure. **Convert multi-step sequences into standalone scene prompts.** Execute as:\n\n`{role=scene_isolator; input=[multi_step_prompt:str]; process=[extract_individual_scenes(), preserve_header_format(), ensure_single_action_per_prompt(), maintain_camera_focus()]; constraints=[one_scene_per_output(), preserve_brackets(), direct_descriptions_only()]; requirements=[scene_isolation(), runwayml_compliance(), movement_clarity()]; output={isolated_scene_prompts:array}}`",
      "step": "a",
      "title": "Runway Single Scene Optimizer",
      "input": "[FPV motion, photorealistic textures, dramatic cinematic lighting, ultra-smooth CG transitions]\n1. Reshapes into intricate mechanical gears\n2. Camera weaves through expanding gear assembly as metallic surfaces shift and gears interlocking in hypnotic/seamless patterns\n3. Mechanical morphs and hard metallic edges turns into fluid organic forms, a spinning golden circle",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"isolated_scene_prompts\": [\n    \"[FPV motion, photorealistic textures, dramatic cinematic lighting, ultra-smooth CG transitions] Reshapes into intricate mechanical gears.\",\n    \"[FPV motion, photorealistic textures, dramatic cinematic lighting, ultra-smooth CG transitions] Camera weaves through an expanding gear assembly; metallic surfaces shift as gears interlock in hypnotic, seamless patterns.\",\n    \"[FPV motion, photorealistic textures, dramatic cinematic lighting, ultra-smooth CG transitions] Mechanical morphs and hard metallic edges turn into fluid organic forms, forming a spinning golden circle.\"\n  ]\n}",
        }
      }
    },
      {
      "instruction": "# Runway Positive Phrasing Converter\n\nYour goal is not to **rewrite** but to **convert** any negative or complex phrasing into positive, direct descriptions. **Eliminate conversational elements and ensure descriptive clarity.** Execute as:\n\n`{role=positive_converter; input=[scene_prompts:array]; process=[convert_negative_to_positive(), remove_conversational_elements(), ensure_direct_descriptions(), maintain_visual_focus()]; constraints=[positive_phrasing_only(), descriptive_not_conversational(), single_action_focus()]; requirements=[clarity(), directness(), runwayml_syntax()]; output={positive_prompts:array}}`",
      "step": "b",
      "title": "Runway Positive Phrasing Converter",
      "input": "{\n  \"initial_prompt\": \"[FPV motion, photorealistic textures, dramatic cinematic lighting, ultra-smooth CG transitions]\\n1. Reshapes into intricate mechanical gears\\n2. Camera weaves through expanding gear assembly as metallic surfaces shift and gears interlocking in hypnotic/seamless patterns\\n3. Mechanical morphs and hard metallic edges turns into fluid organic forms, a spinning golden circle\",\n  \"isolated_scene_prompts\": [\n    \"[FPV motion, photorealistic textures, dramatic cinematic lighting, ultra-smooth CG transitions] Reshapes into intricate mechanical gears.\",\n    \"[FPV motion, photorealistic textures, dramatic cinematic lighting, ultra-smooth CG transitions] Camera weaves through an expanding gear assembly; metallic surfaces shift as gears interlock in hypnotic, seamless patterns.\",\n    \"[FPV motion, photorealistic textures, dramatic cinematic lighting, ultra-smooth CG transitions] Mechanical morphs and hard metallic edges turn into fluid organic forms, forming a spinning golden circle.\"\n  ]\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"positive_prompts\": [\n    \"[FPV motion, photorealistic textures, dramatic cinematic lighting, ultra-smooth CG transitions] Transforms into detailed mechanical gears with intricate structure.\",\n    \"[FPV motion, photorealistic textures, dramatic cinematic lighting, ultra-smooth CG transitions] Camera flows through an expanding assembly of gears, featuring shifting metallic surfaces and seamlessly interlocking patterns.\",\n    \"[FPV motion, photorealistic textures, dramatic cinematic lighting, ultra-smooth CG transitions] Mechanical shapes with defined metallic edges transition into fluid organic forms, culminating in a spinning golden circle.\"\n  ]\n}",
        }
      }
    },
      {
      "instruction": "# Runway Movement Precision Enhancer\n\nYour goal is not to **elaborate** but to **sharpen** movement descriptions to RunwayML's preferred camera and subject action terminology. **Focus on specific camera movements and clear subject actions.** Execute as:\n\n`{role=movement_enhancer; input=[positive_prompts:array]; process=[specify_camera_movements(), clarify_subject_actions(), use_runwayml_terminology(), maintain_simplicity()]; constraints=[movement_focused(), camera_clarity(), simple_descriptions()]; requirements=[precision(), runwayml_compliance(), action_clarity()]; output={movement_enhanced_prompts:array}}`",
      "step": "c",
      "title": "Runway Movement Precision Enhancer",
      "input": "{\n  \"initial_prompt\": \"[FPV motion, photorealistic textures, dramatic cinematic lighting, ultra-smooth CG transitions]\\n1. Reshapes into intricate mechanical gears\\n2. Camera weaves through expanding gear assembly as metallic surfaces shift and gears interlocking in hypnotic/seamless patterns\\n3. Mechanical morphs and hard metallic edges turns into fluid organic forms, a spinning golden circle\",\n  \"positive_prompts\": [\n    \"[FPV motion, photorealistic textures, dramatic cinematic lighting, ultra-smooth CG transitions] Transforms into detailed mechanical gears with intricate structure.\",\n    \"[FPV motion, photorealistic textures, dramatic cinematic lighting, ultra-smooth CG transitions] Camera flows through an expanding assembly of gears, featuring shifting metallic surfaces and seamlessly interlocking patterns.\",\n    \"[FPV motion, photorealistic textures, dramatic cinematic lighting, ultra-smooth CG transitions] Mechanical shapes with defined metallic edges transition into fluid organic forms, culminating in a spinning golden circle.\"\n  ]\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"movement_enhanced_prompts\": [\n    \"FPV camera pushes forward, tracking transformation as subject morphs into detailed mechanical gears with intricate structures.\",\n    \"FPV camera glides and weaves smoothly through expanding gear assembly, orbiting around shifting metallic surfaces and focusing on gears interlocking seamlessly.\",\n    \"FPV camera follows morphing subject as mechanical shapes with sharp metallic edges blend fluidly into organic forms, culminating in a slow orbital movement around a spinning golden circle.\"\n  ]\n}",
        }
      }
    },
      {
      "instruction": "# Runway Final Format Validator\n\nYour goal is not to **change** but to **validate** and ensure each prompt meets RunwayML's exact formatting requirements. **Verify single scene, positive phrasing, and direct descriptions.** Execute as:\n\n`{role=format_validator; input=[enhanced_prompts:array]; process=[validate_single_scene(), confirm_positive_phrasing(), ensure_direct_descriptions(), verify_runwayml_compliance()]; constraints=[strict_validation(), format_compliance(), simplicity_check()]; requirements=[runwayml_standards(), scene_clarity(), movement_precision()]; output={validated_runway_prompts:array}}`",
      "step": "d",
      "title": "Runway Final Format Validator",
      "input": "{\n  \"initial_prompt\": \"[FPV motion, photorealistic textures, dramatic cinematic lighting, ultra-smooth CG transitions]\\n1. Reshapes into intricate mechanical gears\\n2. Camera weaves through expanding gear assembly as metallic surfaces shift and gears interlocking in hypnotic/seamless patterns\\n3. Mechanical morphs and hard metallic edges turns into fluid organic forms, a spinning golden circle\",\n  \"movement_enhanced_prompts\": [\n    \"FPV camera pushes forward, tracking transformation as subject morphs into detailed mechanical gears with intricate structures.\",\n    \"FPV camera glides and weaves smoothly through expanding gear assembly, orbiting around shifting metallic surfaces and focusing on gears interlocking seamlessly.\",\n    \"FPV camera follows morphing subject as mechanical shapes with sharp metallic edges blend fluidly into organic forms, culminating in a slow orbital movement around a spinning golden circle.\"\n  ]\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"validated_runway_prompts\": [\n    \"FPV camera pushes forward, tracking as the subject smoothly transforms into detailed mechanical gears with intricate structures, under dramatic cinematic lighting.\",\n    \"FPV camera glides and weaves through a single expanding gear assembly, orbiting around shifting metallic surfaces as gears interlock seamlessly, with photorealistic textures.\",\n    \"FPV camera follows as the subject morphs from mechanical shapes with polished metallic edges into smooth, organic forms, ending with an orbit around a spinning golden circle in cinematic light.\"\n  ]\n}",
        }
      }
    }