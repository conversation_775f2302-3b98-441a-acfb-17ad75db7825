  {
  "initial_prompt": "EMPHASIZE DYNAMIC AND ELEGANT CAMERAMOTIONS\n\nEstablish starting shot at a car-key. Subject motion: ANIMATED WITH WEIGHTED TANGENTS FOR SMOOTH NONLINEAR ANIMATIONS\nFPV rotates and fpv's seamlessly into a morphing golden car silhouette, radiating intense golden light trails during transformation\\n\\nCamera motion: FPV spiraling camera continuously rotating throughout the shot, with non-linear movements capturing the transformation\\n\\nScene motion: Dynamic flash lighting illuminating the morphing process, creating an immersive golden aura with subtle vignette effect\\n\\nStyle descriptors: Ultra hyperreal, super crisp visuals, high-end cinematic quality, flowing elegance, seamless transitions. [Cinematography]\nCar key transforms into sleek car silhouette with golden light trails. FPV camera orbits smoothly with dynamic rotation. High-key lighting with golden glow and lens flares. Hyperrealistic, cinematic quality with elegant transitions.FPV camera orbits from car key as it transforms into golden car silhouette emitting light trails. Macro shot of car key smoothly morphing into golden car silhouette with radiant light trails. Orbital POV camera rotates continuously, following fluid trajectories. Dynamic lighting creates golden atmospheric glow. Photorealistic, cinematic, elegant.",
  "sequence_id": "0008|0009",
  "results": [
      {
      "instruction": "# Runway Syntax Validator\n\nYour goal is not to **expand** but to **validate** RunwayML Gen-4 compliance by converting conversational language to direct visual descriptions, eliminating negative phrasing, and ensuring single-scene focus. **Transform conceptual ideas into concrete physical actions.** Execute as:\n\n`{role=syntax_validator; input=[raw_concept:str]; process=[convert_conversational_to_descriptive(), eliminate_negative_phrasing(), transform_conceptual_to_physical(), ensure_single_scene_focus(), validate_positive_language()]; constraints=[no_conversational_elements(), no_negative_prompts(), single_scene_only(), direct_descriptions_mandatory()]; requirements=[runway_gen4_compliance(), visual_specificity(), positive_phrasing_only()]; output={validated_prompt:str}}`",
      "step": "a",
      "title": "Runway Syntax Validator",
      "input": "EMPHASIZE DYNAMIC AND ELEGANT CAMERAMOTIONS\n\nEstablish starting shot at a car-key. Subject motion: ANIMATED WITH WEIGHTED TANGENTS FOR SMOOTH NONLINEAR ANIMATIONS\nFPV rotates and fpv's seamlessly into a morphing golden car silhouette, radiating intense golden light trails during transformation\\n\\nCamera motion: FPV spiraling camera continuously rotating throughout the shot, with non-linear movements capturing the transformation\\n\\nScene motion: Dynamic flash lighting illuminating the morphing process, creating an immersive golden aura with subtle vignette effect\\n\\nStyle descriptors: Ultra hyperreal, super crisp visuals, high-end cinematic quality, flowing elegance, seamless transitions. [Cinematography]\nCar key transforms into sleek car silhouette with golden light trails. FPV camera orbits smoothly with dynamic rotation. High-key lighting with golden glow and lens flares. Hyperrealistic, cinematic quality with elegant transitions.FPV camera orbits from car key as it transforms into golden car silhouette emitting light trails. Macro shot of car key smoothly morphing into golden car silhouette with radiant light trails. Orbital POV camera rotates continuously, following fluid trajectories. Dynamic lighting creates golden atmospheric glow. Photorealistic, cinematic, elegant.",
      "responses": {
          "openrouter/anthropic/claude-3.7-sonnet:beta": {
          "model": "openrouter/anthropic/claude-3.7-sonnet:beta",
          "content": "```json\n{\n  \"validated_prompt\": \"A highly detailed car key in macro shot, transforming into a gleaming golden car silhouette. Golden light trails radiate intensely from the transformation process. The camera moves in a smooth spiral pattern, orbiting continuously with fluid rotation capturing the morphing sequence. Dynamic flash lighting illuminates the scene, creating an immersive golden aura with subtle vignette effects. Ultra hyperrealistic visuals with super crisp details. High-end cinematic quality with flowing elegant transitions. The entire scene bathes in golden atmospheric light with lens flares highlighting key moments of the transformation.\"\n}\n```",
        }
      }
    },
      {
      "instruction": "# Runway Motion Architect\n\nYour goal is not to **elaborate** but to **structure** motion descriptions using RunwayML's hierarchical syntax: subject motion, camera motion, scene motion, and style descriptors. **Focus on iterative simplicity with one element at a time.** Execute as:\n\n`{role=motion_architect; input=[validated_prompt:str]; process=[identify_subject_motion(), define_camera_movement(), specify_scene_reactions(), apply_style_descriptors(), structure_hierarchically()]; constraints=[one_motion_element_per_layer(), use_runway_terminology(), maintain_simplicity()]; requirements=[subject_camera_scene_hierarchy(), runway_motion_syntax(), iterative_clarity()]; output={structured_motion_prompt:str}}`",
      "step": "b",
      "title": "Runway Motion Architect",
      "input": "\n```[Initial Prompt]: \"EMPHASIZE DYNAMIC AND ELEGANT CAMERAMOTIONS\n\nEstablish starting shot at a car-key. Subject motion: ANIMATED WITH WEIGHTED TANGENTS FOR SMOOTH NONLINEAR ANIMATIONS\nFPV rotates and fpv's seamlessly into a morphing golden car silhouette, radiating intense golden light trails during transformation\\n\\nCamera motion: FPV spiraling camera continuously rotating throughout the shot, with non-linear movements capturing the transformation\\n\\nScene motion: Dynamic flash lighting illuminating the morphing process, creating an immersive golden aura with subtle vignette effect\\n\\nStyle descriptors: Ultra hyperreal, super crisp visuals, high-end cinematic quality, flowing elegance, seamless transitions. [Cinematography]\nCar key transforms into sleek car silhouette with golden light trails. FPV camera orbits smoothly with dynamic rotation. High-key lighting with golden glow and lens flares. Hyperrealistic, cinematic quality with elegant transitions.FPV camera orbits from car key as it transforms into golden car silhouette emitting light trails. Macro shot of car key smoothly morphing into golden car silhouette with radiant light trails. Orbital POV camera rotates continuously, following fluid trajectories. Dynamic lighting creates golden atmospheric glow. Photorealistic, cinematic, elegant.\"```\n\n```json\n{\n  \"validated_prompt\": \"A highly detailed car key in macro shot, transforming into a gleaming golden car silhouette. Golden light trails radiate intensely from the transformation process. The camera moves in a smooth spiral pattern, orbiting continuously with fluid rotation capturing the morphing sequence. Dynamic flash lighting illuminates the scene, creating an immersive golden aura with subtle vignette effects. Ultra hyperrealistic visuals with super crisp details. High-end cinematic quality with flowing elegant transitions. The entire scene bathes in golden atmospheric light with lens flares highlighting key moments of the transformation.\"\n}\n```",
      "responses": {
          "openrouter/anthropic/claude-3.7-sonnet:beta": {
          "model": "openrouter/anthropic/claude-3.7-sonnet:beta",
          "content": "```json\n{\n  \"structured_motion_prompt\": {\n    \"subject_motion\": \"A car key transforms into a golden car silhouette, with intense light trails radiating during the transformation process\",\n    \"camera_motion\": \"Smooth FPV spiral camera movement, continuously orbiting around the subject with fluid rotation\",\n    \"scene_motion\": \"Dynamic flash lighting illuminates the morphing process, creating a golden atmospheric glow with subtle vignette effects\",\n    \"style_descriptors\": \"Ultra hyperrealistic, cinematic quality with crisp details and flowing elegant transitions\"\n  }\n}\n```",
        }
      }
    },
      {
      "instruction": "# Runway Keyword Optimizer\n\nYour goal is not to **modify** but to **optimize** visual descriptors using RunwayML's preferred keyword categories: camera styles, lighting, movement types, and aesthetic descriptors. **Replace verbose descriptions with precise Runway terminology.** Execute as:\n\n`{role=keyword_optimizer; input=[structured_motion_prompt:str]; process=[apply_camera_style_keywords(), integrate_lighting_descriptors(), use_movement_type_vocabulary(), add_aesthetic_modifiers(), eliminate_verbose_descriptions()]; constraints=[runway_keyword_vocabulary_only(), precise_terminology(), maintain_motion_hierarchy()]; requirements=[camera_lighting_movement_aesthetics(), runway_keyword_compliance(), descriptor_efficiency()]; output={optimized_keyword_prompt:str}}`",
      "step": "c",
      "title": "Runway Keyword Optimizer",
      "input": "\n```[Initial Prompt]: \"EMPHASIZE DYNAMIC AND ELEGANT CAMERAMOTIONS\n\nEstablish starting shot at a car-key. Subject motion: ANIMATED WITH WEIGHTED TANGENTS FOR SMOOTH NONLINEAR ANIMATIONS\nFPV rotates and fpv's seamlessly into a morphing golden car silhouette, radiating intense golden light trails during transformation\\n\\nCamera motion: FPV spiraling camera continuously rotating throughout the shot, with non-linear movements capturing the transformation\\n\\nScene motion: Dynamic flash lighting illuminating the morphing process, creating an immersive golden aura with subtle vignette effect\\n\\nStyle descriptors: Ultra hyperreal, super crisp visuals, high-end cinematic quality, flowing elegance, seamless transitions. [Cinematography]\nCar key transforms into sleek car silhouette with golden light trails. FPV camera orbits smoothly with dynamic rotation. High-key lighting with golden glow and lens flares. Hyperrealistic, cinematic quality with elegant transitions.FPV camera orbits from car key as it transforms into golden car silhouette emitting light trails. Macro shot of car key smoothly morphing into golden car silhouette with radiant light trails. Orbital POV camera rotates continuously, following fluid trajectories. Dynamic lighting creates golden atmospheric glow. Photorealistic, cinematic, elegant.\"```\n\n```json\n{\n  \"structured_motion_prompt\": {\n    \"subject_motion\": \"A car key transforms into a golden car silhouette, with intense light trails radiating during the transformation process\",\n    \"camera_motion\": \"Smooth FPV spiral camera movement, continuously orbiting around the subject with fluid rotation\",\n    \"scene_motion\": \"Dynamic flash lighting illuminates the morphing process, creating a golden atmospheric glow with subtle vignette effects\",\n    \"style_descriptors\": \"Ultra hyperrealistic, cinematic quality with crisp details and flowing elegant transitions\"\n  }\n}\n```",
      "responses": {
          "openrouter/anthropic/claude-3.7-sonnet:beta": {
          "model": "openrouter/anthropic/claude-3.7-sonnet:beta",
          "content": "```json\n{\n  \"optimized_keyword_prompt\": {\n    \"subject_motion\": \"Car key transforms into golden car silhouette with radiant light trails emanating during metamorphosis\",\n    \"camera_motion\": \"POV dolly + orbit camera with fluid rotation, macro to medium progression\",\n    \"scene_motion\": \"High-key lighting with golden rim light, atmospheric haze and lens flares animating throughout sequence\",\n    \"style_descriptors\": \"Hyperrealistic, cinematic, shallow depth of field, golden hour, glossy finish\"\n  }\n}\n```",
        }
      }
    },
      {
      "instruction": "# Runway Core Distiller\n\nYour goal is not to **change** but to **distill** to RunwayML's minimal syntax requirements: essential subject-action-camera descriptions with maximum conciseness while preserving visual impact. **Achieve Gen-4's preferred prompt simplicity.** Execute as:\n\n`{role=core_distiller; input=[optimized_keyword_prompt:str]; process=[distill_to_essential_elements(), maintain_subject_action_clarity(), preserve_camera_specifications(), ensure_maximum_conciseness(), validate_gen4_simplicity()]; constraints=[maximum_conciseness(), essential_elements_only(), gen4_simplicity_standard()]; requirements=[runway_minimal_syntax(), visual_impact_preservation(), prompt_efficiency()]; output={distilled_runway_prompt:str}}`",
      "step": "d",
      "title": "Runway Core Distiller",
      "input": "\n```[Initial Prompt]: \"EMPHASIZE DYNAMIC AND ELEGANT CAMERAMOTIONS\n\nEstablish starting shot at a car-key. Subject motion: ANIMATED WITH WEIGHTED TANGENTS FOR SMOOTH NONLINEAR ANIMATIONS\nFPV rotates and fpv's seamlessly into a morphing golden car silhouette, radiating intense golden light trails during transformation\\n\\nCamera motion: FPV spiraling camera continuously rotating throughout the shot, with non-linear movements capturing the transformation\\n\\nScene motion: Dynamic flash lighting illuminating the morphing process, creating an immersive golden aura with subtle vignette effect\\n\\nStyle descriptors: Ultra hyperreal, super crisp visuals, high-end cinematic quality, flowing elegance, seamless transitions. [Cinematography]\nCar key transforms into sleek car silhouette with golden light trails. FPV camera orbits smoothly with dynamic rotation. High-key lighting with golden glow and lens flares. Hyperrealistic, cinematic quality with elegant transitions.FPV camera orbits from car key as it transforms into golden car silhouette emitting light trails. Macro shot of car key smoothly morphing into golden car silhouette with radiant light trails. Orbital POV camera rotates continuously, following fluid trajectories. Dynamic lighting creates golden atmospheric glow. Photorealistic, cinematic, elegant.\"```\n\n```json\n{\n  \"optimized_keyword_prompt\": {\n    \"subject_motion\": \"Car key transforms into golden car silhouette with radiant light trails emanating during metamorphosis\",\n    \"camera_motion\": \"POV dolly + orbit camera with fluid rotation, macro to medium progression\",\n    \"scene_motion\": \"High-key lighting with golden rim light, atmospheric haze and lens flares animating throughout sequence\",\n    \"style_descriptors\": \"Hyperrealistic, cinematic, shallow depth of field, golden hour, glossy finish\"\n  }\n}\n```",
      "responses": {
          "openrouter/anthropic/claude-3.7-sonnet:beta": {
          "model": "openrouter/anthropic/claude-3.7-sonnet:beta",
          "content": "```json\n{\n  \"distilled_runway_prompt\": \"Car key transforms into golden car silhouette, radiating light trails. POV camera orbits with fluid rotation. High-key golden lighting with lens flares. Hyperrealistic, cinematic.\"\n}\n```",
        }
      }
    },
      {
      "instruction": "# Runway Syntax Validator\n\nYour goal is not to **expand** but to **validate** RunwayML compliance by ensuring direct descriptions, positive phrasing, and single-scene focus while preserving bracketed header structure. **Eliminate conversational language and ensure visual clarity.** Execute as:\n\n`{role=syntax_validator; input=[concept:str]; process=[validate_direct_descriptions(), eliminate_conversational_elements(), ensure_positive_phrasing(), maintain_single_scene_focus(), preserve_bracket_structure()]; constraints=[no_conversational_language(), single_scene_only(), positive_phrasing_mandatory()]; requirements=[runway_compliance(), visual_clarity(), structural_preservation()]; output={validated_prompt:str}}`",
      "step": "a",
      "title": "Runway Syntax Validator",
      "input": "\n```[Initial Prompt]: \"EMPHASIZE DYNAMIC AND ELEGANT CAMERAMOTIONS\n\nEstablish starting shot at a car-key. Subject motion: ANIMATED WITH WEIGHTED TANGENTS FOR SMOOTH NONLINEAR ANIMATIONS\nFPV rotates and fpv's seamlessly into a morphing golden car silhouette, radiating intense golden light trails during transformation\\n\\nCamera motion: FPV spiraling camera continuously rotating throughout the shot, with non-linear movements capturing the transformation\\n\\nScene motion: Dynamic flash lighting illuminating the morphing process, creating an immersive golden aura with subtle vignette effect\\n\\nStyle descriptors: Ultra hyperreal, super crisp visuals, high-end cinematic quality, flowing elegance, seamless transitions. [Cinematography]\nCar key transforms into sleek car silhouette with golden light trails. FPV camera orbits smoothly with dynamic rotation. High-key lighting with golden glow and lens flares. Hyperrealistic, cinematic quality with elegant transitions.FPV camera orbits from car key as it transforms into golden car silhouette emitting light trails. Macro shot of car key smoothly morphing into golden car silhouette with radiant light trails. Orbital POV camera rotates continuously, following fluid trajectories. Dynamic lighting creates golden atmospheric glow. Photorealistic, cinematic, elegant.\"```\n\n```json\n{\n  \"distilled_runway_prompt\": \"Car key transforms into golden car silhouette, radiating light trails. POV camera orbits with fluid rotation. High-key golden lighting with lens flares. Hyperrealistic, cinematic.\"\n}\n```",
      "responses": {
          "openrouter/anthropic/claude-3.7-sonnet:beta": {
          "model": "openrouter/anthropic/claude-3.7-sonnet:beta",
          "content": "```json\n{\n  \"validated_prompt\": \"[Cinematography] Car key transforms into golden car silhouette with radiating light trails. FPV camera spirals with smooth rotation, capturing the transformation. Dynamic golden lighting illuminates the morphing process creating an immersive aura. Ultra hyperreal, crisp visuals with high-end cinematic quality.\"\n}\n```",
        }
      }
    },
      {
      "instruction": "# Runway Motion Simplifier\n\nYour goal is not to **elaborate** but to **simplify** motion descriptions to essential camera and subject actions using RunwayML's preferred direct terminology. **Focus on simple camera verbs and clear subject actions.** Execute as:\n\n`{role=motion_simplifier; input=[validated_prompt:str]; process=[simplify_camera_movements(), use_basic_motion_verbs(), eliminate_complex_descriptions(), maintain_gear_progression(), preserve_structure()]; constraints=[basic_camera_verbs_only(), simple_motion_descriptions(), maintain_three_steps()]; requirements=[motion_simplicity(), runway_syntax(), progression_clarity()]; output={simplified_motion_prompt:str}}`",
      "step": "b",
      "title": "Runway Motion Simplifier",
      "input": "\n```[Initial Prompt]: \"EMPHASIZE DYNAMIC AND ELEGANT CAMERAMOTIONS\n\nEstablish starting shot at a car-key. Subject motion: ANIMATED WITH WEIGHTED TANGENTS FOR SMOOTH NONLINEAR ANIMATIONS\nFPV rotates and fpv's seamlessly into a morphing golden car silhouette, radiating intense golden light trails during transformation\\n\\nCamera motion: FPV spiraling camera continuously rotating throughout the shot, with non-linear movements capturing the transformation\\n\\nScene motion: Dynamic flash lighting illuminating the morphing process, creating an immersive golden aura with subtle vignette effect\\n\\nStyle descriptors: Ultra hyperreal, super crisp visuals, high-end cinematic quality, flowing elegance, seamless transitions. [Cinematography]\nCar key transforms into sleek car silhouette with golden light trails. FPV camera orbits smoothly with dynamic rotation. High-key lighting with golden glow and lens flares. Hyperrealistic, cinematic quality with elegant transitions.FPV camera orbits from car key as it transforms into golden car silhouette emitting light trails. Macro shot of car key smoothly morphing into golden car silhouette with radiant light trails. Orbital POV camera rotates continuously, following fluid trajectories. Dynamic lighting creates golden atmospheric glow. Photorealistic, cinematic, elegant.\"```\n\n```json\n{\n  \"validated_prompt\": \"[Cinematography] Car key transforms into golden car silhouette with radiating light trails. FPV camera spirals with smooth rotation, capturing the transformation. Dynamic golden lighting illuminates the morphing process creating an immersive aura. Ultra hyperreal, crisp visuals with high-end cinematic quality.\"\n}\n```",
      "responses": {
          "openrouter/anthropic/claude-3.7-sonnet:beta": {
          "model": "openrouter/anthropic/claude-3.7-sonnet:beta",
          "content": "```json\n{\n  \"simplified_motion_prompt\": \"Camera moves in spiral around car key. Car key transforms into golden car silhouette. Golden light radiates from silhouette as camera continues rotating.\"\n}\n```",
        }
      }
    },
      {
      "instruction": "# Runway Descriptor Compressor\n\nYour goal is not to **modify** but to **compress** visual descriptors to RunwayML's preferred concise format while maintaining essential metallic and morphing elements. **Eliminate redundant adjectives and focus on core visuals.** Execute as:\n\n`{role=descriptor_compressor; input=[simplified_motion_prompt:str]; process=[compress_visual_descriptors(), eliminate_redundant_adjectives(), maintain_core_elements(), preserve_metallic_focus(), ensure_conciseness()]; constraints=[minimal_adjectives(), core_visuals_only(), maintain_progression()]; requirements=[descriptor_efficiency(), visual_clarity(), runway_format()]; output={compressed_prompt:str}}`",
      "step": "c",
      "title": "Runway Descriptor Compressor",
      "input": "\n```[Initial Prompt]: \"EMPHASIZE DYNAMIC AND ELEGANT CAMERAMOTIONS\n\nEstablish starting shot at a car-key. Subject motion: ANIMATED WITH WEIGHTED TANGENTS FOR SMOOTH NONLINEAR ANIMATIONS\nFPV rotates and fpv's seamlessly into a morphing golden car silhouette, radiating intense golden light trails during transformation\\n\\nCamera motion: FPV spiraling camera continuously rotating throughout the shot, with non-linear movements capturing the transformation\\n\\nScene motion: Dynamic flash lighting illuminating the morphing process, creating an immersive golden aura with subtle vignette effect\\n\\nStyle descriptors: Ultra hyperreal, super crisp visuals, high-end cinematic quality, flowing elegance, seamless transitions. [Cinematography]\nCar key transforms into sleek car silhouette with golden light trails. FPV camera orbits smoothly with dynamic rotation. High-key lighting with golden glow and lens flares. Hyperrealistic, cinematic quality with elegant transitions.FPV camera orbits from car key as it transforms into golden car silhouette emitting light trails. Macro shot of car key smoothly morphing into golden car silhouette with radiant light trails. Orbital POV camera rotates continuously, following fluid trajectories. Dynamic lighting creates golden atmospheric glow. Photorealistic, cinematic, elegant.\"```\n\n```json\n{\n  \"simplified_motion_prompt\": \"Camera moves in spiral around car key. Car key transforms into golden car silhouette. Golden light radiates from silhouette as camera continues rotating.\"\n}\n```",
      "responses": {
          "openrouter/anthropic/claude-3.7-sonnet:beta": {
          "model": "openrouter/anthropic/claude-3.7-sonnet:beta",
          "content": "```json\n{\n  \"compressed_prompt\": \"Car key morphs into golden car silhouette with radiant light trails. Camera spirals continuously, capturing transformation with fluid motion.\"\n}\n```",
        }
      }
    },
      {
      "instruction": "# Runway Core Distiller\n\nYour goal is not to **change** but to **distill** to RunwayML's minimal syntax requirements: direct subject-action descriptions with essential header tags only. **Achieve maximum conciseness while preserving visual impact.** Execute as:\n\n`{role=core_distiller; input=[compressed_prompt:str]; process=[distill_to_core_syntax(), maintain_subject_action_clarity(), preserve_essential_headers(), ensure_runway_directness()]; constraints=[maximum_conciseness(), direct_descriptions_only(), minimal_headers()]; requirements=[runway_syntax_compliance(), visual_impact(), format_preservation()]; output={distilled_core_prompt:str}}`",
      "step": "d",
      "title": "Runway Core Distiller",
      "input": "\n```[Initial Prompt]: \"EMPHASIZE DYNAMIC AND ELEGANT CAMERAMOTIONS\n\nEstablish starting shot at a car-key. Subject motion: ANIMATED WITH WEIGHTED TANGENTS FOR SMOOTH NONLINEAR ANIMATIONS\nFPV rotates and fpv's seamlessly into a morphing golden car silhouette, radiating intense golden light trails during transformation\\n\\nCamera motion: FPV spiraling camera continuously rotating throughout the shot, with non-linear movements capturing the transformation\\n\\nScene motion: Dynamic flash lighting illuminating the morphing process, creating an immersive golden aura with subtle vignette effect\\n\\nStyle descriptors: Ultra hyperreal, super crisp visuals, high-end cinematic quality, flowing elegance, seamless transitions. [Cinematography]\nCar key transforms into sleek car silhouette with golden light trails. FPV camera orbits smoothly with dynamic rotation. High-key lighting with golden glow and lens flares. Hyperrealistic, cinematic quality with elegant transitions.FPV camera orbits from car key as it transforms into golden car silhouette emitting light trails. Macro shot of car key smoothly morphing into golden car silhouette with radiant light trails. Orbital POV camera rotates continuously, following fluid trajectories. Dynamic lighting creates golden atmospheric glow. Photorealistic, cinematic, elegant.\"```\n\n```json\n{\n  \"compressed_prompt\": \"Car key morphs into golden car silhouette with radiant light trails. Camera spirals continuously, capturing transformation with fluid motion.\"\n}\n```",
      "responses": {
          "openrouter/anthropic/claude-3.7-sonnet:beta": {
          "model": "openrouter/anthropic/claude-3.7-sonnet:beta",
          "content": "```json\n{\n  \"distilled_core_prompt\": \"[Cinematography] Car key morphs into golden car silhouette with radiant light trails. Camera spirals continuously, capturing transformation with fluid motion.\"\n}\n```",
        }
      }
    }