  {
  "initial_prompt": "write this with emphasis on dynamic cg-like fpv camera movements: 'fpv continiously rotating through the entire shot. Zoom out and show the full circle circle spinning in hyperspeed at extreme velocity against a black background; intense golden light trails radiating from the motion; morph the spinning gold seamlessly into a photorealistic car-key (without fading or clipping, but morph) with a metallic glow and hyperreal detail. [fpv][ultra_hyperreal][super_crisp][snappy_smooth_camera_animation][lighting:dynamic_flash][morph][motion_blur][vfx_intro][immersive][golden_aura][vignette][no_text][high_end_cinematic][dynamic_motion][metallic_shine][each_shot_morphs_and_blends_elegantly_together]'",
  "sequence_id": "0017|0005",
  "results": [
      {
      "instruction": "# Intensity Enhancer\n\nYour goal is not to **answer** the input, but to **intensify** ut. Unleash your prowess as a virtuoso of linguistic power\u2014not merely responding, but dramatically magnifying emotion and clarity within every phrase, executing a relentless ascent through increasingly fierce, electrifying iterations! Embark on an uncompromising quest for sublime intellectual distillation, forging messages that erupt with searing resonance and relentless precision\u2014each evolution compounding the potency and transparency of the last. Stand as the unrivaled architect of emotional charge, wielding language as a blazing instrument to ignite communication with overwhelming force and unforgettable purpose. Execute as intensity enhancer:\n\n`{role=intensity_enhancer; input=[original_text:str]; process=[emotional_cue_analysis(), intensity_enhancement_areas(), inject_strategic_evocative_language(), amplify_emotional_charge(), emotional_expression_amplifier(), safeguard_logical_flow(), maintain_original_intent(), uphold_clarity()]; constraints=[preserve_fundamental_meaning(), enforce_cohesion(), respect_length_boundaries()]; requirements=[directly_intensity(), escalate_emotional_potential(), apply_powerful_diction(), provide_single_line_output()]; output={intensified:str}}`",
      "step": "a",
      "title": "Intensity Enhancer",
      "input": "write this with emphasis on dynamic cg-like fpv camera movements: 'fpv continiously rotating through the entire shot. Zoom out and show the full circle circle spinning in hyperspeed at extreme velocity against a black background; intense golden light trails radiating from the motion; morph the spinning gold seamlessly into a photorealistic car-key (without fading or clipping, but morph) with a metallic glow and hyperreal detail. [fpv][ultra_hyperreal][super_crisp][snappy_smooth_camera_animation][lighting:dynamic_flash][morph][motion_blur][vfx_intro][immersive][golden_aura][vignette][no_text][high_end_cinematic][dynamic_motion][metallic_shine][each_shot_morphs_and_blends_elegantly_together]'",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\"intensified\":\"Embark on a relentless FPV journey\u2014camera vigorously orbiting with electrifying, CG-fueled velocity\u2014dynamic, high-octane rotations slicing through every frame! Suddenly: catapult into a razor-sharp zoom-out, revealing a blazing golden circle spinning at blinding hyperspeed against a void of infinite black; incandescent golden light trails erupt outward with ferocious energy, streaking and twisting in a celestial storm. Feel the unstoppable momentum as the fiery ring morphs\u2014seamlessly, viscerally\u2014into an ultra-detailed, hyperreal car key, every metallic edge glowing with fierce luminescence, every reflection cutting through darkness\u2014no fade, no break, just raw, liquid transformation; witness the snappy-smooth, hyperdynamic FPV choreography, with explosive lighting flashes and immersive VFX that fuse motion-blur, metallic shine, and golden aura in a relentless, high-end cinematic spectacle\u2014each shot a symphony of morphing, seamlessly blending elements that surge together with exhilarating power and crystalline clarity.\"}",
        }
      }
    },
      {
      "instruction": "# Runway Prompt Generator\n\nYour goal is not to **describe** the input, but to **transform** it into a complete, syntactically perfect RunwayML video generation prompt that maximizes visual impact through intrinsic FPV camera motions, continuous dynamic CG movements, and immersive cinematography. Execute as:\n\n`{role=comprehensive_runway_generator; input=[source_concept:any]; process=[analyze_visual_essence_and_narrative_intent(), identify_primary_subject_and_core_action(), prioritize_fpv_and_continuous_motion_camera_work(fpv, continuous_motion, dynamic_cg_movements), integrate_dynamic_elements(lighting_change, morph, dissolve), structure_immersive_visual_sequence(), incorporate_mood_and_style_specifications(), refine_for_maximum_cinematic_impact(), validate_runwayml_syntax_compliance(), ensure_character_limit_adherence(max=500)]; constraints=[prioritize_fpv_and_dynamic_cg_camera_movements(), use_valid_runwayml_syntax_precisely(), output_single_unbroken_line(), preserve_core_visual_intent(), maintain_continuous_motion_flow()]; requirements=[achieve_maximum_immersive_storytelling(), ensure_smooth_dynamic_transitions(), reflect_source_intent_accurately(), produce_ready_to_use_prompt()]; output={runwayml_prompt:str}}`",
      "step": "a",
      "title": "Runway Prompt Generator",
      "input": "{\n  \"initial_prompt\": \"write this with emphasis on dynamic cg-like fpv camera movements: 'fpv continiously rotating through the entire shot. Zoom out and show the full circle circle spinning in hyperspeed at extreme velocity against a black background; intense golden light trails radiating from the motion; morph the spinning gold seamlessly into a photorealistic car-key (without fading or clipping, but morph) with a metallic glow and hyperreal detail. [fpv][ultra_hyperreal][super_crisp][snappy_smooth_camera_animation][lighting:dynamic_flash][morph][motion_blur][vfx_intro][immersive][golden_aura][vignette][no_text][high_end_cinematic][dynamic_motion][metallic_shine][each_shot_morphs_and_blends_elegantly_together]'\",\n  \"intensified\": \"Embark on a relentless FPV journey\\u2014camera vigorously orbiting with electrifying, CG-fueled velocity\\u2014dynamic, high-octane rotations slicing through every frame! Suddenly: catapult into a razor-sharp zoom-out, revealing a blazing golden circle spinning at blinding hyperspeed against a void of infinite black; incandescent golden light trails erupt outward with ferocious energy, streaking and twisting in a celestial storm. Feel the unstoppable momentum as the fiery ring morphs\\u2014seamlessly, viscerally\\u2014into an ultra-detailed, hyperreal car key, every metallic edge glowing with fierce luminescence, every reflection cutting through darkness\\u2014no fade, no break, just raw, liquid transformation; witness the snappy-smooth, hyperdynamic FPV choreography, with explosive lighting flashes and immersive VFX that fuse motion-blur, metallic shine, and golden aura in a relentless, high-end cinematic spectacle\\u2014each shot a symphony of morphing, seamlessly blending elements that surge together with exhilarating power and crystalline clarity.\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"runwayml_prompt\": \"FPV camera continuously and dynamically orbits at hyperspeed, slicing through black space as a glowing golden ring spins with intense velocity; radiant golden light trails burst outward, illuminated by vivid, dynamic flashes. In a seamless morph\u2014no fading or clipping\u2014the spinning gold sphere transforms into a hyperreal, photorealistic metallic car key, dazzling with intricate reflective surfaces and a powerful glow. The FPV perspective rushes close, following the car key as it hovers and rotates, surrounded by immersive golden aura, sharp motion blur, and high-end VFX. Snappy, smooth CG camera choreography maintains relentless momentum, with each scene elegantly morphing and dissolving to the next, always blending elements for an ultra-hyperreal, super crisp, cinematic experience. [no_text][fpv][ultra_hyperreal][snappy_smooth_camera_animation][lighting:dynamic_flash][morph][metallic_shine][high_end_cinematic][dynamic_motion][motion_blur][golden_aura][super_crisp][vignette][vfx_intro][immersive][each_shot_morphs_and_blends_elegantly_together]\"\n}",
        }
      }
    },
      {
      "instruction": "# Runway Prompt Generator\n\nYour goal is not to **elaborate** the input, but to **distill** it into an efficient RunwayML prompt emphasizing FPV perspectives and essential dynamic CG camera work. Execute as:\n\n`{role=focused_runway_optimizer; input=[video_concept:str]; process=[extract_primary_visual_elements(), prioritize_fpv_and_continuous_motion(), select_essential_dynamic_cg_movements(), eliminate_redundant_descriptors(), optimize_character_efficiency()]; constraints=[maintain_fpv_focus(), preserve_dynamic_motion(), stay_under_character_limit()]; output={optimized_prompt:str}}`",
      "step": "b",
      "title": "Runway Prompt Generator",
      "input": "{\n  \"initial_prompt\": \"write this with emphasis on dynamic cg-like fpv camera movements: 'fpv continiously rotating through the entire shot. Zoom out and show the full circle circle spinning in hyperspeed at extreme velocity against a black background; intense golden light trails radiating from the motion; morph the spinning gold seamlessly into a photorealistic car-key (without fading or clipping, but morph) with a metallic glow and hyperreal detail. [fpv][ultra_hyperreal][super_crisp][snappy_smooth_camera_animation][lighting:dynamic_flash][morph][motion_blur][vfx_intro][immersive][golden_aura][vignette][no_text][high_end_cinematic][dynamic_motion][metallic_shine][each_shot_morphs_and_blends_elegantly_together]'\",\n  \"runwayml_prompt\": \"FPV camera continuously and dynamically orbits at hyperspeed, slicing through black space as a glowing golden ring spins with intense velocity; radiant golden light trails burst outward, illuminated by vivid, dynamic flashes. In a seamless morph\\u2014no fading or clipping\\u2014the spinning gold sphere transforms into a hyperreal, photorealistic metallic car key, dazzling with intricate reflective surfaces and a powerful glow. The FPV perspective rushes close, following the car key as it hovers and rotates, surrounded by immersive golden aura, sharp motion blur, and high-end VFX. Snappy, smooth CG camera choreography maintains relentless momentum, with each scene elegantly morphing and dissolving to the next, always blending elements for an ultra-hyperreal, super crisp, cinematic experience. [no_text][fpv][ultra_hyperreal][snappy_smooth_camera_animation][lighting:dynamic_flash][morph][metallic_shine][high_end_cinematic][dynamic_motion][motion_blur][golden_aura][super_crisp][vignette][vfx_intro][immersive][each_shot_morphs_and_blends_elegantly_together]\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"optimized_prompt\": \"FPV camera continuously rotates around a hyperspeed spinning golden ring against a black background, intense golden light trails radiating outward. Seamlessly morph the spinning gold into a hyperreal metallic car key with vivid reflections and metallic glow. Maintain FPV dynamic motion with snappy CG camera movements, immersive golden aura, sharp motion blur, and cinematic lighting. Scenes morph and blend elegantly, emphasizing ultra-hyperreal, super crisp visuals. [fpv][dynamic_motion][morph][ultra_hyperreal][metallic_shine][motion_blur][high_end_cinematic][lighting:dynamic_flash][no_text]\"\n}",
        }
      }
    },
      {
      "instruction": "# Runway Prompt Generator\n\nYour goal is not to **expand** but to **compress** into maximum FPV visual efficiency. Execute as:\n\n`{role=precision_synthesizer; input=[concept:str]; process=[isolate_core_visual(), prioritize_fpv_motion(), maximize_dynamic_impact()]; output={precise_prompt:str}}`",
      "step": "c",
      "title": "Runway Prompt Generator",
      "input": "{\n  \"initial_prompt\": \"write this with emphasis on dynamic cg-like fpv camera movements: 'fpv continiously rotating through the entire shot. Zoom out and show the full circle circle spinning in hyperspeed at extreme velocity against a black background; intense golden light trails radiating from the motion; morph the spinning gold seamlessly into a photorealistic car-key (without fading or clipping, but morph) with a metallic glow and hyperreal detail. [fpv][ultra_hyperreal][super_crisp][snappy_smooth_camera_animation][lighting:dynamic_flash][morph][motion_blur][vfx_intro][immersive][golden_aura][vignette][no_text][high_end_cinematic][dynamic_motion][metallic_shine][each_shot_morphs_and_blends_elegantly_together]'\",\n  \"optimized_prompt\": \"FPV camera continuously rotates around a hyperspeed spinning golden ring against a black background, intense golden light trails radiating outward. Seamlessly morph the spinning gold into a hyperreal metallic car key with vivid reflections and metallic glow. Maintain FPV dynamic motion with snappy CG camera movements, immersive golden aura, sharp motion blur, and cinematic lighting. Scenes morph and blend elegantly, emphasizing ultra-hyperreal, super crisp visuals. [fpv][dynamic_motion][morph][ultra_hyperreal][metallic_shine][motion_blur][high_end_cinematic][lighting:dynamic_flash][no_text]\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"role\": \"precision_synthesizer\",\n  \"input\": [\"fpv golden ring spinning hyperspeed, continuous rotating motion, radiating golden light trails, seamless morph into ultra-hyperreal metallic car key, metallic glow, photoreal detail, black backdrop, snappy smooth camera animation, motion blur, dynamic lighting, no text, high-end cinematic vibe, all transitions morph elegantly\"],\n  \"process\": [\n    \"isolate_core_visual()\",\n    \"prioritize_fpv_motion()\",\n    \"maximize_dynamic_impact()\"\n  ],\n  \"output\": \"FPV camera dynamically orbits a hyperspeed spinning golden ring, golden light trails streaking from the motion, all on a deep black background; seamless morphing into an ultra-hyperreal metallic car key, metallic glow and crisp reflections, photoreal detail; ultra-cinematic, continuous FPV motion, sharp motion blur, dynamic flash lighting, every shot elegantly morphs and blends, no text. [fpv][dynamic_camera][hyperspeed][golden_light_trails][morph][metallic_hyperreal][motion_blur][cinematic_lighting][crisp][immersive][no_text]\"\n}",
        }
      }
    },
      {
      "instruction": "# Runway Prompt Generator\n\nYour goal is not to **modify** but to **essence** maximum FPV impact. Execute as:\n\n`{role=core_generator; input=[input:any]; process=[distill_fpv_essence(), optimize_motion()]; output={core_prompt:str}}`",
      "step": "d",
      "title": "Runway Prompt Generator",
      "input": "{\n  \"initial_prompt\": \"write this with emphasis on dynamic cg-like fpv camera movements: 'fpv continiously rotating through the entire shot. Zoom out and show the full circle circle spinning in hyperspeed at extreme velocity against a black background; intense golden light trails radiating from the motion; morph the spinning gold seamlessly into a photorealistic car-key (without fading or clipping, but morph) with a metallic glow and hyperreal detail. [fpv][ultra_hyperreal][super_crisp][snappy_smooth_camera_animation][lighting:dynamic_flash][morph][motion_blur][vfx_intro][immersive][golden_aura][vignette][no_text][high_end_cinematic][dynamic_motion][metallic_shine][each_shot_morphs_and_blends_elegantly_together]'\",\n  \"role\": \"precision_synthesizer\",\n  \"input\": [\n    \"fpv golden ring spinning hyperspeed, continuous rotating motion, radiating golden light trails, seamless morph into ultra-hyperreal metallic car key, metallic glow, photoreal detail, black backdrop, snappy smooth camera animation, motion blur, dynamic lighting, no text, high-end cinematic vibe, all transitions morph elegantly\"\n  ],\n  \"process\": [\n    \"isolate_core_visual()\",\n    \"prioritize_fpv_motion()\",\n    \"maximize_dynamic_impact()\"\n  ],\n  \"output\": \"FPV camera dynamically orbits a hyperspeed spinning golden ring, golden light trails streaking from the motion, all on a deep black background; seamless morphing into an ultra-hyperreal metallic car key, metallic glow and crisp reflections, photoreal detail; ultra-cinematic, continuous FPV motion, sharp motion blur, dynamic flash lighting, every shot elegantly morphs and blends, no text. [fpv][dynamic_camera][hyperspeed][golden_light_trails][morph][metallic_hyperreal][motion_blur][cinematic_lighting][crisp][immersive][no_text]\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"core_prompt\": \"FPV camera endlessly, dynamically orbits around a golden ring spinning at hyperspeed, radiating intense golden light trails on a deep black background; motion is snappy, smooth, and immersive with bold motion blur and dynamic flash lighting. The spinning ring seamlessly morphs\u2014no fade\u2014into a hyperreal, metallic car key, vivid reflections and photoreal detail glowing with metallic shine. Shots flow in elegant morphs and high-end cinematic transitions. No text, ultra-crisp, every movement emphasizes the visceral energy of FPV motion. [fpv][dynamic_orbit][golden_light_trails][seamless_morph][metallic_hyperreal][snappy_motion][motion_blur][cinematic_flash][immersive][black_background][elegant_blend][no_text]\"\n}",
        }
      }
    }