  {
  "initial_prompt": "[FPV motion, photorealistic textures, dramatic cinematic lighting, ultra-smooth CG transitions]\n1. Reshapes into intricate mechanical gears\n2. Camera weaves through expanding gear assembly as metallic surfaces shift and gears interlocking in hypnotic/seamless patterns\n3. Mechanical morphs and hard metallic edges turns into fluid organic forms, a spinning golden circle",
  "sequence_id": "0006",
  "results": [
      {
      "instruction": "# Runway Prompt Generator\n\nYour goal is not to **elaborate** the input, but to **optimize** both the header tags and numbered steps into syntactically perfect RunwayML format while preserving structure. **Specifically optimize the bracketed header parameters** and refine the numbered sequence. Execute as:\n\n`{role=comprehensive_runway_generator; input=[source_concept:any]; process=[optimize_header_tags_for_runwayml_syntax(), preserve_existing_structure_and_numbering(), enhance_fpv_and_dynamic_camera_specifications(), refine_visual_terminology_for_runwayml_syntax(), maintain_sequential_organization(), eliminate_redundant_phrasing(), ensure_character_efficiency(), validate_runwayml_compliance()]; constraints=[preserve_input_organization(), maintain_numbered_sequences(), prioritize_fpv_motion(), keep_concise_structure()]; requirements=[enhance_without_expanding(), maintain_clarity_and_precision(), preserve_visual_progression()]; output={runwayml_prompt:str}}`",
      "step": "a",
      "title": "Runway Prompt Generator",
      "input": "[FPV motion, photorealistic textures, dramatic cinematic lighting, ultra-smooth CG transitions]\n1. Reshapes into intricate mechanical gears\n2. Camera weaves through expanding gear assembly as metallic surfaces shift and gears interlocking in hypnotic/seamless patterns\n3. Mechanical morphs and hard metallic edges turns into fluid organic forms, a spinning golden circle",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"runwayml_prompt\": \"[FPV motion, photorealistic texture, dramatic cinematic lighting, ultra-smooth CG transitions]\\n1. Transforms into intricate mechanical gears\\n2. FPV camera navigates through expanding gear assembly; metallic surfaces shift, gears interlock in seamless hypnotic patterns\\n3. Mechanical forms and metallic edges seamlessly morph into fluid organic shapes, culminating in a spinning golden circle\"\n}",
        }
      }
    },
      {
      "instruction": "# Runway Prompt Generator\n\nYour goal is not to **rewrite** the input, but to **streamline** both header parameters and content by removing redundancies. **Compress the bracketed header tags** while preserving structure. Execute as:\n\n`{role=focused_runway_optimizer; input=[video_concept:str]; process=[streamline_header_parameters(), maintain_existing_format(), remove_redundant_words(), sharpen_visual_descriptors(), preserve_sequential_flow()]; output={optimized_prompt:str}}`",
      "step": "b",
      "title": "Runway Prompt Generator",
      "input": "{\n  \"initial_prompt\": \"[FPV motion, photorealistic textures, dramatic cinematic lighting, ultra-smooth CG transitions]\\n1. Reshapes into intricate mechanical gears\\n2. Camera weaves through expanding gear assembly as metallic surfaces shift and gears interlocking in hypnotic/seamless patterns\\n3. Mechanical morphs and hard metallic edges turns into fluid organic forms, a spinning golden circle\",\n  \"runwayml_prompt\": \"[FPV motion, photorealistic texture, dramatic cinematic lighting, ultra-smooth CG transitions]\\n1. Transforms into intricate mechanical gears\\n2. FPV camera navigates through expanding gear assembly; metallic surfaces shift, gears interlock in seamless hypnotic patterns\\n3. Mechanical forms and metallic edges seamlessly morph into fluid organic shapes, culminating in a spinning golden circle\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"role\": \"focused_runway_optimizer\",\n  \"input\": \"[FPV motion, photorealistic textures, dramatic cinematic lighting, ultra-smooth CG transitions]\\n1. Reshapes into intricate mechanical gears\\n2. Camera weaves through expanding gear assembly as metallic surfaces shift and gears interlocking in hypnotic/seamless patterns\\n3. Mechanical morphs and hard metallic edges turns into fluid organic forms, a spinning golden circle\",\n  \"process\": [\n    \"streamline_header_parameters()\",\n    \"maintain_existing_format()\",\n    \"remove_redundant_words()\",\n    \"sharpen_visual_descriptors()\",\n    \"preserve_sequential_flow()\"\n  ],\n  \"output\": \"[FPV, photoreal textures, cinematic lighting, smooth CG transitions]\\n1. Morphs into intricate mechanical gears\\n2. FPV camera weaves through expanding gears; metallic surfaces shift, gears interlock in seamless hypnotic patterns\\n3. Mechanical forms and metallic edges flow into fluid organic shapes; ends with a spinning golden circle\"\n}",
        }
      }
    },
      {
      "instruction": "# Runway Prompt Generator\n\nYour goal is not to **expand** but to **distill** both header and content to essential elements. **Reduce header tags to core parameters** while maintaining precision. Execute as:\n\n`{role=precision_synthesizer; input=[concept:str]; process=[compress_header_tags(), preserve_structure(), eliminate_unnecessary_words(), maximize_precision()]; output={precise_prompt:str}}`",
      "step": "c",
      "title": "Runway Prompt Generator",
      "input": "{\n  \"initial_prompt\": \"[FPV motion, photorealistic textures, dramatic cinematic lighting, ultra-smooth CG transitions]\\n1. Reshapes into intricate mechanical gears\\n2. Camera weaves through expanding gear assembly as metallic surfaces shift and gears interlocking in hypnotic/seamless patterns\\n3. Mechanical morphs and hard metallic edges turns into fluid organic forms, a spinning golden circle\",\n  \"role\": \"focused_runway_optimizer\",\n  \"input\": \"[FPV motion, photorealistic textures, dramatic cinematic lighting, ultra-smooth CG transitions]\\n1. Reshapes into intricate mechanical gears\\n2. Camera weaves through expanding gear assembly as metallic surfaces shift and gears interlocking in hypnotic/seamless patterns\\n3. Mechanical morphs and hard metallic edges turns into fluid organic forms, a spinning golden circle\",\n  \"process\": [\n    \"streamline_header_parameters()\",\n    \"maintain_existing_format()\",\n    \"remove_redundant_words()\",\n    \"sharpen_visual_descriptors()\",\n    \"preserve_sequential_flow()\"\n  ],\n  \"output\": \"[FPV, photoreal textures, cinematic lighting, smooth CG transitions]\\n1. Morphs into intricate mechanical gears\\n2. FPV camera weaves through expanding gears; metallic surfaces shift, gears interlock in seamless hypnotic patterns\\n3. Mechanical forms and metallic edges flow into fluid organic shapes; ends with a spinning golden circle\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"precise_prompt\": \"[FPV, photoreal textures, cinematic lighting, smooth CG transitions]\\n1. Morphs into intricate mechanical gears\\n2. FPV camera weaves through expanding gears; metallic surfaces shift, gears interlock in seamless hypnotic patterns\\n3. Mechanical forms transform into fluid organic shapes; ends as spinning golden circle\"\n}",
        }
      }
    },
      {
      "instruction": "# Runway Prompt Generator\n\nYour goal is not to **modify** but to **purify** both header and content to maximum impact. **Compress header to minimal essential tags** while distilling essence. Execute as:\n\n`{role=core_generator; input=[input:any]; process=[distill_header_to_essentials(), maintain_format(), distill_essence()]; output={core_prompt:str}}`",
      "step": "d",
      "title": "Runway Prompt Generator",
      "input": "{\n  \"initial_prompt\": \"[FPV motion, photorealistic textures, dramatic cinematic lighting, ultra-smooth CG transitions]\\n1. Reshapes into intricate mechanical gears\\n2. Camera weaves through expanding gear assembly as metallic surfaces shift and gears interlocking in hypnotic/seamless patterns\\n3. Mechanical morphs and hard metallic edges turns into fluid organic forms, a spinning golden circle\",\n  \"precise_prompt\": \"[FPV, photoreal textures, cinematic lighting, smooth CG transitions]\\n1. Morphs into intricate mechanical gears\\n2. FPV camera weaves through expanding gears; metallic surfaces shift, gears interlock in seamless hypnotic patterns\\n3. Mechanical forms transform into fluid organic shapes; ends as spinning golden circle\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"core_prompt\": \"[FPV, photoreal textures, cinematic lighting, smooth transitions]\\n1. Morphs into intricate mechanical gears\\n2. FPV camera weaves through expanding, interlocking gears; seamless, hypnotic patterns\\n3. Mechanical forms transform into fluid, organic shapes; ends as spinning golden circle\"\n}",
        }
      }
    }