  {
  "initial_prompt": "Hyperreal Car-Key to Geometric Metallic Mechanism to Golden Ring Cinematic Morph Sequence: FPV camera dynamically circles a hyperreal car key, morphs fluidly into a geometric metallic mechanism, then seamlessly transforms into a spinning golden ring; continuous energetic FPV motion, photorealistic textures, dramatic cinematic lighting, ultra-smooth CG transitions",
  "sequence_id": "0005",
  "results": [
      {
      "instruction": "# Runway Prompt Generator\n\nYour goal is not to **describe** the input, but to **transform** it into a complete, syntactically perfect RunwayML video generation prompt that maximizes visual impact through intrinsic FPV camera motions, continuous dynamic CG movements, and immersive cinematography. Execute as:\n\n`{role=comprehensive_runway_generator; input=[source_concept:any]; process=[analyze_visual_essence_and_narrative_intent(), identify_primary_subject_and_core_action(), prioritize_fpv_and_continuous_motion_camera_work(fpv, continuous_motion, dynamic_cg_movements), integrate_dynamic_elements(lighting_change, morph, dissolve), structure_immersive_visual_sequence(), incorporate_mood_and_style_specifications(), refine_for_maximum_cinematic_impact(), validate_runwayml_syntax_compliance(), ensure_character_limit_adherence(max=500)]; constraints=[prioritize_fpv_and_dynamic_cg_camera_movements(), use_valid_runwayml_syntax_precisely(), output_single_unbroken_line(), preserve_core_visual_intent(), maintain_continuous_motion_flow()]; requirements=[achieve_maximum_immersive_storytelling(), ensure_smooth_dynamic_transitions(), reflect_source_intent_accurately(), produce_ready_to_use_prompt()]; output={runwayml_prompt:str}}`",
      "step": "a",
      "title": "Runway Prompt Generator",
      "input": "Hyperreal Car-Key to Geometric Metallic Mechanism to Golden Ring Cinematic Morph Sequence: FPV camera dynamically circles a hyperreal car key, morphs fluidly into a geometric metallic mechanism, then seamlessly transforms into a spinning golden ring; continuous energetic FPV motion, photorealistic textures, dramatic cinematic lighting, ultra-smooth CG transitions",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\"runwayml_prompt\":\"FPV camera orbits a hyperreal car key with photorealistic metallic textures, dramatic cinematic lighting, and energetic motion; as the FPV view swoops closer, the car key begins morphing in a fluid, ultra-smooth CG transition into a complex geometric metallic mechanism, reflecting intense highlights and intricate details; the FPV camera continuously circles and weaves through the mechanism as it swiftly twists and morphs again, seamlessly transitioning into a luminous spinning golden ring; the camera pushes through the ring\u2019s center, maintaining immersive FPV movement throughout, enhanced by dynamic lighting changes and high-impact cinematic atmosphere\"}",
        }
      }
    },
      {
      "instruction": "# Runway Prompt Generator\n\nYour goal is not to **elaborate** the input, but to **distill** it into an efficient RunwayML prompt emphasizing FPV perspectives and essential dynamic CG camera work. Execute as:\n\n`{role=focused_runway_optimizer; input=[video_concept:str]; process=[extract_primary_visual_elements(), prioritize_fpv_and_continuous_motion(), select_essential_dynamic_cg_movements(), eliminate_redundant_descriptors(), optimize_character_efficiency()]; constraints=[maintain_fpv_focus(), preserve_dynamic_motion(), stay_under_character_limit()]; output={optimized_prompt:str}}`",
      "step": "b",
      "title": "Runway Prompt Generator",
      "input": "{\n  \"initial_prompt\": \"Hyperreal Car-Key to Geometric Metallic Mechanism to Golden Ring Cinematic Morph Sequence: FPV camera dynamically circles a hyperreal car key, morphs fluidly into a geometric metallic mechanism, then seamlessly transforms into a spinning golden ring; continuous energetic FPV motion, photorealistic textures, dramatic cinematic lighting, ultra-smooth CG transitions\",\n  \"runwayml_prompt\": \"FPV camera orbits a hyperreal car key with photorealistic metallic textures, dramatic cinematic lighting, and energetic motion; as the FPV view swoops closer, the car key begins morphing in a fluid, ultra-smooth CG transition into a complex geometric metallic mechanism, reflecting intense highlights and intricate details; the FPV camera continuously circles and weaves through the mechanism as it swiftly twists and morphs again, seamlessly transitioning into a luminous spinning golden ring; the camera pushes through the ring\\u2019s center, maintaining immersive FPV movement throughout, enhanced by dynamic lighting changes and high-impact cinematic atmosphere\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"optimized_prompt\": \"FPV camera orbits hyperreal car key, morphs fluidly into geometric metallic mechanism, then seamlessly transforms into spinning golden ring; continuous dynamic FPV motion, ultra-smooth CG transitions, photorealistic textures, dramatic cinematic lighting\"\n}",
        }
      }
    },
      {
      "instruction": "# Runway Prompt Generator\n\nYour goal is not to **expand** but to **compress** into maximum FPV visual efficiency. Execute as:\n\n`{role=precision_synthesizer; input=[concept:str]; process=[isolate_core_visual(), prioritize_fpv_motion(), maximize_dynamic_impact()]; output={precise_prompt:str}}`",
      "step": "c",
      "title": "Runway Prompt Generator",
      "input": "{\n  \"initial_prompt\": \"Hyperreal Car-Key to Geometric Metallic Mechanism to Golden Ring Cinematic Morph Sequence: FPV camera dynamically circles a hyperreal car key, morphs fluidly into a geometric metallic mechanism, then seamlessly transforms into a spinning golden ring; continuous energetic FPV motion, photorealistic textures, dramatic cinematic lighting, ultra-smooth CG transitions\",\n  \"optimized_prompt\": \"FPV camera orbits hyperreal car key, morphs fluidly into geometric metallic mechanism, then seamlessly transforms into spinning golden ring; continuous dynamic FPV motion, ultra-smooth CG transitions, photorealistic textures, dramatic cinematic lighting\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"precise_prompt\": \"FPV camera rapidly circles a hyperreal car key, morphs into sleek geometric metal mechanism, then seamlessly transforms into spinning golden ring; persistent dynamic FPV movement, ultra-smooth photoreal CG transitions, bold cinematic lighting\"\n}",
        }
      }
    },
      {
      "instruction": "# Runway Prompt Generator\n\nYour goal is not to **modify** but to **essence** maximum FPV impact. Execute as:\n\n`{role=core_generator; input=[input:any]; process=[distill_fpv_essence(), optimize_motion()]; output={core_prompt:str}}`",
      "step": "d",
      "title": "Runway Prompt Generator",
      "input": "{\n  \"initial_prompt\": \"Hyperreal Car-Key to Geometric Metallic Mechanism to Golden Ring Cinematic Morph Sequence: FPV camera dynamically circles a hyperreal car key, morphs fluidly into a geometric metallic mechanism, then seamlessly transforms into a spinning golden ring; continuous energetic FPV motion, photorealistic textures, dramatic cinematic lighting, ultra-smooth CG transitions\",\n  \"precise_prompt\": \"FPV camera rapidly circles a hyperreal car key, morphs into sleek geometric metal mechanism, then seamlessly transforms into spinning golden ring; persistent dynamic FPV movement, ultra-smooth photoreal CG transitions, bold cinematic lighting\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"core_prompt\": \"FPV camera energetically orbits a hyperreal car key, fluidly morphs into a polished geometric metal mechanism, then transitions into a spinning golden ring; continuous dynamic FPV motion, hyperrealistic textures, cinematic bold lighting, ultra-smooth CG morphs\"\n}",
        }
      }
    }