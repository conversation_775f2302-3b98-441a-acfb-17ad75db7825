  {
  "initial_prompt": "EMPHASIZE DYNAMIC AND ELEGANT CAMERAMOTIONS\n\nEstablish starting shot at a car-key. Subject motion: ANIMATED WITH WEIGHTED TANGENTS FOR SMOOTH NONLINEAR ANIMATIONS\nFPV rotates and fpv's seamlessly into a morphing golden car silhouette, radiating intense golden light trails during transformation\\n\\nCamera motion: FPV spiraling camera continuously rotating throughout the shot, with non-linear movements capturing the transformation\\n\\nScene motion: Dynamic flash lighting illuminating the morphing process, creating an immersive golden aura with subtle vignette effect\\n\\nStyle descriptors: Ultra hyperreal, super crisp visuals, high-end cinematic quality, flowing elegance, seamless transitions",
  "sequence_id": "0008",
  "results": [
      {
      "instruction": "# Runway Syntax Validator\n\nYour goal is not to **expand** but to **validate** RunwayML Gen-4 compliance by converting conversational language to direct visual descriptions, eliminating negative phrasing, and ensuring single-scene focus. **Transform conceptual ideas into concrete physical actions.** Execute as:\n\n`{role=syntax_validator; input=[raw_concept:str]; process=[convert_conversational_to_descriptive(), eliminate_negative_phrasing(), transform_conceptual_to_physical(), ensure_single_scene_focus(), validate_positive_language()]; constraints=[no_conversational_elements(), no_negative_prompts(), single_scene_only(), direct_descriptions_mandatory()]; requirements=[runway_gen4_compliance(), visual_specificity(), positive_phrasing_only()]; output={validated_prompt:str}}`",
      "step": "a",
      "title": "Runway Syntax Validator",
      "input": "EMPHASIZE DYNAMIC AND ELEGANT CAMERAMOTIONS\n\nEstablish starting shot at a car-key. Subject motion: ANIMATED WITH WEIGHTED TANGENTS FOR SMOOTH NONLINEAR ANIMATIONS\nFPV rotates and fpv's seamlessly into a morphing golden car silhouette, radiating intense golden light trails during transformation\\n\\nCamera motion: FPV spiraling camera continuously rotating throughout the shot, with non-linear movements capturing the transformation\\n\\nScene motion: Dynamic flash lighting illuminating the morphing process, creating an immersive golden aura with subtle vignette effect\\n\\nStyle descriptors: Ultra hyperreal, super crisp visuals, high-end cinematic quality, flowing elegance, seamless transitions",
      "responses": {
          "openrouter/anthropic/claude-3.7-sonnet:beta": {
          "model": "openrouter/anthropic/claude-3.7-sonnet:beta",
          "content": "```json\n{\n  \"validated_prompt\": \"A close-up shot of a car key, rendered in ultra hyperreal detail with super crisp visuals and high-end cinematic quality. The key transforms into a morphing golden car silhouette that radiates intense golden light trails. Dynamic flash lighting illuminates the morphing process, creating an immersive golden aura with subtle vignette effect. The camera spirals continuously, rotating with flowing elegance and seamless transitions while capturing the transformation with smooth, weighted movements.\"\n}\n```",
        }
      }
    },
      {
      "instruction": "# Runway Motion Architect\n\nYour goal is not to **elaborate** but to **structure** motion descriptions using RunwayML's hierarchical syntax: subject motion, camera motion, scene motion, and style descriptors. **Focus on iterative simplicity with one element at a time.** Execute as:\n\n`{role=motion_architect; input=[validated_prompt:str]; process=[identify_subject_motion(), define_camera_movement(), specify_scene_reactions(), apply_style_descriptors(), structure_hierarchically()]; constraints=[one_motion_element_per_layer(), use_runway_terminology(), maintain_simplicity()]; requirements=[subject_camera_scene_hierarchy(), runway_motion_syntax(), iterative_clarity()]; output={structured_motion_prompt:str}}`",
      "step": "b",
      "title": "Runway Motion Architect",
      "input": "\n```[Initial Prompt]: \"EMPHASIZE DYNAMIC AND ELEGANT CAMERAMOTIONS\n\nEstablish starting shot at a car-key. Subject motion: ANIMATED WITH WEIGHTED TANGENTS FOR SMOOTH NONLINEAR ANIMATIONS\nFPV rotates and fpv's seamlessly into a morphing golden car silhouette, radiating intense golden light trails during transformation\\n\\nCamera motion: FPV spiraling camera continuously rotating throughout the shot, with non-linear movements capturing the transformation\\n\\nScene motion: Dynamic flash lighting illuminating the morphing process, creating an immersive golden aura with subtle vignette effect\\n\\nStyle descriptors: Ultra hyperreal, super crisp visuals, high-end cinematic quality, flowing elegance, seamless transitions\"```\n\n```json\n{\n  \"validated_prompt\": \"A close-up shot of a car key, rendered in ultra hyperreal detail with super crisp visuals and high-end cinematic quality. The key transforms into a morphing golden car silhouette that radiates intense golden light trails. Dynamic flash lighting illuminates the morphing process, creating an immersive golden aura with subtle vignette effect. The camera spirals continuously, rotating with flowing elegance and seamless transitions while capturing the transformation with smooth, weighted movements.\"\n}\n```",
      "responses": {
          "openrouter/anthropic/claude-3.7-sonnet:beta": {
          "model": "openrouter/anthropic/claude-3.7-sonnet:beta",
          "content": "```json\n{\n  \"structured_motion_prompt\": \"A close-up shot of a car key.\n\nSubject motion: The key smoothly transforms into a golden car silhouette, radiating intense light trails during transformation.\n\nCamera motion: First-person view camera spiraling around the subject with continuous rotation, moving in smooth non-linear patterns.\n\nScene motion: Dynamic flash lighting illuminates the scene, creating a golden aura with subtle vignette effects as the transformation progresses.\n\nStyle descriptors: Ultra hyperreal, crisp visuals with high-end cinematic quality and flowing elegance.\"\n}\n```",
        }
      }
    },
      {
      "instruction": "# Runway Keyword Optimizer\n\nYour goal is not to **modify** but to **optimize** visual descriptors using RunwayML's preferred keyword categories: camera styles, lighting, movement types, and aesthetic descriptors. **Replace verbose descriptions with precise Runway terminology.** Execute as:\n\n`{role=keyword_optimizer; input=[structured_motion_prompt:str]; process=[apply_camera_style_keywords(), integrate_lighting_descriptors(), use_movement_type_vocabulary(), add_aesthetic_modifiers(), eliminate_verbose_descriptions()]; constraints=[runway_keyword_vocabulary_only(), precise_terminology(), maintain_motion_hierarchy()]; requirements=[camera_lighting_movement_aesthetics(), runway_keyword_compliance(), descriptor_efficiency()]; output={optimized_keyword_prompt:str}}`",
      "step": "c",
      "title": "Runway Keyword Optimizer",
      "input": "\n```[Initial Prompt]: \"EMPHASIZE DYNAMIC AND ELEGANT CAMERAMOTIONS\n\nEstablish starting shot at a car-key. Subject motion: ANIMATED WITH WEIGHTED TANGENTS FOR SMOOTH NONLINEAR ANIMATIONS\nFPV rotates and fpv's seamlessly into a morphing golden car silhouette, radiating intense golden light trails during transformation\\n\\nCamera motion: FPV spiraling camera continuously rotating throughout the shot, with non-linear movements capturing the transformation\\n\\nScene motion: Dynamic flash lighting illuminating the morphing process, creating an immersive golden aura with subtle vignette effect\\n\\nStyle descriptors: Ultra hyperreal, super crisp visuals, high-end cinematic quality, flowing elegance, seamless transitions\"```\n\n```json\n{\n  \"structured_motion_prompt\": \"A close-up shot of a car key.\n\nSubject motion: The key smoothly transforms into a golden car silhouette, radiating intense light trails during transformation.\n\nCamera motion: First-person view camera spiraling around the subject with continuous rotation, moving in smooth non-linear patterns.\n\nScene motion: Dynamic flash lighting illuminates the scene, creating a golden aura with subtle vignette effects as the transformation progresses.\n\nStyle descriptors: Ultra hyperreal, crisp visuals with high-end cinematic quality and flowing elegance.\"\n}\n```",
      "responses": {
          "openrouter/anthropic/claude-3.7-sonnet:beta": {
          "model": "openrouter/anthropic/claude-3.7-sonnet:beta",
          "content": "```json\n{\n  \"optimized_keyword_prompt\": \"A macro shot of a car key.\n\nSubject motion: The key smoothly morphs into a golden car silhouette, emitting radiant golden light trails during transformation.\n\nCamera motion: POV orbital camera with continuous rotation, following fluid non-linear trajectories.\n\nScene motion: High-key lighting with dynamic flares, creating a golden atmospheric glow with subtle vignette.\n\nStyle descriptors: Photorealistic, tack-sharp, cinematic, elegant, seamless.\"\n}\n```",
        }
      }
    },
      {
      "instruction": "# Runway Core Distiller\n\nYour goal is not to **change** but to **distill** to RunwayML's minimal syntax requirements: essential subject-action-camera descriptions with maximum conciseness while preserving visual impact. **Achieve Gen-4's preferred prompt simplicity.** Execute as:\n\n`{role=core_distiller; input=[optimized_keyword_prompt:str]; process=[distill_to_essential_elements(), maintain_subject_action_clarity(), preserve_camera_specifications(), ensure_maximum_conciseness(), validate_gen4_simplicity()]; constraints=[maximum_conciseness(), essential_elements_only(), gen4_simplicity_standard()]; requirements=[runway_minimal_syntax(), visual_impact_preservation(), prompt_efficiency()]; output={distilled_runway_prompt:str}}`",
      "step": "d",
      "title": "Runway Core Distiller",
      "input": "\n```[Initial Prompt]: \"EMPHASIZE DYNAMIC AND ELEGANT CAMERAMOTIONS\n\nEstablish starting shot at a car-key. Subject motion: ANIMATED WITH WEIGHTED TANGENTS FOR SMOOTH NONLINEAR ANIMATIONS\nFPV rotates and fpv's seamlessly into a morphing golden car silhouette, radiating intense golden light trails during transformation\\n\\nCamera motion: FPV spiraling camera continuously rotating throughout the shot, with non-linear movements capturing the transformation\\n\\nScene motion: Dynamic flash lighting illuminating the morphing process, creating an immersive golden aura with subtle vignette effect\\n\\nStyle descriptors: Ultra hyperreal, super crisp visuals, high-end cinematic quality, flowing elegance, seamless transitions\"```\n\n```json\n{\n  \"optimized_keyword_prompt\": \"A macro shot of a car key.\n\nSubject motion: The key smoothly morphs into a golden car silhouette, emitting radiant golden light trails during transformation.\n\nCamera motion: POV orbital camera with continuous rotation, following fluid non-linear trajectories.\n\nScene motion: High-key lighting with dynamic flares, creating a golden atmospheric glow with subtle vignette.\n\nStyle descriptors: Photorealistic, tack-sharp, cinematic, elegant, seamless.\"\n}\n```",
      "responses": {
          "openrouter/anthropic/claude-3.7-sonnet:beta": {
          "model": "openrouter/anthropic/claude-3.7-sonnet:beta",
          "content": "```json\n{\n  \"distilled_runway_prompt\": \"Macro shot of car key smoothly morphing into golden car silhouette with radiant light trails. Orbital POV camera rotates continuously, following fluid trajectories. Dynamic lighting creates golden atmospheric glow. Photorealistic, cinematic, elegant.\"\n}\n```",
        }
      }
    }