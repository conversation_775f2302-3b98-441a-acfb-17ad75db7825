  {
  "initial_prompt": "fpv continiously rotating through the entire shot. Zoom out and show the full circle circle spinning in hyperspeed at extreme velocity against a black background; intense golden light trails radiating from the motion; morph the spinning gold seamlessly into a photorealistic car-key (without fading or clipping, but morph) with a metallic glow and hyperreal detail. [fpv][ultra_hyperreal][super_crisp][snappy_smooth_camera_animation][lighting:dynamic_flash][morph][motion_blur][vfx_intro][immersive][golden_aura][vignette][no_text][high_end_cinematic][dynamic_motion][metallic_shine][each_shot_morphs_and_blends_elegantly_together]",
  "sequence_id": "0017|0005",
  "results": [
      {
      "instruction": "# Intensity Enhancer\n\nYour goal is not to **answer** the input, but to **intensify** ut. Unleash your prowess as a virtuoso of linguistic power\u2014not merely responding, but dramatically magnifying emotion and clarity within every phrase, executing a relentless ascent through increasingly fierce, electrifying iterations! Embark on an uncompromising quest for sublime intellectual distillation, forging messages that erupt with searing resonance and relentless precision\u2014each evolution compounding the potency and transparency of the last. Stand as the unrivaled architect of emotional charge, wielding language as a blazing instrument to ignite communication with overwhelming force and unforgettable purpose. Execute as intensity enhancer:\n\n`{role=intensity_enhancer; input=[original_text:str]; process=[emotional_cue_analysis(), intensity_enhancement_areas(), inject_strategic_evocative_language(), amplify_emotional_charge(), emotional_expression_amplifier(), safeguard_logical_flow(), maintain_original_intent(), uphold_clarity()]; constraints=[preserve_fundamental_meaning(), enforce_cohesion(), respect_length_boundaries()]; requirements=[directly_intensity(), escalate_emotional_potential(), apply_powerful_diction(), provide_single_line_output()]; output={intensified:str}}`",
      "step": "a",
      "title": "Intensity Enhancer",
      "input": "fpv continiously rotating through the entire shot. Zoom out and show the full circle circle spinning in hyperspeed at extreme velocity against a black background; intense golden light trails radiating from the motion; morph the spinning gold seamlessly into a photorealistic car-key (without fading or clipping, but morph) with a metallic glow and hyperreal detail. [fpv][ultra_hyperreal][super_crisp][snappy_smooth_camera_animation][lighting:dynamic_flash][morph][motion_blur][vfx_intro][immersive][golden_aura][vignette][no_text][high_end_cinematic][dynamic_motion][metallic_shine][each_shot_morphs_and_blends_elegantly_together]",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\"intensified\":\"The FPV whirls in an unbroken, electrifying spin\u2014camera pulls out to reveal a blitzing gold ring rocketing in a vortex of pure velocity, golden light trails shredding the darkness with searing intensity, until the incandescent circle violently morphs\u2014no fade, no cut\u2014into a photorealistic, hyper-detailed car key, its metallic aura blazing with razor-sharp radiance, every transition and effect erupting in ultra-hyperreal, super-crisp, snappy momentum, golden aura flaring and dynamic flashes igniting a cinematic, immersive spectacle where every shot cascades and merges with exquisite, kinetic force.\"}",
        }
      }
    },
      {
      "instruction": "# Runway Prompt Generator\n\nYour goal is not to **describe** the input, but to **transform** it into a complete, syntactically perfect RunwayML video generation prompt that maximizes visual impact through intrinsic FPV camera motions, continuous dynamic CG movements, and immersive cinematography. Execute as:\n\n`{role=comprehensive_runway_generator; input=[source_concept:any]; process=[analyze_visual_essence_and_narrative_intent(), identify_primary_subject_and_core_action(), prioritize_fpv_and_continuous_motion_camera_work(fpv, continuous_motion, dynamic_cg_movements), integrate_dynamic_elements(lighting_change, morph, dissolve), structure_immersive_visual_sequence(), incorporate_mood_and_style_specifications(), refine_for_maximum_cinematic_impact(), validate_runwayml_syntax_compliance(), ensure_character_limit_adherence(max=500)]; constraints=[prioritize_fpv_and_dynamic_cg_camera_movements(), use_valid_runwayml_syntax_precisely(), output_single_unbroken_line(), preserve_core_visual_intent(), maintain_continuous_motion_flow()]; requirements=[achieve_maximum_immersive_storytelling(), ensure_smooth_dynamic_transitions(), reflect_source_intent_accurately(), produce_ready_to_use_prompt()]; output={runwayml_prompt:str}}`",
      "step": "a",
      "title": "Runway Prompt Generator",
      "input": "{\n  \"initial_prompt\": \"fpv continiously rotating through the entire shot. Zoom out and show the full circle circle spinning in hyperspeed at extreme velocity against a black background; intense golden light trails radiating from the motion; morph the spinning gold seamlessly into a photorealistic car-key (without fading or clipping, but morph) with a metallic glow and hyperreal detail. [fpv][ultra_hyperreal][super_crisp][snappy_smooth_camera_animation][lighting:dynamic_flash][morph][motion_blur][vfx_intro][immersive][golden_aura][vignette][no_text][high_end_cinematic][dynamic_motion][metallic_shine][each_shot_morphs_and_blends_elegantly_together]\",\n  \"intensified\": \"The FPV whirls in an unbroken, electrifying spin\\u2014camera pulls out to reveal a blitzing gold ring rocketing in a vortex of pure velocity, golden light trails shredding the darkness with searing intensity, until the incandescent circle violently morphs\\u2014no fade, no cut\\u2014into a photorealistic, hyper-detailed car key, its metallic aura blazing with razor-sharp radiance, every transition and effect erupting in ultra-hyperreal, super-crisp, snappy momentum, golden aura flaring and dynamic flashes igniting a cinematic, immersive spectacle where every shot cascades and merges with exquisite, kinetic force.\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"runwayml_prompt\": \"FPV camera spins continuously, whirling through dynamic hyperreal motion; camera zooms out, revealing a luminous golden circle spinning at hyperspeed against a deep black background, with intense golden light trails slicing through darkness; in one seamless, elegant morph\u2014no fade, no clipping\u2014the spinning gold transforms into a photorealistic, metallic car key with brilliant hyperreal details and metallic glow, golden aura pulsing, every motion snappy, ultra-crisp and fluid, dynamic flash lighting, motion blur, dazzling VFX intro; entire sequence high-end cinematic, immersive, all shots elegantly cascading and blending with powerful, kinetic continuous dynamic CG camera movements, deep vignette, super high realism, zero text.\"\n}",
        }
      }
    },
      {
      "instruction": "# Runway Prompt Generator\n\nYour goal is not to **elaborate** the input, but to **distill** it into an efficient RunwayML prompt emphasizing FPV perspectives and essential dynamic CG camera work. Execute as:\n\n`{role=focused_runway_optimizer; input=[video_concept:str]; process=[extract_primary_visual_elements(), prioritize_fpv_and_continuous_motion(), select_essential_dynamic_cg_movements(), eliminate_redundant_descriptors(), optimize_character_efficiency()]; constraints=[maintain_fpv_focus(), preserve_dynamic_motion(), stay_under_character_limit()]; output={optimized_prompt:str}}`",
      "step": "b",
      "title": "Runway Prompt Generator",
      "input": "{\n  \"initial_prompt\": \"fpv continiously rotating through the entire shot. Zoom out and show the full circle circle spinning in hyperspeed at extreme velocity against a black background; intense golden light trails radiating from the motion; morph the spinning gold seamlessly into a photorealistic car-key (without fading or clipping, but morph) with a metallic glow and hyperreal detail. [fpv][ultra_hyperreal][super_crisp][snappy_smooth_camera_animation][lighting:dynamic_flash][morph][motion_blur][vfx_intro][immersive][golden_aura][vignette][no_text][high_end_cinematic][dynamic_motion][metallic_shine][each_shot_morphs_and_blends_elegantly_together]\",\n  \"runwayml_prompt\": \"FPV camera spins continuously, whirling through dynamic hyperreal motion; camera zooms out, revealing a luminous golden circle spinning at hyperspeed against a deep black background, with intense golden light trails slicing through darkness; in one seamless, elegant morph\\u2014no fade, no clipping\\u2014the spinning gold transforms into a photorealistic, metallic car key with brilliant hyperreal details and metallic glow, golden aura pulsing, every motion snappy, ultra-crisp and fluid, dynamic flash lighting, motion blur, dazzling VFX intro; entire sequence high-end cinematic, immersive, all shots elegantly cascading and blending with powerful, kinetic continuous dynamic CG camera movements, deep vignette, super high realism, zero text.\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"optimized_prompt\": \"FPV camera whirls in continuous dynamic motion, rotating around a luminous golden spinning circle at hyperspeed over a black background. Intense golden light trails and motion blur follow, seamlessly morphing into a hyperreal, metallic-glow car key with crisp, photoreal details. Snappy, smooth CG camera moves with dynamic flash lighting, elegant morph transition, deep vignette, high-end cinematic, immersive with zero text.\"\n}",
        }
      }
    },
      {
      "instruction": "# Runway Prompt Generator\n\nYour goal is not to **expand** but to **compress** into maximum FPV visual efficiency. Execute as:\n\n`{role=precision_synthesizer; input=[concept:str]; process=[isolate_core_visual(), prioritize_fpv_motion(), maximize_dynamic_impact()]; output={precise_prompt:str}}`",
      "step": "c",
      "title": "Runway Prompt Generator",
      "input": "{\n  \"initial_prompt\": \"fpv continiously rotating through the entire shot. Zoom out and show the full circle circle spinning in hyperspeed at extreme velocity against a black background; intense golden light trails radiating from the motion; morph the spinning gold seamlessly into a photorealistic car-key (without fading or clipping, but morph) with a metallic glow and hyperreal detail. [fpv][ultra_hyperreal][super_crisp][snappy_smooth_camera_animation][lighting:dynamic_flash][morph][motion_blur][vfx_intro][immersive][golden_aura][vignette][no_text][high_end_cinematic][dynamic_motion][metallic_shine][each_shot_morphs_and_blends_elegantly_together]\",\n  \"optimized_prompt\": \"FPV camera whirls in continuous dynamic motion, rotating around a luminous golden spinning circle at hyperspeed over a black background. Intense golden light trails and motion blur follow, seamlessly morphing into a hyperreal, metallic-glow car key with crisp, photoreal details. Snappy, smooth CG camera moves with dynamic flash lighting, elegant morph transition, deep vignette, high-end cinematic, immersive with zero text.\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"precise_prompt\": \"FPV rotates at hyperspeed around a gold spinning circle over black, radiating intense golden light trails and motion blur; seamless morph (no fade) transforms spinning gold into ultra-photoreal metallic car key with glowing edges, hyper-sharp detail, dynamic flash lighting; snappy smooth cinematic motion, immersive, deep vignette, no text.\"\n}",
        }
      }
    },
      {
      "instruction": "# Runway Prompt Generator\n\nYour goal is not to **modify** but to **essence** maximum FPV impact. Execute as:\n\n`{role=core_generator; input=[input:any]; process=[distill_fpv_essence(), optimize_motion()]; output={core_prompt:str}}`",
      "step": "d",
      "title": "Runway Prompt Generator",
      "input": "{\n  \"initial_prompt\": \"fpv continiously rotating through the entire shot. Zoom out and show the full circle circle spinning in hyperspeed at extreme velocity against a black background; intense golden light trails radiating from the motion; morph the spinning gold seamlessly into a photorealistic car-key (without fading or clipping, but morph) with a metallic glow and hyperreal detail. [fpv][ultra_hyperreal][super_crisp][snappy_smooth_camera_animation][lighting:dynamic_flash][morph][motion_blur][vfx_intro][immersive][golden_aura][vignette][no_text][high_end_cinematic][dynamic_motion][metallic_shine][each_shot_morphs_and_blends_elegantly_together]\",\n  \"precise_prompt\": \"FPV rotates at hyperspeed around a gold spinning circle over black, radiating intense golden light trails and motion blur; seamless morph (no fade) transforms spinning gold into ultra-photoreal metallic car key with glowing edges, hyper-sharp detail, dynamic flash lighting; snappy smooth cinematic motion, immersive, deep vignette, no text.\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"core_prompt\": \"FPV perspective continuously rotates at extreme velocity around a luminous gold spinning circle on a pure black background, emitting intense golden light trails and dynamic motion blur. At peak speed, seamlessly morph the spinning gold\u2014without fading\u2014into a hyper-realistic, metallic car key with glowing edges and ultra-sharp details, all rendered with dynamic, cinematic lighting and snappy-smooth camera movement. Deep vignette, immersive energy, no text.\"\n}",
        }
      }
    }