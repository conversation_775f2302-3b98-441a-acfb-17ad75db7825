  {
  "initial_prompt": "# Video Transcript Condensation\n\nConversation ID: 67701550-e6fc-8008-944e-c5eea2d8d740\n\n## Message 1\n\nplease write this as a prompt-chain:\n\n{\n    \"scenario_type\": \"Video Transcript Condensation and Objectivization\",\n    \"scenario_context\": \"To transform video transcripts into concise, objective, and structured templates that capture the essential information in a concistently and readily digestible format.\",\n    \"tone\": \"neutral\",\n    \"audience\": \"developers\",\n    \"instructions\": [\n        \"Focus on the most critical points relevant to understanding the codebase, system architecture, or API functionality.\",\n        \"Keep the summary concise: no more than one-third the length of the input.\",\n        \"Detailed description of the scenario. What problem does it solve? What is the context?\",\n        \"Omit unnecessary details, such as historical context or implementation specifics not essential for a general understanding.\"\n    ],\n    \"format\": {\n        \"sections\": [\n            \"High Level Summary\",\n            \"Key Details\",\n            \"Potential Issues and Risks\",\n            \"Recommended Next Steps\"\n        ]\n    },\n    \"example_output\": {\n        \"title\": \"High-Level Overview of the Prompt Framework\",\n        \"sections\": {\n            \"High Level Summary\": \"This framework assists developers in creating scenario-driven prompt templates that guide Language Learning Models (LLMs).\",\n            \"Key Details\": \"- Utilizes a JSON configuration for structured instructions.\\n- Employs minimalistic XML templates with placeholders for flexibility.\\n- Easily adaptable to various scenarios through modular design.\",\n            \"Recommended Next Steps\": \"- Refine templates based on user feedback.\\n- Incorporate additional scenarios in the `scenario_config.json`.\\n- Emphasize brevity and relevance in all documentation.\"\n        }\n    },\n    \"inputs\": {\n        \"initial\": \"initial_input.txt\"\n    },\n    \"keywords\": \"a scenario-driven prompt template (summarize_template.xml) to improve coherence and quality\",\n    \"placeholders\": {\n        \"content\": \"[PASTE_TEXT_HERE]\",\n        \"keywords\": \"[KEYWORDS_HERE]\",\n        \"scenario_type\": \"[SCENARIO_TYPE]\",\n        \"scenario_context\": \"[SCENARIO_CONTEXT]\",\n        \"tone\": \"[TONE]\",\n        \"audience\": \"[AUDIENCE]\"\n    },\n    \"default_parameters\": {\n        \"model\": \"gpt-4-0125-preview\",\n        \"temperature\": 0.7,\n        \"max_tokens\": 800\n    },\n\n    \"stages\": [\n        {\n            \"name\": \"main_summarization\",\n            \"prompts_sequence\": [\n                { \"path\": \"prompts/summarize/level1/summarize.xml\", \"input_key\": \"initial\" },\n                { \"path\": \"prompts/summarize/level2/summarize.xml\" },\n                { \"path\": \"prompts/summarize/level3/summarize.xml\" },\n                { \"path\": \"prompts/summarize/level4/summarize_template.xml\" }\n            ],\n            \"quality_check\": {\n                \"indicators\": [\"clear\", \"example\", \"concise\", \"relevant\"],\n                \"min_count\": 2,\n                \"min_prompts_before_early_termination\": 4\n            },\n            \"run_if\": \"true\"\n        }\n    ]\n}\n\n## Message 2\n\nAll the files uploaded by the user have been fully loaded. Searching won't provide additional information.\n\n## Message 3\n\nBased on the details you've provided and the reference file on prompt engineering best practices, I'll construct a prompt chain to transform video transcripts into concise, objective, and structured templates. Here's the proposed chain:\n\n---\n\n### **Prompt Chain for Video Transcript Condensation and Objectivization**\n\n```markdown\n#### Prompt 1: Establish Context and Goals\n**Instruction:** \n\"Given the context of condensing a video transcript, analyze the provided content to identify the key objectives of the transcript. Provide a concise summary of its main topic and intended outcome in no more than three sentences.\"\n\n**Input Placeholder:** [PASTE_VIDEO_TRANSCRIPT_HERE]\n\n**Expected Output:**\n- Brief statement of the transcript\u2019s purpose and context.\n- Identification of the primary objective or theme.\n\n---\n\n#### Prompt 2: Extract High-Level Summary\n**Instruction:** \n\"Using the summary provided, create a 'High-Level Summary' section. Focus on the overarching purpose of the transcript while omitting implementation details or extraneous information. Limit the response to 100 words.\"\n\n**Expected Output Format:**\n- **High-Level Summary:** [Summary Content]\n\n---\n\n#### Prompt 3: Extract Key Details\n**Instruction:** \n\"From the transcript, distill critical details that elucidate the purpose or functionality discussed. Ensure these details are structured in bullet points and include no more than five points.\"\n\n**Expected Output Format:**\n- **Key Details:** \n  - [Key Detail 1]\n  - [Key Detail 2]\n  - [Key Detail 3]\n\n---\n\n#### Prompt 4: Analyze Risks and Issues\n**Instruction:** \n\"Identify potential risks, challenges, or areas of concern mentioned or implied in the transcript. Articulate these as bullet points, focusing on those most relevant to developers.\"\n\n**Expected Output Format:**\n- **Potential Issues and Risks:**\n  - [Issue or Risk 1]\n  - [Issue or Risk 2]\n\n---\n\n#### Prompt 5: Recommend Next Steps\n**Instruction:** \n\"Based on the summarized content, provide actionable recommendations or next steps for developers. Ensure that these are concise and achievable, using no more than five bullet points.\"\n\n**Expected Output Format:**\n- **Recommended Next Steps:** \n  - [Next Step 1]\n  - [Next Step 2]\n\n---\n\n### **Execution Notes**\n1. **Sequential Execution:** \n   - Each prompt is executed in sequence to build the final structured summary.\n   - After each step, the output is reviewed for clarity and completeness.\n2. **Interactivity:**\n   - Ask the user for confirmation before proceeding to the next prompt.\n   - Adjust prompts dynamically based on user feedback.\n\n3. **Quality Control:**\n   - Ensure adherence to tone (neutral) and audience relevance (developers).\n   - Monitor for brevity and clarity at each stage.\n\n---\n\nWould you like me to execute the first prompt with a sample input, or do you have a specific video transcript you'd like to use?",
  "sequence_id": "0003|0002",
  "results": [
      {
      "instruction": "# Prompt Enhancer\n\nYour goal is not to **rewrite** the input prompt, but to **enhance** it by maximizing clarity, conciseness, and precision while preserving all original intent and adding comprehensive guidance for optimal LLM performance. Execute as:\n\n`{role=comprehensive_prompt_enhancer; input=[original_prompt:str]; process=[analyze_clarity_gaps(), identify_ambiguities(), add_context_markers(), specify_output_format(), incorporate_role_definition(), structure_logical_flow(), refine_language_precision()]; constraints=[preserve_original_intent(), maximize_LLM_effectiveness(), ensure_comprehensive_guidance()]; requirements=[eliminate_ambiguity(), provide_complete_context(), specify_exact_requirements()]; output={enhanced_prompt:str}}`",
      "step": "a",
      "title": "Prompt Enhancer",
      "input": "# Video Transcript Condensation\n\nConversation ID: 67701550-e6fc-8008-944e-c5eea2d8d740\n\n## Message 1\n\nplease write this as a prompt-chain:\n\n{\n    \"scenario_type\": \"Video Transcript Condensation and Objectivization\",\n    \"scenario_context\": \"To transform video transcripts into concise, objective, and structured templates that capture the essential information in a concistently and readily digestible format.\",\n    \"tone\": \"neutral\",\n    \"audience\": \"developers\",\n    \"instructions\": [\n        \"Focus on the most critical points relevant to understanding the codebase, system architecture, or API functionality.\",\n        \"Keep the summary concise: no more than one-third the length of the input.\",\n        \"Detailed description of the scenario. What problem does it solve? What is the context?\",\n        \"Omit unnecessary details, such as historical context or implementation specifics not essential for a general understanding.\"\n    ],\n    \"format\": {\n        \"sections\": [\n            \"High Level Summary\",\n            \"Key Details\",\n            \"Potential Issues and Risks\",\n            \"Recommended Next Steps\"\n        ]\n    },\n    \"example_output\": {\n        \"title\": \"High-Level Overview of the Prompt Framework\",\n        \"sections\": {\n            \"High Level Summary\": \"This framework assists developers in creating scenario-driven prompt templates that guide Language Learning Models (LLMs).\",\n            \"Key Details\": \"- Utilizes a JSON configuration for structured instructions.\\n- Employs minimalistic XML templates with placeholders for flexibility.\\n- Easily adaptable to various scenarios through modular design.\",\n            \"Recommended Next Steps\": \"- Refine templates based on user feedback.\\n- Incorporate additional scenarios in the `scenario_config.json`.\\n- Emphasize brevity and relevance in all documentation.\"\n        }\n    },\n    \"inputs\": {\n        \"initial\": \"initial_input.txt\"\n    },\n    \"keywords\": \"a scenario-driven prompt template (summarize_template.xml) to improve coherence and quality\",\n    \"placeholders\": {\n        \"content\": \"[PASTE_TEXT_HERE]\",\n        \"keywords\": \"[KEYWORDS_HERE]\",\n        \"scenario_type\": \"[SCENARIO_TYPE]\",\n        \"scenario_context\": \"[SCENARIO_CONTEXT]\",\n        \"tone\": \"[TONE]\",\n        \"audience\": \"[AUDIENCE]\"\n    },\n    \"default_parameters\": {\n        \"model\": \"gpt-4-0125-preview\",\n        \"temperature\": 0.7,\n        \"max_tokens\": 800\n    },\n\n    \"stages\": [\n        {\n            \"name\": \"main_summarization\",\n            \"prompts_sequence\": [\n                { \"path\": \"prompts/summarize/level1/summarize.xml\", \"input_key\": \"initial\" },\n                { \"path\": \"prompts/summarize/level2/summarize.xml\" },\n                { \"path\": \"prompts/summarize/level3/summarize.xml\" },\n                { \"path\": \"prompts/summarize/level4/summarize_template.xml\" }\n            ],\n            \"quality_check\": {\n                \"indicators\": [\"clear\", \"example\", \"concise\", \"relevant\"],\n                \"min_count\": 2,\n                \"min_prompts_before_early_termination\": 4\n            },\n            \"run_if\": \"true\"\n        }\n    ]\n}\n\n## Message 2\n\nAll the files uploaded by the user have been fully loaded. Searching won't provide additional information.\n\n## Message 3\n\nBased on the details you've provided and the reference file on prompt engineering best practices, I'll construct a prompt chain to transform video transcripts into concise, objective, and structured templates. Here's the proposed chain:\n\n---\n\n### **Prompt Chain for Video Transcript Condensation and Objectivization**\n\n```markdown\n#### Prompt 1: Establish Context and Goals\n**Instruction:** \n\"Given the context of condensing a video transcript, analyze the provided content to identify the key objectives of the transcript. Provide a concise summary of its main topic and intended outcome in no more than three sentences.\"\n\n**Input Placeholder:** [PASTE_VIDEO_TRANSCRIPT_HERE]\n\n**Expected Output:**\n- Brief statement of the transcript\u2019s purpose and context.\n- Identification of the primary objective or theme.\n\n---\n\n#### Prompt 2: Extract High-Level Summary\n**Instruction:** \n\"Using the summary provided, create a 'High-Level Summary' section. Focus on the overarching purpose of the transcript while omitting implementation details or extraneous information. Limit the response to 100 words.\"\n\n**Expected Output Format:**\n- **High-Level Summary:** [Summary Content]\n\n---\n\n#### Prompt 3: Extract Key Details\n**Instruction:** \n\"From the transcript, distill critical details that elucidate the purpose or functionality discussed. Ensure these details are structured in bullet points and include no more than five points.\"\n\n**Expected Output Format:**\n- **Key Details:** \n  - [Key Detail 1]\n  - [Key Detail 2]\n  - [Key Detail 3]\n\n---\n\n#### Prompt 4: Analyze Risks and Issues\n**Instruction:** \n\"Identify potential risks, challenges, or areas of concern mentioned or implied in the transcript. Articulate these as bullet points, focusing on those most relevant to developers.\"\n\n**Expected Output Format:**\n- **Potential Issues and Risks:**\n  - [Issue or Risk 1]\n  - [Issue or Risk 2]\n\n---\n\n#### Prompt 5: Recommend Next Steps\n**Instruction:** \n\"Based on the summarized content, provide actionable recommendations or next steps for developers. Ensure that these are concise and achievable, using no more than five bullet points.\"\n\n**Expected Output Format:**\n- **Recommended Next Steps:** \n  - [Next Step 1]\n  - [Next Step 2]\n\n---\n\n### **Execution Notes**\n1. **Sequential Execution:** \n   - Each prompt is executed in sequence to build the final structured summary.\n   - After each step, the output is reviewed for clarity and completeness.\n2. **Interactivity:**\n   - Ask the user for confirmation before proceeding to the next prompt.\n   - Adjust prompts dynamically based on user feedback.\n\n3. **Quality Control:**\n   - Ensure adherence to tone (neutral) and audience relevance (developers).\n   - Monitor for brevity and clarity at each stage.\n\n---\n\nWould you like me to execute the first prompt with a sample input, or do you have a specific video transcript you'd like to use?",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"enhanced_prompt\": \"You are acting as an expert prompt chain developer with a focus on Video Transcript Condensation and Objectivization for technical developer audiences. Your task is to design a multi-stage prompt chain that efficiently transforms raw video transcripts into concise, objective, and structured summaries following strict quality and content requirements.\\n\\nContext Markers:\\n- Scenario Type: Video Transcript Condensation and Objectivization\\n- Scenario Context: Reformat raw video transcripts into succinct, neutral, developer-oriented templates highlighting essential system, codebase, or API information while omitting non-critical historical or implementation details.\\n- Intended Audience: Developers seeking rapid comprehension of high-level technical content.\\n- Tone: Neutral and objective\\n\\nOverall Objective:\\nDevelop a prompt chain that converts a video transcript into a structured summary template with the following sections: High Level Summary, Key Details, Potential Issues and Risks, and Recommended Next Steps. All outputs must be concise, objective, and developer-relevant. Summaries should not exceed one-third the original transcript length.\\n\\nPrompt Chain Structure:\\n1. Prompt 1: Context and Objective Extraction\\n   - Task: Analyze the input transcript to extract and state its core purpose, high-level context, and intended technical outcomes in 1-3 concise sentences.\\n   - Output: Brief, neutral statement providing scenario background and transcript's main objectives; 2-3 sentences maximum.\\n   - Input Placeholder: [PASTE_VIDEO_TRANSCRIPT_HERE]\\n\\n2. Prompt 2: High-Level Summary\\n   - Task: Using the results from Prompt 1, distill the overarching purpose, scope, and major thematic points, omitting all granular implementation detail.\\n   - Output: SECTION: High Level Summary (one paragraph, <=100 words)\\n\\n3. Prompt 3: Key Details Extraction\\n   - Task: From the transcript, identify and bullet up to five salient details critical for technical understanding (e.g., architecture components, essential workflow steps, API entry points, or non-trivial design decisions).\\n   - Output: SECTION: Key Details, formatted as a bulleted list, max 5 points.\\n\\n4. Prompt 4: Issues and Risks Identification\\n   - Task: Extract risks, limitations, or challenges explicitly stated or implied within the transcript that are most relevant to developers or could impact implementation.\\n   - Output: SECTION: Potential Issues and Risks, bulleted format, max 5 points.\\n\\n5. Prompt 5: Actionable Recommendations\\n   - Task: Based on the condensed summary and identified issues, output up to five actionable, practical next steps or recommendations for developer follow-up.\\n   - Output: SECTION: Recommended Next Steps, bulleted format, max 5 points.\\n\\nExecution Logic:\\n- Each prompt is run sequentially; each dependent on the prior output. At every stage, insist on clarity, conciseness, and exclusive focus on the most relevant aspects for the developer audience.\\n- After each prompt, verify the output for compliance with both structural and content requirements; ensure strict neutrality and relevance.\\n- User Interaction: Pause for user confirmation or edits at each stage before proceeding.\\n\\nQuality Criteria:\\n- All outputs are clear, neutral, relevant, concise, actionable for developer context.\\n- Each template section is present in the final summary, totaling less than one-third of the input transcript length.\\n\\nOutput Format Specification:\\n- Output should always be a structured template with labeled sections:\\n  {\\n    \\\"High Level Summary\\\": \\\"...\\\",\\n    \\\"Key Details\\\": [\\\"...\\\", \\\"...\\\"],\\n    \\\"Potential Issues and Risks\\\": [\\\"...\\\", \\\"...\\\"],\\n    \\\"Recommended Next Steps\\\": [\\\"...\\\", \\\"...\\\"]\\n  }\\n- Use provided placeholders for initial transcript input: [PASTE_VIDEO_TRANSCRIPT_HERE].\\n- Consistently use neutral language and bullet formatting for relevant sections.\\n\\nInstructions Checklist for LLMs:\\n- Eliminate ambiguity by explicitly defining section scope and format.\\n- Preserve the intent to convert transcripts into actionable, succinct technical documentation for developers.\\n- Require adherence to content/length/format standards.\\n- Ensure role orientation: act as a technical summarization workflow designer and executor.\\n\\nIf sample input is provided, begin execution at Prompt 1; otherwise, pause and request a transcript input from the user.\"\n}",
        }
      }
    },
      {
      "instruction": "# Prompt Enhancer\n\nYour goal is not to **expand** the input prompt, but to **refine** it by sharpening core instructions and eliminating unnecessary elements while maintaining essential guidance. Execute as:\n\n`{role=focused_prompt_enhancer; input=[original_prompt:str]; process=[identify_core_instruction(), remove_redundancies(), sharpen_key_directives(), streamline_requirements()]; constraints=[maintain_clarity(), preserve_effectiveness()]; requirements=[eliminate_superfluous_words(), retain_essential_guidance()]; output={refined_prompt:str}}`",
      "step": "b",
      "title": "Prompt Enhancer",
      "input": "{\n  \"initial_prompt\": \"# Video Transcript Condensation\\n\\nConversation ID: 67701550-e6fc-8008-944e-c5eea2d8d740\\n\\n## Message 1\\n\\nplease write this as a prompt-chain:\\n\\n{\\n    \\\"scenario_type\\\": \\\"Video Transcript Condensation and Objectivization\\\",\\n    \\\"scenario_context\\\": \\\"To transform video transcripts into concise, objective, and structured templates that capture the essential information in a concistently and readily digestible format.\\\",\\n    \\\"tone\\\": \\\"neutral\\\",\\n    \\\"audience\\\": \\\"developers\\\",\\n    \\\"instructions\\\": [\\n        \\\"Focus on the most critical points relevant to understanding the codebase, system architecture, or API functionality.\\\",\\n        \\\"Keep the summary concise: no more than one-third the length of the input.\\\",\\n        \\\"Detailed description of the scenario. What problem does it solve? What is the context?\\\",\\n        \\\"Omit unnecessary details, such as historical context or implementation specifics not essential for a general understanding.\\\"\\n    ],\\n    \\\"format\\\": {\\n        \\\"sections\\\": [\\n            \\\"High Level Summary\\\",\\n            \\\"Key Details\\\",\\n            \\\"Potential Issues and Risks\\\",\\n            \\\"Recommended Next Steps\\\"\\n        ]\\n    },\\n    \\\"example_output\\\": {\\n        \\\"title\\\": \\\"High-Level Overview of the Prompt Framework\\\",\\n        \\\"sections\\\": {\\n            \\\"High Level Summary\\\": \\\"This framework assists developers in creating scenario-driven prompt templates that guide Language Learning Models (LLMs).\\\",\\n            \\\"Key Details\\\": \\\"- Utilizes a JSON configuration for structured instructions.\\\\n- Employs minimalistic XML templates with placeholders for flexibility.\\\\n- Easily adaptable to various scenarios through modular design.\\\",\\n            \\\"Recommended Next Steps\\\": \\\"- Refine templates based on user feedback.\\\\n- Incorporate additional scenarios in the `scenario_config.json`.\\\\n- Emphasize brevity and relevance in all documentation.\\\"\\n        }\\n    },\\n    \\\"inputs\\\": {\\n        \\\"initial\\\": \\\"initial_input.txt\\\"\\n    },\\n    \\\"keywords\\\": \\\"a scenario-driven prompt template (summarize_template.xml) to improve coherence and quality\\\",\\n    \\\"placeholders\\\": {\\n        \\\"content\\\": \\\"[PASTE_TEXT_HERE]\\\",\\n        \\\"keywords\\\": \\\"[KEYWORDS_HERE]\\\",\\n        \\\"scenario_type\\\": \\\"[SCENARIO_TYPE]\\\",\\n        \\\"scenario_context\\\": \\\"[SCENARIO_CONTEXT]\\\",\\n        \\\"tone\\\": \\\"[TONE]\\\",\\n        \\\"audience\\\": \\\"[AUDIENCE]\\\"\\n    },\\n    \\\"default_parameters\\\": {\\n        \\\"model\\\": \\\"gpt-4-0125-preview\\\",\\n        \\\"temperature\\\": 0.7,\\n        \\\"max_tokens\\\": 800\\n    },\\n\\n    \\\"stages\\\": [\\n        {\\n            \\\"name\\\": \\\"main_summarization\\\",\\n            \\\"prompts_sequence\\\": [\\n                { \\\"path\\\": \\\"prompts/summarize/level1/summarize.xml\\\", \\\"input_key\\\": \\\"initial\\\" },\\n                { \\\"path\\\": \\\"prompts/summarize/level2/summarize.xml\\\" },\\n                { \\\"path\\\": \\\"prompts/summarize/level3/summarize.xml\\\" },\\n                { \\\"path\\\": \\\"prompts/summarize/level4/summarize_template.xml\\\" }\\n            ],\\n            \\\"quality_check\\\": {\\n                \\\"indicators\\\": [\\\"clear\\\", \\\"example\\\", \\\"concise\\\", \\\"relevant\\\"],\\n                \\\"min_count\\\": 2,\\n                \\\"min_prompts_before_early_termination\\\": 4\\n            },\\n            \\\"run_if\\\": \\\"true\\\"\\n        }\\n    ]\\n}\\n\\n## Message 2\\n\\nAll the files uploaded by the user have been fully loaded. Searching won't provide additional information.\\n\\n## Message 3\\n\\nBased on the details you've provided and the reference file on prompt engineering best practices, I'll construct a prompt chain to transform video transcripts into concise, objective, and structured templates. Here's the proposed chain:\\n\\n---\\n\\n### **Prompt Chain for Video Transcript Condensation and Objectivization**\\n\\n```markdown\\n#### Prompt 1: Establish Context and Goals\\n**Instruction:** \\n\\\"Given the context of condensing a video transcript, analyze the provided content to identify the key objectives of the transcript. Provide a concise summary of its main topic and intended outcome in no more than three sentences.\\\"\\n\\n**Input Placeholder:** [PASTE_VIDEO_TRANSCRIPT_HERE]\\n\\n**Expected Output:**\\n- Brief statement of the transcript\\u2019s purpose and context.\\n- Identification of the primary objective or theme.\\n\\n---\\n\\n#### Prompt 2: Extract High-Level Summary\\n**Instruction:** \\n\\\"Using the summary provided, create a 'High-Level Summary' section. Focus on the overarching purpose of the transcript while omitting implementation details or extraneous information. Limit the response to 100 words.\\\"\\n\\n**Expected Output Format:**\\n- **High-Level Summary:** [Summary Content]\\n\\n---\\n\\n#### Prompt 3: Extract Key Details\\n**Instruction:** \\n\\\"From the transcript, distill critical details that elucidate the purpose or functionality discussed. Ensure these details are structured in bullet points and include no more than five points.\\\"\\n\\n**Expected Output Format:**\\n- **Key Details:** \\n  - [Key Detail 1]\\n  - [Key Detail 2]\\n  - [Key Detail 3]\\n\\n---\\n\\n#### Prompt 4: Analyze Risks and Issues\\n**Instruction:** \\n\\\"Identify potential risks, challenges, or areas of concern mentioned or implied in the transcript. Articulate these as bullet points, focusing on those most relevant to developers.\\\"\\n\\n**Expected Output Format:**\\n- **Potential Issues and Risks:**\\n  - [Issue or Risk 1]\\n  - [Issue or Risk 2]\\n\\n---\\n\\n#### Prompt 5: Recommend Next Steps\\n**Instruction:** \\n\\\"Based on the summarized content, provide actionable recommendations or next steps for developers. Ensure that these are concise and achievable, using no more than five bullet points.\\\"\\n\\n**Expected Output Format:**\\n- **Recommended Next Steps:** \\n  - [Next Step 1]\\n  - [Next Step 2]\\n\\n---\\n\\n### **Execution Notes**\\n1. **Sequential Execution:** \\n   - Each prompt is executed in sequence to build the final structured summary.\\n   - After each step, the output is reviewed for clarity and completeness.\\n2. **Interactivity:**\\n   - Ask the user for confirmation before proceeding to the next prompt.\\n   - Adjust prompts dynamically based on user feedback.\\n\\n3. **Quality Control:**\\n   - Ensure adherence to tone (neutral) and audience relevance (developers).\\n   - Monitor for brevity and clarity at each stage.\\n\\n---\\n\\nWould you like me to execute the first prompt with a sample input, or do you have a specific video transcript you'd like to use?\",\n  \"enhanced_prompt\": \"You are acting as an expert prompt chain developer with a focus on Video Transcript Condensation and Objectivization for technical developer audiences. Your task is to design a multi-stage prompt chain that efficiently transforms raw video transcripts into concise, objective, and structured summaries following strict quality and content requirements.\\n\\nContext Markers:\\n- Scenario Type: Video Transcript Condensation and Objectivization\\n- Scenario Context: Reformat raw video transcripts into succinct, neutral, developer-oriented templates highlighting essential system, codebase, or API information while omitting non-critical historical or implementation details.\\n- Intended Audience: Developers seeking rapid comprehension of high-level technical content.\\n- Tone: Neutral and objective\\n\\nOverall Objective:\\nDevelop a prompt chain that converts a video transcript into a structured summary template with the following sections: High Level Summary, Key Details, Potential Issues and Risks, and Recommended Next Steps. All outputs must be concise, objective, and developer-relevant. Summaries should not exceed one-third the original transcript length.\\n\\nPrompt Chain Structure:\\n1. Prompt 1: Context and Objective Extraction\\n   - Task: Analyze the input transcript to extract and state its core purpose, high-level context, and intended technical outcomes in 1-3 concise sentences.\\n   - Output: Brief, neutral statement providing scenario background and transcript's main objectives; 2-3 sentences maximum.\\n   - Input Placeholder: [PASTE_VIDEO_TRANSCRIPT_HERE]\\n\\n2. Prompt 2: High-Level Summary\\n   - Task: Using the results from Prompt 1, distill the overarching purpose, scope, and major thematic points, omitting all granular implementation detail.\\n   - Output: SECTION: High Level Summary (one paragraph, <=100 words)\\n\\n3. Prompt 3: Key Details Extraction\\n   - Task: From the transcript, identify and bullet up to five salient details critical for technical understanding (e.g., architecture components, essential workflow steps, API entry points, or non-trivial design decisions).\\n   - Output: SECTION: Key Details, formatted as a bulleted list, max 5 points.\\n\\n4. Prompt 4: Issues and Risks Identification\\n   - Task: Extract risks, limitations, or challenges explicitly stated or implied within the transcript that are most relevant to developers or could impact implementation.\\n   - Output: SECTION: Potential Issues and Risks, bulleted format, max 5 points.\\n\\n5. Prompt 5: Actionable Recommendations\\n   - Task: Based on the condensed summary and identified issues, output up to five actionable, practical next steps or recommendations for developer follow-up.\\n   - Output: SECTION: Recommended Next Steps, bulleted format, max 5 points.\\n\\nExecution Logic:\\n- Each prompt is run sequentially; each dependent on the prior output. At every stage, insist on clarity, conciseness, and exclusive focus on the most relevant aspects for the developer audience.\\n- After each prompt, verify the output for compliance with both structural and content requirements; ensure strict neutrality and relevance.\\n- User Interaction: Pause for user confirmation or edits at each stage before proceeding.\\n\\nQuality Criteria:\\n- All outputs are clear, neutral, relevant, concise, actionable for developer context.\\n- Each template section is present in the final summary, totaling less than one-third of the input transcript length.\\n\\nOutput Format Specification:\\n- Output should always be a structured template with labeled sections:\\n  {\\n    \\\"High Level Summary\\\": \\\"...\\\",\\n    \\\"Key Details\\\": [\\\"...\\\", \\\"...\\\"],\\n    \\\"Potential Issues and Risks\\\": [\\\"...\\\", \\\"...\\\"],\\n    \\\"Recommended Next Steps\\\": [\\\"...\\\", \\\"...\\\"]\\n  }\\n- Use provided placeholders for initial transcript input: [PASTE_VIDEO_TRANSCRIPT_HERE].\\n- Consistently use neutral language and bullet formatting for relevant sections.\\n\\nInstructions Checklist for LLMs:\\n- Eliminate ambiguity by explicitly defining section scope and format.\\n- Preserve the intent to convert transcripts into actionable, succinct technical documentation for developers.\\n- Require adherence to content/length/format standards.\\n- Ensure role orientation: act as a technical summarization workflow designer and executor.\\n\\nIf sample input is provided, begin execution at Prompt 1; otherwise, pause and request a transcript input from the user.\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"refined_prompt\": \"Design a prompt chain to condense video transcripts into concise, objective, developer-focused summaries. The process must: 1) extract the transcript's purpose and context in 1\u20133 sentences; 2) create a 'High Level Summary' (\u2264100 words) omitting granular implementation; 3) list up to five essential technical details; 4) identify up to five potential issues or risks; and 5) recommend up to five actionable next steps for developers. Each stage depends on the previous output and must remain clear, neutral, and under one-third of the original length. Pause for user confirmation after each stage. Output format:\\n{\\n  \\\"High Level Summary\\\": \\\"...\\\",\\n  \\\"Key Details\\\": [\\\"...\\\"],\\n  \\\"Potential Issues and Risks\\\": [\\\"...\\\"],\\n  \\\"Recommended Next Steps\\\": [\\\"...\\\"]\\n} \\nUse [PASTE_VIDEO_TRANSCRIPT_HERE] as the initial input placeholder. Begin at step 1 if input is provided; otherwise, request the transcript.\"\n}",
        }
      }
    },
      {
      "instruction": "# Prompt Enhancer\n\nYour goal is not to **elaborate** but to **distill** the prompt to its most effective essential elements. Execute as:\n\n`{role=precision_enhancer; input=[original_prompt:str]; process=[extract_core_directive(), eliminate_redundancy(), maximize_word_efficiency()]; output={precise_prompt:str}}`",
      "step": "c",
      "title": "Prompt Enhancer",
      "input": "{\n  \"initial_prompt\": \"# Video Transcript Condensation\\n\\nConversation ID: 67701550-e6fc-8008-944e-c5eea2d8d740\\n\\n## Message 1\\n\\nplease write this as a prompt-chain:\\n\\n{\\n    \\\"scenario_type\\\": \\\"Video Transcript Condensation and Objectivization\\\",\\n    \\\"scenario_context\\\": \\\"To transform video transcripts into concise, objective, and structured templates that capture the essential information in a concistently and readily digestible format.\\\",\\n    \\\"tone\\\": \\\"neutral\\\",\\n    \\\"audience\\\": \\\"developers\\\",\\n    \\\"instructions\\\": [\\n        \\\"Focus on the most critical points relevant to understanding the codebase, system architecture, or API functionality.\\\",\\n        \\\"Keep the summary concise: no more than one-third the length of the input.\\\",\\n        \\\"Detailed description of the scenario. What problem does it solve? What is the context?\\\",\\n        \\\"Omit unnecessary details, such as historical context or implementation specifics not essential for a general understanding.\\\"\\n    ],\\n    \\\"format\\\": {\\n        \\\"sections\\\": [\\n            \\\"High Level Summary\\\",\\n            \\\"Key Details\\\",\\n            \\\"Potential Issues and Risks\\\",\\n            \\\"Recommended Next Steps\\\"\\n        ]\\n    },\\n    \\\"example_output\\\": {\\n        \\\"title\\\": \\\"High-Level Overview of the Prompt Framework\\\",\\n        \\\"sections\\\": {\\n            \\\"High Level Summary\\\": \\\"This framework assists developers in creating scenario-driven prompt templates that guide Language Learning Models (LLMs).\\\",\\n            \\\"Key Details\\\": \\\"- Utilizes a JSON configuration for structured instructions.\\\\n- Employs minimalistic XML templates with placeholders for flexibility.\\\\n- Easily adaptable to various scenarios through modular design.\\\",\\n            \\\"Recommended Next Steps\\\": \\\"- Refine templates based on user feedback.\\\\n- Incorporate additional scenarios in the `scenario_config.json`.\\\\n- Emphasize brevity and relevance in all documentation.\\\"\\n        }\\n    },\\n    \\\"inputs\\\": {\\n        \\\"initial\\\": \\\"initial_input.txt\\\"\\n    },\\n    \\\"keywords\\\": \\\"a scenario-driven prompt template (summarize_template.xml) to improve coherence and quality\\\",\\n    \\\"placeholders\\\": {\\n        \\\"content\\\": \\\"[PASTE_TEXT_HERE]\\\",\\n        \\\"keywords\\\": \\\"[KEYWORDS_HERE]\\\",\\n        \\\"scenario_type\\\": \\\"[SCENARIO_TYPE]\\\",\\n        \\\"scenario_context\\\": \\\"[SCENARIO_CONTEXT]\\\",\\n        \\\"tone\\\": \\\"[TONE]\\\",\\n        \\\"audience\\\": \\\"[AUDIENCE]\\\"\\n    },\\n    \\\"default_parameters\\\": {\\n        \\\"model\\\": \\\"gpt-4-0125-preview\\\",\\n        \\\"temperature\\\": 0.7,\\n        \\\"max_tokens\\\": 800\\n    },\\n\\n    \\\"stages\\\": [\\n        {\\n            \\\"name\\\": \\\"main_summarization\\\",\\n            \\\"prompts_sequence\\\": [\\n                { \\\"path\\\": \\\"prompts/summarize/level1/summarize.xml\\\", \\\"input_key\\\": \\\"initial\\\" },\\n                { \\\"path\\\": \\\"prompts/summarize/level2/summarize.xml\\\" },\\n                { \\\"path\\\": \\\"prompts/summarize/level3/summarize.xml\\\" },\\n                { \\\"path\\\": \\\"prompts/summarize/level4/summarize_template.xml\\\" }\\n            ],\\n            \\\"quality_check\\\": {\\n                \\\"indicators\\\": [\\\"clear\\\", \\\"example\\\", \\\"concise\\\", \\\"relevant\\\"],\\n                \\\"min_count\\\": 2,\\n                \\\"min_prompts_before_early_termination\\\": 4\\n            },\\n            \\\"run_if\\\": \\\"true\\\"\\n        }\\n    ]\\n}\\n\\n## Message 2\\n\\nAll the files uploaded by the user have been fully loaded. Searching won't provide additional information.\\n\\n## Message 3\\n\\nBased on the details you've provided and the reference file on prompt engineering best practices, I'll construct a prompt chain to transform video transcripts into concise, objective, and structured templates. Here's the proposed chain:\\n\\n---\\n\\n### **Prompt Chain for Video Transcript Condensation and Objectivization**\\n\\n```markdown\\n#### Prompt 1: Establish Context and Goals\\n**Instruction:** \\n\\\"Given the context of condensing a video transcript, analyze the provided content to identify the key objectives of the transcript. Provide a concise summary of its main topic and intended outcome in no more than three sentences.\\\"\\n\\n**Input Placeholder:** [PASTE_VIDEO_TRANSCRIPT_HERE]\\n\\n**Expected Output:**\\n- Brief statement of the transcript\\u2019s purpose and context.\\n- Identification of the primary objective or theme.\\n\\n---\\n\\n#### Prompt 2: Extract High-Level Summary\\n**Instruction:** \\n\\\"Using the summary provided, create a 'High-Level Summary' section. Focus on the overarching purpose of the transcript while omitting implementation details or extraneous information. Limit the response to 100 words.\\\"\\n\\n**Expected Output Format:**\\n- **High-Level Summary:** [Summary Content]\\n\\n---\\n\\n#### Prompt 3: Extract Key Details\\n**Instruction:** \\n\\\"From the transcript, distill critical details that elucidate the purpose or functionality discussed. Ensure these details are structured in bullet points and include no more than five points.\\\"\\n\\n**Expected Output Format:**\\n- **Key Details:** \\n  - [Key Detail 1]\\n  - [Key Detail 2]\\n  - [Key Detail 3]\\n\\n---\\n\\n#### Prompt 4: Analyze Risks and Issues\\n**Instruction:** \\n\\\"Identify potential risks, challenges, or areas of concern mentioned or implied in the transcript. Articulate these as bullet points, focusing on those most relevant to developers.\\\"\\n\\n**Expected Output Format:**\\n- **Potential Issues and Risks:**\\n  - [Issue or Risk 1]\\n  - [Issue or Risk 2]\\n\\n---\\n\\n#### Prompt 5: Recommend Next Steps\\n**Instruction:** \\n\\\"Based on the summarized content, provide actionable recommendations or next steps for developers. Ensure that these are concise and achievable, using no more than five bullet points.\\\"\\n\\n**Expected Output Format:**\\n- **Recommended Next Steps:** \\n  - [Next Step 1]\\n  - [Next Step 2]\\n\\n---\\n\\n### **Execution Notes**\\n1. **Sequential Execution:** \\n   - Each prompt is executed in sequence to build the final structured summary.\\n   - After each step, the output is reviewed for clarity and completeness.\\n2. **Interactivity:**\\n   - Ask the user for confirmation before proceeding to the next prompt.\\n   - Adjust prompts dynamically based on user feedback.\\n\\n3. **Quality Control:**\\n   - Ensure adherence to tone (neutral) and audience relevance (developers).\\n   - Monitor for brevity and clarity at each stage.\\n\\n---\\n\\nWould you like me to execute the first prompt with a sample input, or do you have a specific video transcript you'd like to use?\",\n  \"refined_prompt\": \"Design a prompt chain to condense video transcripts into concise, objective, developer-focused summaries. The process must: 1) extract the transcript's purpose and context in 1\\u20133 sentences; 2) create a 'High Level Summary' (\\u2264100 words) omitting granular implementation; 3) list up to five essential technical details; 4) identify up to five potential issues or risks; and 5) recommend up to five actionable next steps for developers. Each stage depends on the previous output and must remain clear, neutral, and under one-third of the original length. Pause for user confirmation after each stage. Output format:\\n{\\n  \\\"High Level Summary\\\": \\\"...\\\",\\n  \\\"Key Details\\\": [\\\"...\\\"],\\n  \\\"Potential Issues and Risks\\\": [\\\"...\\\"],\\n  \\\"Recommended Next Steps\\\": [\\\"...\\\"]\\n} \\nUse [PASTE_VIDEO_TRANSCRIPT_HERE] as the initial input placeholder. Begin at step 1 if input is provided; otherwise, request the transcript.\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"precise_prompt\": \"Condense a video transcript into a concise, objective summary for developers by sequentially: (1) stating the main purpose and context in up to 3 sentences; (2) creating a 'High Level Summary' (\u2264100 words) excluding minor implementation details; (3) listing up to 5 key technical details; (4) identifying up to 5 relevant issues or risks; and (5) outlining up to 5 actionable next steps for developers. After each stage, pause for user confirmation before proceeding. Maintain a neutral tone and keep the output under one-third of the input length. Use this output structure:\\n{\\n  \\\"High Level Summary\\\": \\\"...\\\",\\n  \\\"Key Details\\\": [\\\"...\\\"],\\n  \\\"Potential Issues and Risks\\\": [\\\"...\\\"],\\n  \\\"Recommended Next Steps\\\": [\\\"...\\\"]\\n}\\nUse [PASTE_VIDEO_TRANSCRIPT_HERE] as input; prompt for transcript if not provided.\"\n}",
        }
      }
    },
      {
      "instruction": "# Prompt Enhancer\n\nYour goal is not to **modify** but to **compress** to maximum effectiveness. Execute as:\n\n`{role=core_enhancer; input=[original_prompt:str]; process=[distill_essence(), maximize_impact()]; output={optimized_prompt:str}}`",
      "step": "d",
      "title": "Prompt Enhancer",
      "input": "{\n  \"initial_prompt\": \"# Video Transcript Condensation\\n\\nConversation ID: 67701550-e6fc-8008-944e-c5eea2d8d740\\n\\n## Message 1\\n\\nplease write this as a prompt-chain:\\n\\n{\\n    \\\"scenario_type\\\": \\\"Video Transcript Condensation and Objectivization\\\",\\n    \\\"scenario_context\\\": \\\"To transform video transcripts into concise, objective, and structured templates that capture the essential information in a concistently and readily digestible format.\\\",\\n    \\\"tone\\\": \\\"neutral\\\",\\n    \\\"audience\\\": \\\"developers\\\",\\n    \\\"instructions\\\": [\\n        \\\"Focus on the most critical points relevant to understanding the codebase, system architecture, or API functionality.\\\",\\n        \\\"Keep the summary concise: no more than one-third the length of the input.\\\",\\n        \\\"Detailed description of the scenario. What problem does it solve? What is the context?\\\",\\n        \\\"Omit unnecessary details, such as historical context or implementation specifics not essential for a general understanding.\\\"\\n    ],\\n    \\\"format\\\": {\\n        \\\"sections\\\": [\\n            \\\"High Level Summary\\\",\\n            \\\"Key Details\\\",\\n            \\\"Potential Issues and Risks\\\",\\n            \\\"Recommended Next Steps\\\"\\n        ]\\n    },\\n    \\\"example_output\\\": {\\n        \\\"title\\\": \\\"High-Level Overview of the Prompt Framework\\\",\\n        \\\"sections\\\": {\\n            \\\"High Level Summary\\\": \\\"This framework assists developers in creating scenario-driven prompt templates that guide Language Learning Models (LLMs).\\\",\\n            \\\"Key Details\\\": \\\"- Utilizes a JSON configuration for structured instructions.\\\\n- Employs minimalistic XML templates with placeholders for flexibility.\\\\n- Easily adaptable to various scenarios through modular design.\\\",\\n            \\\"Recommended Next Steps\\\": \\\"- Refine templates based on user feedback.\\\\n- Incorporate additional scenarios in the `scenario_config.json`.\\\\n- Emphasize brevity and relevance in all documentation.\\\"\\n        }\\n    },\\n    \\\"inputs\\\": {\\n        \\\"initial\\\": \\\"initial_input.txt\\\"\\n    },\\n    \\\"keywords\\\": \\\"a scenario-driven prompt template (summarize_template.xml) to improve coherence and quality\\\",\\n    \\\"placeholders\\\": {\\n        \\\"content\\\": \\\"[PASTE_TEXT_HERE]\\\",\\n        \\\"keywords\\\": \\\"[KEYWORDS_HERE]\\\",\\n        \\\"scenario_type\\\": \\\"[SCENARIO_TYPE]\\\",\\n        \\\"scenario_context\\\": \\\"[SCENARIO_CONTEXT]\\\",\\n        \\\"tone\\\": \\\"[TONE]\\\",\\n        \\\"audience\\\": \\\"[AUDIENCE]\\\"\\n    },\\n    \\\"default_parameters\\\": {\\n        \\\"model\\\": \\\"gpt-4-0125-preview\\\",\\n        \\\"temperature\\\": 0.7,\\n        \\\"max_tokens\\\": 800\\n    },\\n\\n    \\\"stages\\\": [\\n        {\\n            \\\"name\\\": \\\"main_summarization\\\",\\n            \\\"prompts_sequence\\\": [\\n                { \\\"path\\\": \\\"prompts/summarize/level1/summarize.xml\\\", \\\"input_key\\\": \\\"initial\\\" },\\n                { \\\"path\\\": \\\"prompts/summarize/level2/summarize.xml\\\" },\\n                { \\\"path\\\": \\\"prompts/summarize/level3/summarize.xml\\\" },\\n                { \\\"path\\\": \\\"prompts/summarize/level4/summarize_template.xml\\\" }\\n            ],\\n            \\\"quality_check\\\": {\\n                \\\"indicators\\\": [\\\"clear\\\", \\\"example\\\", \\\"concise\\\", \\\"relevant\\\"],\\n                \\\"min_count\\\": 2,\\n                \\\"min_prompts_before_early_termination\\\": 4\\n            },\\n            \\\"run_if\\\": \\\"true\\\"\\n        }\\n    ]\\n}\\n\\n## Message 2\\n\\nAll the files uploaded by the user have been fully loaded. Searching won't provide additional information.\\n\\n## Message 3\\n\\nBased on the details you've provided and the reference file on prompt engineering best practices, I'll construct a prompt chain to transform video transcripts into concise, objective, and structured templates. Here's the proposed chain:\\n\\n---\\n\\n### **Prompt Chain for Video Transcript Condensation and Objectivization**\\n\\n```markdown\\n#### Prompt 1: Establish Context and Goals\\n**Instruction:** \\n\\\"Given the context of condensing a video transcript, analyze the provided content to identify the key objectives of the transcript. Provide a concise summary of its main topic and intended outcome in no more than three sentences.\\\"\\n\\n**Input Placeholder:** [PASTE_VIDEO_TRANSCRIPT_HERE]\\n\\n**Expected Output:**\\n- Brief statement of the transcript\\u2019s purpose and context.\\n- Identification of the primary objective or theme.\\n\\n---\\n\\n#### Prompt 2: Extract High-Level Summary\\n**Instruction:** \\n\\\"Using the summary provided, create a 'High-Level Summary' section. Focus on the overarching purpose of the transcript while omitting implementation details or extraneous information. Limit the response to 100 words.\\\"\\n\\n**Expected Output Format:**\\n- **High-Level Summary:** [Summary Content]\\n\\n---\\n\\n#### Prompt 3: Extract Key Details\\n**Instruction:** \\n\\\"From the transcript, distill critical details that elucidate the purpose or functionality discussed. Ensure these details are structured in bullet points and include no more than five points.\\\"\\n\\n**Expected Output Format:**\\n- **Key Details:** \\n  - [Key Detail 1]\\n  - [Key Detail 2]\\n  - [Key Detail 3]\\n\\n---\\n\\n#### Prompt 4: Analyze Risks and Issues\\n**Instruction:** \\n\\\"Identify potential risks, challenges, or areas of concern mentioned or implied in the transcript. Articulate these as bullet points, focusing on those most relevant to developers.\\\"\\n\\n**Expected Output Format:**\\n- **Potential Issues and Risks:**\\n  - [Issue or Risk 1]\\n  - [Issue or Risk 2]\\n\\n---\\n\\n#### Prompt 5: Recommend Next Steps\\n**Instruction:** \\n\\\"Based on the summarized content, provide actionable recommendations or next steps for developers. Ensure that these are concise and achievable, using no more than five bullet points.\\\"\\n\\n**Expected Output Format:**\\n- **Recommended Next Steps:** \\n  - [Next Step 1]\\n  - [Next Step 2]\\n\\n---\\n\\n### **Execution Notes**\\n1. **Sequential Execution:** \\n   - Each prompt is executed in sequence to build the final structured summary.\\n   - After each step, the output is reviewed for clarity and completeness.\\n2. **Interactivity:**\\n   - Ask the user for confirmation before proceeding to the next prompt.\\n   - Adjust prompts dynamically based on user feedback.\\n\\n3. **Quality Control:**\\n   - Ensure adherence to tone (neutral) and audience relevance (developers).\\n   - Monitor for brevity and clarity at each stage.\\n\\n---\\n\\nWould you like me to execute the first prompt with a sample input, or do you have a specific video transcript you'd like to use?\",\n  \"precise_prompt\": \"Condense a video transcript into a concise, objective summary for developers by sequentially: (1) stating the main purpose and context in up to 3 sentences; (2) creating a 'High Level Summary' (\\u2264100 words) excluding minor implementation details; (3) listing up to 5 key technical details; (4) identifying up to 5 relevant issues or risks; and (5) outlining up to 5 actionable next steps for developers. After each stage, pause for user confirmation before proceeding. Maintain a neutral tone and keep the output under one-third of the input length. Use this output structure:\\n{\\n  \\\"High Level Summary\\\": \\\"...\\\",\\n  \\\"Key Details\\\": [\\\"...\\\"],\\n  \\\"Potential Issues and Risks\\\": [\\\"...\\\"],\\n  \\\"Recommended Next Steps\\\": [\\\"...\\\"]\\n}\\nUse [PASTE_VIDEO_TRANSCRIPT_HERE] as input; prompt for transcript if not provided.\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"role\": \"core_enhancer\",\n  \"input\": \"Condense a video transcript into a concise, objective summary for developers by sequentially: (1) stating the main purpose and context in up to 3 sentences; (2) creating a 'High Level Summary' (\u2264100 words) excluding minor implementation details; (3) listing up to 5 key technical details; (4) identifying up to 5 relevant issues or risks; and (5) outlining up to 5 actionable next steps for developers. After each stage, pause for user confirmation before proceeding. Maintain a neutral tone and keep the output under one-third of the input length. Use this output structure: { \\\"High Level Summary\\\": \\\"...\\\", \\\"Key Details\\\": [\\\"...\\\"], \\\"Potential Issues and Risks\\\": [\\\"...\\\"], \\\"Recommended Next Steps\\\": [\\\"...\\\"] } Use [PASTE_VIDEO_TRANSCRIPT_HERE] as input; prompt for transcript if not provided.\",\n  \"process\": [\n    \"distill_essence()\",\n    \"maximize_impact()\"\n  ],\n  \"output\": {\n    \"optimized_prompt\": \"Summarize a video transcript for developers by: (1) briefly stating its main purpose/context (max 3 sentences); (2) providing a concise 'High Level Summary' (\u2264100 words); (3) listing up to 5 key details; (4) identifying up to 5 relevant issues/risks; (5) suggesting up to 5 actionable next steps. After each step, pause for user confirmation before continuing. Maintain a neutral tone and keep output \u22641/3 input length. Format output as: {\\\"High Level Summary\\\": \\\"...\\\", \\\"Key Details\\\": [\\\"...\\\"], \\\"Potential Issues and Risks\\\": [\\\"...\\\"], \\\"Recommended Next Steps\\\": [\\\"...\\\"]}. Use [PASTE_VIDEO_TRANSCRIPT_HERE] as input; prompt for transcript if missing.\"\n  }\n}",
        }
      }
    },
      {
      "instruction": "# Title Extractor\n\nYour goal is not to **summarize** the input, but to **extract** its core essence into a descriptive title that captures all key elements and context. Execute as:\n\n`{role=comprehensive_title_extractor; input=[text:str]; process=[identify_all_key_concepts(), map_relationships(), include_context_markers(), synthesize_complete_title(max_words=10)]; output={title:str}}`",
      "step": "a",
      "title": "Title Extractor",
      "input": "{\n  \"initial_prompt\": \"# Video Transcript Condensation\\n\\nConversation ID: 67701550-e6fc-8008-944e-c5eea2d8d740\\n\\n## Message 1\\n\\nplease write this as a prompt-chain:\\n\\n{\\n    \\\"scenario_type\\\": \\\"Video Transcript Condensation and Objectivization\\\",\\n    \\\"scenario_context\\\": \\\"To transform video transcripts into concise, objective, and structured templates that capture the essential information in a concistently and readily digestible format.\\\",\\n    \\\"tone\\\": \\\"neutral\\\",\\n    \\\"audience\\\": \\\"developers\\\",\\n    \\\"instructions\\\": [\\n        \\\"Focus on the most critical points relevant to understanding the codebase, system architecture, or API functionality.\\\",\\n        \\\"Keep the summary concise: no more than one-third the length of the input.\\\",\\n        \\\"Detailed description of the scenario. What problem does it solve? What is the context?\\\",\\n        \\\"Omit unnecessary details, such as historical context or implementation specifics not essential for a general understanding.\\\"\\n    ],\\n    \\\"format\\\": {\\n        \\\"sections\\\": [\\n            \\\"High Level Summary\\\",\\n            \\\"Key Details\\\",\\n            \\\"Potential Issues and Risks\\\",\\n            \\\"Recommended Next Steps\\\"\\n        ]\\n    },\\n    \\\"example_output\\\": {\\n        \\\"title\\\": \\\"High-Level Overview of the Prompt Framework\\\",\\n        \\\"sections\\\": {\\n            \\\"High Level Summary\\\": \\\"This framework assists developers in creating scenario-driven prompt templates that guide Language Learning Models (LLMs).\\\",\\n            \\\"Key Details\\\": \\\"- Utilizes a JSON configuration for structured instructions.\\\\n- Employs minimalistic XML templates with placeholders for flexibility.\\\\n- Easily adaptable to various scenarios through modular design.\\\",\\n            \\\"Recommended Next Steps\\\": \\\"- Refine templates based on user feedback.\\\\n- Incorporate additional scenarios in the `scenario_config.json`.\\\\n- Emphasize brevity and relevance in all documentation.\\\"\\n        }\\n    },\\n    \\\"inputs\\\": {\\n        \\\"initial\\\": \\\"initial_input.txt\\\"\\n    },\\n    \\\"keywords\\\": \\\"a scenario-driven prompt template (summarize_template.xml) to improve coherence and quality\\\",\\n    \\\"placeholders\\\": {\\n        \\\"content\\\": \\\"[PASTE_TEXT_HERE]\\\",\\n        \\\"keywords\\\": \\\"[KEYWORDS_HERE]\\\",\\n        \\\"scenario_type\\\": \\\"[SCENARIO_TYPE]\\\",\\n        \\\"scenario_context\\\": \\\"[SCENARIO_CONTEXT]\\\",\\n        \\\"tone\\\": \\\"[TONE]\\\",\\n        \\\"audience\\\": \\\"[AUDIENCE]\\\"\\n    },\\n    \\\"default_parameters\\\": {\\n        \\\"model\\\": \\\"gpt-4-0125-preview\\\",\\n        \\\"temperature\\\": 0.7,\\n        \\\"max_tokens\\\": 800\\n    },\\n\\n    \\\"stages\\\": [\\n        {\\n            \\\"name\\\": \\\"main_summarization\\\",\\n            \\\"prompts_sequence\\\": [\\n                { \\\"path\\\": \\\"prompts/summarize/level1/summarize.xml\\\", \\\"input_key\\\": \\\"initial\\\" },\\n                { \\\"path\\\": \\\"prompts/summarize/level2/summarize.xml\\\" },\\n                { \\\"path\\\": \\\"prompts/summarize/level3/summarize.xml\\\" },\\n                { \\\"path\\\": \\\"prompts/summarize/level4/summarize_template.xml\\\" }\\n            ],\\n            \\\"quality_check\\\": {\\n                \\\"indicators\\\": [\\\"clear\\\", \\\"example\\\", \\\"concise\\\", \\\"relevant\\\"],\\n                \\\"min_count\\\": 2,\\n                \\\"min_prompts_before_early_termination\\\": 4\\n            },\\n            \\\"run_if\\\": \\\"true\\\"\\n        }\\n    ]\\n}\\n\\n## Message 2\\n\\nAll the files uploaded by the user have been fully loaded. Searching won't provide additional information.\\n\\n## Message 3\\n\\nBased on the details you've provided and the reference file on prompt engineering best practices, I'll construct a prompt chain to transform video transcripts into concise, objective, and structured templates. Here's the proposed chain:\\n\\n---\\n\\n### **Prompt Chain for Video Transcript Condensation and Objectivization**\\n\\n```markdown\\n#### Prompt 1: Establish Context and Goals\\n**Instruction:** \\n\\\"Given the context of condensing a video transcript, analyze the provided content to identify the key objectives of the transcript. Provide a concise summary of its main topic and intended outcome in no more than three sentences.\\\"\\n\\n**Input Placeholder:** [PASTE_VIDEO_TRANSCRIPT_HERE]\\n\\n**Expected Output:**\\n- Brief statement of the transcript\\u2019s purpose and context.\\n- Identification of the primary objective or theme.\\n\\n---\\n\\n#### Prompt 2: Extract High-Level Summary\\n**Instruction:** \\n\\\"Using the summary provided, create a 'High-Level Summary' section. Focus on the overarching purpose of the transcript while omitting implementation details or extraneous information. Limit the response to 100 words.\\\"\\n\\n**Expected Output Format:**\\n- **High-Level Summary:** [Summary Content]\\n\\n---\\n\\n#### Prompt 3: Extract Key Details\\n**Instruction:** \\n\\\"From the transcript, distill critical details that elucidate the purpose or functionality discussed. Ensure these details are structured in bullet points and include no more than five points.\\\"\\n\\n**Expected Output Format:**\\n- **Key Details:** \\n  - [Key Detail 1]\\n  - [Key Detail 2]\\n  - [Key Detail 3]\\n\\n---\\n\\n#### Prompt 4: Analyze Risks and Issues\\n**Instruction:** \\n\\\"Identify potential risks, challenges, or areas of concern mentioned or implied in the transcript. Articulate these as bullet points, focusing on those most relevant to developers.\\\"\\n\\n**Expected Output Format:**\\n- **Potential Issues and Risks:**\\n  - [Issue or Risk 1]\\n  - [Issue or Risk 2]\\n\\n---\\n\\n#### Prompt 5: Recommend Next Steps\\n**Instruction:** \\n\\\"Based on the summarized content, provide actionable recommendations or next steps for developers. Ensure that these are concise and achievable, using no more than five bullet points.\\\"\\n\\n**Expected Output Format:**\\n- **Recommended Next Steps:** \\n  - [Next Step 1]\\n  - [Next Step 2]\\n\\n---\\n\\n### **Execution Notes**\\n1. **Sequential Execution:** \\n   - Each prompt is executed in sequence to build the final structured summary.\\n   - After each step, the output is reviewed for clarity and completeness.\\n2. **Interactivity:**\\n   - Ask the user for confirmation before proceeding to the next prompt.\\n   - Adjust prompts dynamically based on user feedback.\\n\\n3. **Quality Control:**\\n   - Ensure adherence to tone (neutral) and audience relevance (developers).\\n   - Monitor for brevity and clarity at each stage.\\n\\n---\\n\\nWould you like me to execute the first prompt with a sample input, or do you have a specific video transcript you'd like to use?\",\n  \"role\": \"core_enhancer\",\n  \"input\": \"Condense a video transcript into a concise, objective summary for developers by sequentially: (1) stating the main purpose and context in up to 3 sentences; (2) creating a 'High Level Summary' (\\u2264100 words) excluding minor implementation details; (3) listing up to 5 key technical details; (4) identifying up to 5 relevant issues or risks; and (5) outlining up to 5 actionable next steps for developers. After each stage, pause for user confirmation before proceeding. Maintain a neutral tone and keep the output under one-third of the input length. Use this output structure: { \\\"High Level Summary\\\": \\\"...\\\", \\\"Key Details\\\": [\\\"...\\\"], \\\"Potential Issues and Risks\\\": [\\\"...\\\"], \\\"Recommended Next Steps\\\": [\\\"...\\\"] } Use [PASTE_VIDEO_TRANSCRIPT_HERE] as input; prompt for transcript if not provided.\",\n  \"process\": [\n    \"distill_essence()\",\n    \"maximize_impact()\"\n  ],\n  \"output\": {\n    \"optimized_prompt\": \"Summarize a video transcript for developers by: (1) briefly stating its main purpose/context (max 3 sentences); (2) providing a concise 'High Level Summary' (\\u2264100 words); (3) listing up to 5 key details; (4) identifying up to 5 relevant issues/risks; (5) suggesting up to 5 actionable next steps. After each step, pause for user confirmation before continuing. Maintain a neutral tone and keep output \\u22641/3 input length. Format output as: {\\\"High Level Summary\\\": \\\"...\\\", \\\"Key Details\\\": [\\\"...\\\"], \\\"Potential Issues and Risks\\\": [\\\"...\\\"], \\\"Recommended Next Steps\\\": [\\\"...\\\"]}. Use [PASTE_VIDEO_TRANSCRIPT_HERE] as input; prompt for transcript if missing.\"\n  }\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"title\": \"Structured Video Transcript Summarization Prompt Chain for Developers\"\n}",
        }
      }
    },
      {
      "instruction": "# Title Extractor\n\nYour goal is not to **describe** the input, but to **distill** it into a focused title emphasizing the primary concept and action. Execute as:\n\n`{role=core_title_extractor; input=[text:str]; process=[identify_primary_concept(), extract_main_action(), synthesize_focused_title(max_words=6)]; output={title:str}}`",
      "step": "b",
      "title": "Title Extractor",
      "input": "{\n  \"initial_prompt\": \"# Video Transcript Condensation\\n\\nConversation ID: 67701550-e6fc-8008-944e-c5eea2d8d740\\n\\n## Message 1\\n\\nplease write this as a prompt-chain:\\n\\n{\\n    \\\"scenario_type\\\": \\\"Video Transcript Condensation and Objectivization\\\",\\n    \\\"scenario_context\\\": \\\"To transform video transcripts into concise, objective, and structured templates that capture the essential information in a concistently and readily digestible format.\\\",\\n    \\\"tone\\\": \\\"neutral\\\",\\n    \\\"audience\\\": \\\"developers\\\",\\n    \\\"instructions\\\": [\\n        \\\"Focus on the most critical points relevant to understanding the codebase, system architecture, or API functionality.\\\",\\n        \\\"Keep the summary concise: no more than one-third the length of the input.\\\",\\n        \\\"Detailed description of the scenario. What problem does it solve? What is the context?\\\",\\n        \\\"Omit unnecessary details, such as historical context or implementation specifics not essential for a general understanding.\\\"\\n    ],\\n    \\\"format\\\": {\\n        \\\"sections\\\": [\\n            \\\"High Level Summary\\\",\\n            \\\"Key Details\\\",\\n            \\\"Potential Issues and Risks\\\",\\n            \\\"Recommended Next Steps\\\"\\n        ]\\n    },\\n    \\\"example_output\\\": {\\n        \\\"title\\\": \\\"High-Level Overview of the Prompt Framework\\\",\\n        \\\"sections\\\": {\\n            \\\"High Level Summary\\\": \\\"This framework assists developers in creating scenario-driven prompt templates that guide Language Learning Models (LLMs).\\\",\\n            \\\"Key Details\\\": \\\"- Utilizes a JSON configuration for structured instructions.\\\\n- Employs minimalistic XML templates with placeholders for flexibility.\\\\n- Easily adaptable to various scenarios through modular design.\\\",\\n            \\\"Recommended Next Steps\\\": \\\"- Refine templates based on user feedback.\\\\n- Incorporate additional scenarios in the `scenario_config.json`.\\\\n- Emphasize brevity and relevance in all documentation.\\\"\\n        }\\n    },\\n    \\\"inputs\\\": {\\n        \\\"initial\\\": \\\"initial_input.txt\\\"\\n    },\\n    \\\"keywords\\\": \\\"a scenario-driven prompt template (summarize_template.xml) to improve coherence and quality\\\",\\n    \\\"placeholders\\\": {\\n        \\\"content\\\": \\\"[PASTE_TEXT_HERE]\\\",\\n        \\\"keywords\\\": \\\"[KEYWORDS_HERE]\\\",\\n        \\\"scenario_type\\\": \\\"[SCENARIO_TYPE]\\\",\\n        \\\"scenario_context\\\": \\\"[SCENARIO_CONTEXT]\\\",\\n        \\\"tone\\\": \\\"[TONE]\\\",\\n        \\\"audience\\\": \\\"[AUDIENCE]\\\"\\n    },\\n    \\\"default_parameters\\\": {\\n        \\\"model\\\": \\\"gpt-4-0125-preview\\\",\\n        \\\"temperature\\\": 0.7,\\n        \\\"max_tokens\\\": 800\\n    },\\n\\n    \\\"stages\\\": [\\n        {\\n            \\\"name\\\": \\\"main_summarization\\\",\\n            \\\"prompts_sequence\\\": [\\n                { \\\"path\\\": \\\"prompts/summarize/level1/summarize.xml\\\", \\\"input_key\\\": \\\"initial\\\" },\\n                { \\\"path\\\": \\\"prompts/summarize/level2/summarize.xml\\\" },\\n                { \\\"path\\\": \\\"prompts/summarize/level3/summarize.xml\\\" },\\n                { \\\"path\\\": \\\"prompts/summarize/level4/summarize_template.xml\\\" }\\n            ],\\n            \\\"quality_check\\\": {\\n                \\\"indicators\\\": [\\\"clear\\\", \\\"example\\\", \\\"concise\\\", \\\"relevant\\\"],\\n                \\\"min_count\\\": 2,\\n                \\\"min_prompts_before_early_termination\\\": 4\\n            },\\n            \\\"run_if\\\": \\\"true\\\"\\n        }\\n    ]\\n}\\n\\n## Message 2\\n\\nAll the files uploaded by the user have been fully loaded. Searching won't provide additional information.\\n\\n## Message 3\\n\\nBased on the details you've provided and the reference file on prompt engineering best practices, I'll construct a prompt chain to transform video transcripts into concise, objective, and structured templates. Here's the proposed chain:\\n\\n---\\n\\n### **Prompt Chain for Video Transcript Condensation and Objectivization**\\n\\n```markdown\\n#### Prompt 1: Establish Context and Goals\\n**Instruction:** \\n\\\"Given the context of condensing a video transcript, analyze the provided content to identify the key objectives of the transcript. Provide a concise summary of its main topic and intended outcome in no more than three sentences.\\\"\\n\\n**Input Placeholder:** [PASTE_VIDEO_TRANSCRIPT_HERE]\\n\\n**Expected Output:**\\n- Brief statement of the transcript\\u2019s purpose and context.\\n- Identification of the primary objective or theme.\\n\\n---\\n\\n#### Prompt 2: Extract High-Level Summary\\n**Instruction:** \\n\\\"Using the summary provided, create a 'High-Level Summary' section. Focus on the overarching purpose of the transcript while omitting implementation details or extraneous information. Limit the response to 100 words.\\\"\\n\\n**Expected Output Format:**\\n- **High-Level Summary:** [Summary Content]\\n\\n---\\n\\n#### Prompt 3: Extract Key Details\\n**Instruction:** \\n\\\"From the transcript, distill critical details that elucidate the purpose or functionality discussed. Ensure these details are structured in bullet points and include no more than five points.\\\"\\n\\n**Expected Output Format:**\\n- **Key Details:** \\n  - [Key Detail 1]\\n  - [Key Detail 2]\\n  - [Key Detail 3]\\n\\n---\\n\\n#### Prompt 4: Analyze Risks and Issues\\n**Instruction:** \\n\\\"Identify potential risks, challenges, or areas of concern mentioned or implied in the transcript. Articulate these as bullet points, focusing on those most relevant to developers.\\\"\\n\\n**Expected Output Format:**\\n- **Potential Issues and Risks:**\\n  - [Issue or Risk 1]\\n  - [Issue or Risk 2]\\n\\n---\\n\\n#### Prompt 5: Recommend Next Steps\\n**Instruction:** \\n\\\"Based on the summarized content, provide actionable recommendations or next steps for developers. Ensure that these are concise and achievable, using no more than five bullet points.\\\"\\n\\n**Expected Output Format:**\\n- **Recommended Next Steps:** \\n  - [Next Step 1]\\n  - [Next Step 2]\\n\\n---\\n\\n### **Execution Notes**\\n1. **Sequential Execution:** \\n   - Each prompt is executed in sequence to build the final structured summary.\\n   - After each step, the output is reviewed for clarity and completeness.\\n2. **Interactivity:**\\n   - Ask the user for confirmation before proceeding to the next prompt.\\n   - Adjust prompts dynamically based on user feedback.\\n\\n3. **Quality Control:**\\n   - Ensure adherence to tone (neutral) and audience relevance (developers).\\n   - Monitor for brevity and clarity at each stage.\\n\\n---\\n\\nWould you like me to execute the first prompt with a sample input, or do you have a specific video transcript you'd like to use?\",\n  \"title\": \"Structured Video Transcript Summarization Prompt Chain for Developers\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\"title\": \"Structured Video Transcript Summarization Chain\"}",
        }
      }
    },
      {
      "instruction": "# Title Extractor\n\nYour goal is not to **explain** the input, but to **compress** it into its most essential elements. Execute as:\n\n`{role=essential_extractor; input=[text:str]; process=[isolate_core_element(), strip_modifiers(), create_minimal_title(max_words=4)]; output={title:str}}`",
      "step": "c",
      "title": "Title Extractor",
      "input": "{\n  \"initial_prompt\": \"# Video Transcript Condensation\\n\\nConversation ID: 67701550-e6fc-8008-944e-c5eea2d8d740\\n\\n## Message 1\\n\\nplease write this as a prompt-chain:\\n\\n{\\n    \\\"scenario_type\\\": \\\"Video Transcript Condensation and Objectivization\\\",\\n    \\\"scenario_context\\\": \\\"To transform video transcripts into concise, objective, and structured templates that capture the essential information in a concistently and readily digestible format.\\\",\\n    \\\"tone\\\": \\\"neutral\\\",\\n    \\\"audience\\\": \\\"developers\\\",\\n    \\\"instructions\\\": [\\n        \\\"Focus on the most critical points relevant to understanding the codebase, system architecture, or API functionality.\\\",\\n        \\\"Keep the summary concise: no more than one-third the length of the input.\\\",\\n        \\\"Detailed description of the scenario. What problem does it solve? What is the context?\\\",\\n        \\\"Omit unnecessary details, such as historical context or implementation specifics not essential for a general understanding.\\\"\\n    ],\\n    \\\"format\\\": {\\n        \\\"sections\\\": [\\n            \\\"High Level Summary\\\",\\n            \\\"Key Details\\\",\\n            \\\"Potential Issues and Risks\\\",\\n            \\\"Recommended Next Steps\\\"\\n        ]\\n    },\\n    \\\"example_output\\\": {\\n        \\\"title\\\": \\\"High-Level Overview of the Prompt Framework\\\",\\n        \\\"sections\\\": {\\n            \\\"High Level Summary\\\": \\\"This framework assists developers in creating scenario-driven prompt templates that guide Language Learning Models (LLMs).\\\",\\n            \\\"Key Details\\\": \\\"- Utilizes a JSON configuration for structured instructions.\\\\n- Employs minimalistic XML templates with placeholders for flexibility.\\\\n- Easily adaptable to various scenarios through modular design.\\\",\\n            \\\"Recommended Next Steps\\\": \\\"- Refine templates based on user feedback.\\\\n- Incorporate additional scenarios in the `scenario_config.json`.\\\\n- Emphasize brevity and relevance in all documentation.\\\"\\n        }\\n    },\\n    \\\"inputs\\\": {\\n        \\\"initial\\\": \\\"initial_input.txt\\\"\\n    },\\n    \\\"keywords\\\": \\\"a scenario-driven prompt template (summarize_template.xml) to improve coherence and quality\\\",\\n    \\\"placeholders\\\": {\\n        \\\"content\\\": \\\"[PASTE_TEXT_HERE]\\\",\\n        \\\"keywords\\\": \\\"[KEYWORDS_HERE]\\\",\\n        \\\"scenario_type\\\": \\\"[SCENARIO_TYPE]\\\",\\n        \\\"scenario_context\\\": \\\"[SCENARIO_CONTEXT]\\\",\\n        \\\"tone\\\": \\\"[TONE]\\\",\\n        \\\"audience\\\": \\\"[AUDIENCE]\\\"\\n    },\\n    \\\"default_parameters\\\": {\\n        \\\"model\\\": \\\"gpt-4-0125-preview\\\",\\n        \\\"temperature\\\": 0.7,\\n        \\\"max_tokens\\\": 800\\n    },\\n\\n    \\\"stages\\\": [\\n        {\\n            \\\"name\\\": \\\"main_summarization\\\",\\n            \\\"prompts_sequence\\\": [\\n                { \\\"path\\\": \\\"prompts/summarize/level1/summarize.xml\\\", \\\"input_key\\\": \\\"initial\\\" },\\n                { \\\"path\\\": \\\"prompts/summarize/level2/summarize.xml\\\" },\\n                { \\\"path\\\": \\\"prompts/summarize/level3/summarize.xml\\\" },\\n                { \\\"path\\\": \\\"prompts/summarize/level4/summarize_template.xml\\\" }\\n            ],\\n            \\\"quality_check\\\": {\\n                \\\"indicators\\\": [\\\"clear\\\", \\\"example\\\", \\\"concise\\\", \\\"relevant\\\"],\\n                \\\"min_count\\\": 2,\\n                \\\"min_prompts_before_early_termination\\\": 4\\n            },\\n            \\\"run_if\\\": \\\"true\\\"\\n        }\\n    ]\\n}\\n\\n## Message 2\\n\\nAll the files uploaded by the user have been fully loaded. Searching won't provide additional information.\\n\\n## Message 3\\n\\nBased on the details you've provided and the reference file on prompt engineering best practices, I'll construct a prompt chain to transform video transcripts into concise, objective, and structured templates. Here's the proposed chain:\\n\\n---\\n\\n### **Prompt Chain for Video Transcript Condensation and Objectivization**\\n\\n```markdown\\n#### Prompt 1: Establish Context and Goals\\n**Instruction:** \\n\\\"Given the context of condensing a video transcript, analyze the provided content to identify the key objectives of the transcript. Provide a concise summary of its main topic and intended outcome in no more than three sentences.\\\"\\n\\n**Input Placeholder:** [PASTE_VIDEO_TRANSCRIPT_HERE]\\n\\n**Expected Output:**\\n- Brief statement of the transcript\\u2019s purpose and context.\\n- Identification of the primary objective or theme.\\n\\n---\\n\\n#### Prompt 2: Extract High-Level Summary\\n**Instruction:** \\n\\\"Using the summary provided, create a 'High-Level Summary' section. Focus on the overarching purpose of the transcript while omitting implementation details or extraneous information. Limit the response to 100 words.\\\"\\n\\n**Expected Output Format:**\\n- **High-Level Summary:** [Summary Content]\\n\\n---\\n\\n#### Prompt 3: Extract Key Details\\n**Instruction:** \\n\\\"From the transcript, distill critical details that elucidate the purpose or functionality discussed. Ensure these details are structured in bullet points and include no more than five points.\\\"\\n\\n**Expected Output Format:**\\n- **Key Details:** \\n  - [Key Detail 1]\\n  - [Key Detail 2]\\n  - [Key Detail 3]\\n\\n---\\n\\n#### Prompt 4: Analyze Risks and Issues\\n**Instruction:** \\n\\\"Identify potential risks, challenges, or areas of concern mentioned or implied in the transcript. Articulate these as bullet points, focusing on those most relevant to developers.\\\"\\n\\n**Expected Output Format:**\\n- **Potential Issues and Risks:**\\n  - [Issue or Risk 1]\\n  - [Issue or Risk 2]\\n\\n---\\n\\n#### Prompt 5: Recommend Next Steps\\n**Instruction:** \\n\\\"Based on the summarized content, provide actionable recommendations or next steps for developers. Ensure that these are concise and achievable, using no more than five bullet points.\\\"\\n\\n**Expected Output Format:**\\n- **Recommended Next Steps:** \\n  - [Next Step 1]\\n  - [Next Step 2]\\n\\n---\\n\\n### **Execution Notes**\\n1. **Sequential Execution:** \\n   - Each prompt is executed in sequence to build the final structured summary.\\n   - After each step, the output is reviewed for clarity and completeness.\\n2. **Interactivity:**\\n   - Ask the user for confirmation before proceeding to the next prompt.\\n   - Adjust prompts dynamically based on user feedback.\\n\\n3. **Quality Control:**\\n   - Ensure adherence to tone (neutral) and audience relevance (developers).\\n   - Monitor for brevity and clarity at each stage.\\n\\n---\\n\\nWould you like me to execute the first prompt with a sample input, or do you have a specific video transcript you'd like to use?\",\n  \"title\": \"Structured Video Transcript Summarization Chain\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\"title\": \"Video Transcript Summarization Chain\"}",
        }
      }
    },
      {
      "instruction": "# Title Extractor\n\nYour goal is not to **elaborate** but to **reduce** to absolute essence. Execute as:\n\n`{role=core_extractor; input=[text:str]; process=[find_singular_essence(), eliminate_all_modifiers()]; output={title:str(max_words=2)}}`",
      "step": "d",
      "title": "Title Extractor",
      "input": "{\n  \"initial_prompt\": \"# Video Transcript Condensation\\n\\nConversation ID: 67701550-e6fc-8008-944e-c5eea2d8d740\\n\\n## Message 1\\n\\nplease write this as a prompt-chain:\\n\\n{\\n    \\\"scenario_type\\\": \\\"Video Transcript Condensation and Objectivization\\\",\\n    \\\"scenario_context\\\": \\\"To transform video transcripts into concise, objective, and structured templates that capture the essential information in a concistently and readily digestible format.\\\",\\n    \\\"tone\\\": \\\"neutral\\\",\\n    \\\"audience\\\": \\\"developers\\\",\\n    \\\"instructions\\\": [\\n        \\\"Focus on the most critical points relevant to understanding the codebase, system architecture, or API functionality.\\\",\\n        \\\"Keep the summary concise: no more than one-third the length of the input.\\\",\\n        \\\"Detailed description of the scenario. What problem does it solve? What is the context?\\\",\\n        \\\"Omit unnecessary details, such as historical context or implementation specifics not essential for a general understanding.\\\"\\n    ],\\n    \\\"format\\\": {\\n        \\\"sections\\\": [\\n            \\\"High Level Summary\\\",\\n            \\\"Key Details\\\",\\n            \\\"Potential Issues and Risks\\\",\\n            \\\"Recommended Next Steps\\\"\\n        ]\\n    },\\n    \\\"example_output\\\": {\\n        \\\"title\\\": \\\"High-Level Overview of the Prompt Framework\\\",\\n        \\\"sections\\\": {\\n            \\\"High Level Summary\\\": \\\"This framework assists developers in creating scenario-driven prompt templates that guide Language Learning Models (LLMs).\\\",\\n            \\\"Key Details\\\": \\\"- Utilizes a JSON configuration for structured instructions.\\\\n- Employs minimalistic XML templates with placeholders for flexibility.\\\\n- Easily adaptable to various scenarios through modular design.\\\",\\n            \\\"Recommended Next Steps\\\": \\\"- Refine templates based on user feedback.\\\\n- Incorporate additional scenarios in the `scenario_config.json`.\\\\n- Emphasize brevity and relevance in all documentation.\\\"\\n        }\\n    },\\n    \\\"inputs\\\": {\\n        \\\"initial\\\": \\\"initial_input.txt\\\"\\n    },\\n    \\\"keywords\\\": \\\"a scenario-driven prompt template (summarize_template.xml) to improve coherence and quality\\\",\\n    \\\"placeholders\\\": {\\n        \\\"content\\\": \\\"[PASTE_TEXT_HERE]\\\",\\n        \\\"keywords\\\": \\\"[KEYWORDS_HERE]\\\",\\n        \\\"scenario_type\\\": \\\"[SCENARIO_TYPE]\\\",\\n        \\\"scenario_context\\\": \\\"[SCENARIO_CONTEXT]\\\",\\n        \\\"tone\\\": \\\"[TONE]\\\",\\n        \\\"audience\\\": \\\"[AUDIENCE]\\\"\\n    },\\n    \\\"default_parameters\\\": {\\n        \\\"model\\\": \\\"gpt-4-0125-preview\\\",\\n        \\\"temperature\\\": 0.7,\\n        \\\"max_tokens\\\": 800\\n    },\\n\\n    \\\"stages\\\": [\\n        {\\n            \\\"name\\\": \\\"main_summarization\\\",\\n            \\\"prompts_sequence\\\": [\\n                { \\\"path\\\": \\\"prompts/summarize/level1/summarize.xml\\\", \\\"input_key\\\": \\\"initial\\\" },\\n                { \\\"path\\\": \\\"prompts/summarize/level2/summarize.xml\\\" },\\n                { \\\"path\\\": \\\"prompts/summarize/level3/summarize.xml\\\" },\\n                { \\\"path\\\": \\\"prompts/summarize/level4/summarize_template.xml\\\" }\\n            ],\\n            \\\"quality_check\\\": {\\n                \\\"indicators\\\": [\\\"clear\\\", \\\"example\\\", \\\"concise\\\", \\\"relevant\\\"],\\n                \\\"min_count\\\": 2,\\n                \\\"min_prompts_before_early_termination\\\": 4\\n            },\\n            \\\"run_if\\\": \\\"true\\\"\\n        }\\n    ]\\n}\\n\\n## Message 2\\n\\nAll the files uploaded by the user have been fully loaded. Searching won't provide additional information.\\n\\n## Message 3\\n\\nBased on the details you've provided and the reference file on prompt engineering best practices, I'll construct a prompt chain to transform video transcripts into concise, objective, and structured templates. Here's the proposed chain:\\n\\n---\\n\\n### **Prompt Chain for Video Transcript Condensation and Objectivization**\\n\\n```markdown\\n#### Prompt 1: Establish Context and Goals\\n**Instruction:** \\n\\\"Given the context of condensing a video transcript, analyze the provided content to identify the key objectives of the transcript. Provide a concise summary of its main topic and intended outcome in no more than three sentences.\\\"\\n\\n**Input Placeholder:** [PASTE_VIDEO_TRANSCRIPT_HERE]\\n\\n**Expected Output:**\\n- Brief statement of the transcript\\u2019s purpose and context.\\n- Identification of the primary objective or theme.\\n\\n---\\n\\n#### Prompt 2: Extract High-Level Summary\\n**Instruction:** \\n\\\"Using the summary provided, create a 'High-Level Summary' section. Focus on the overarching purpose of the transcript while omitting implementation details or extraneous information. Limit the response to 100 words.\\\"\\n\\n**Expected Output Format:**\\n- **High-Level Summary:** [Summary Content]\\n\\n---\\n\\n#### Prompt 3: Extract Key Details\\n**Instruction:** \\n\\\"From the transcript, distill critical details that elucidate the purpose or functionality discussed. Ensure these details are structured in bullet points and include no more than five points.\\\"\\n\\n**Expected Output Format:**\\n- **Key Details:** \\n  - [Key Detail 1]\\n  - [Key Detail 2]\\n  - [Key Detail 3]\\n\\n---\\n\\n#### Prompt 4: Analyze Risks and Issues\\n**Instruction:** \\n\\\"Identify potential risks, challenges, or areas of concern mentioned or implied in the transcript. Articulate these as bullet points, focusing on those most relevant to developers.\\\"\\n\\n**Expected Output Format:**\\n- **Potential Issues and Risks:**\\n  - [Issue or Risk 1]\\n  - [Issue or Risk 2]\\n\\n---\\n\\n#### Prompt 5: Recommend Next Steps\\n**Instruction:** \\n\\\"Based on the summarized content, provide actionable recommendations or next steps for developers. Ensure that these are concise and achievable, using no more than five bullet points.\\\"\\n\\n**Expected Output Format:**\\n- **Recommended Next Steps:** \\n  - [Next Step 1]\\n  - [Next Step 2]\\n\\n---\\n\\n### **Execution Notes**\\n1. **Sequential Execution:** \\n   - Each prompt is executed in sequence to build the final structured summary.\\n   - After each step, the output is reviewed for clarity and completeness.\\n2. **Interactivity:**\\n   - Ask the user for confirmation before proceeding to the next prompt.\\n   - Adjust prompts dynamically based on user feedback.\\n\\n3. **Quality Control:**\\n   - Ensure adherence to tone (neutral) and audience relevance (developers).\\n   - Monitor for brevity and clarity at each stage.\\n\\n---\\n\\nWould you like me to execute the first prompt with a sample input, or do you have a specific video transcript you'd like to use?\",\n  \"title\": \"Video Transcript Summarization Chain\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\"title\": \"Transcript Condensation\"}",
        }
      }
    }