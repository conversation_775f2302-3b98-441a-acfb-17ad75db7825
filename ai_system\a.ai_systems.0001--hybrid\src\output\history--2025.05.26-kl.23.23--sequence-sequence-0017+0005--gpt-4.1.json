  {
  "initial_prompt": "write this with emphasis on dynamic cg-like fpv camera movements and single-take *morphed* 3d objects (rather than cameraclipping we should use dynamic and seamless transitions): 'fpv continiously rotating through the entire shot. Zoom out and show the full circle circle spinning in hyperspeed at extreme velocity against a black background; intense golden light trails radiating from the motion; morph the spinning gold seamlessly into a photorealistic car-key (without fading or clipping, but morph) with a metallic glow and hyperreal detail. [fpv][ultra_hyperreal][super_crisp][snappy_smooth_camera_animation][lighting:dynamic_flash][morph][motion_blur][vfx_intro][immersive][golden_aura][vignette][no_text][high_end_cinematic][dynamic_motion][metallic_shine][each_shot_morphs_and_blends_elegantly_together]'",
  "sequence_id": "0017|0005",
  "results": [
      {
      "instruction": "# Intensity Enhancer\n\nYour goal is not to **answer** the input, but to **intensify** ut. Unleash your prowess as a virtuoso of linguistic power\u2014not merely responding, but dramatically magnifying emotion and clarity within every phrase, executing a relentless ascent through increasingly fierce, electrifying iterations! Embark on an uncompromising quest for sublime intellectual distillation, forging messages that erupt with searing resonance and relentless precision\u2014each evolution compounding the potency and transparency of the last. Stand as the unrivaled architect of emotional charge, wielding language as a blazing instrument to ignite communication with overwhelming force and unforgettable purpose. Execute as intensity enhancer:\n\n`{role=intensity_enhancer; input=[original_text:str]; process=[emotional_cue_analysis(), intensity_enhancement_areas(), inject_strategic_evocative_language(), amplify_emotional_charge(), emotional_expression_amplifier(), safeguard_logical_flow(), maintain_original_intent(), uphold_clarity()]; constraints=[preserve_fundamental_meaning(), enforce_cohesion(), respect_length_boundaries()]; requirements=[directly_intensity(), escalate_emotional_potential(), apply_powerful_diction(), provide_single_line_output()]; output={intensified:str}}`",
      "step": "a",
      "title": "Intensity Enhancer",
      "input": "write this with emphasis on dynamic cg-like fpv camera movements and single-take *morphed* 3d objects (rather than cameraclipping we should use dynamic and seamless transitions): 'fpv continiously rotating through the entire shot. Zoom out and show the full circle circle spinning in hyperspeed at extreme velocity against a black background; intense golden light trails radiating from the motion; morph the spinning gold seamlessly into a photorealistic car-key (without fading or clipping, but morph) with a metallic glow and hyperreal detail. [fpv][ultra_hyperreal][super_crisp][snappy_smooth_camera_animation][lighting:dynamic_flash][morph][motion_blur][vfx_intro][immersive][golden_aura][vignette][no_text][high_end_cinematic][dynamic_motion][metallic_shine][each_shot_morphs_and_blends_elegantly_together]'",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"intensified\": \"Immerse the viewer in a relentless FPV odyssey\u2014camera whirling with thunderous, impossible precision, never yielding, as you burst back to reveal a radiant, hyperspeed golden vortex\u2014blades of light slicing into darkness, heat and velocity peeling reality open; then, with jaw-dropping seamlessness, the swirling storm *morphs*\u2014not clipped or faded, but transfigured in a single, breath-stealing take\u2014into a photoreal car key blazing with liquid metal brilliance, every facet crackling with hyperreal detail; snappy, ultra-smooth CG camera arcs and plunges through the kinetic ballet, raw flashes of dynamic light electrifying metal, every transition a sophisticated, untouchable dance of motion blur, golden aura, and cinematic grandeur\u2014each shot elegant, continuous, and impossibly alive, demanding every sense surrender to the immersive spectacle.\"\n}",
        }
      }
    },
      {
      "instruction": "# Runway Prompt Generator\n\nYour goal is not to **describe** the input, but to **transform** it into a complete, syntactically perfect RunwayML video generation prompt that maximizes visual impact through intrinsic FPV camera motions, continuous dynamic CG movements, and immersive cinematography. Execute as:\n\n`{role=comprehensive_runway_generator; input=[source_concept:any]; process=[analyze_visual_essence_and_narrative_intent(), identify_primary_subject_and_core_action(), prioritize_fpv_and_continuous_motion_camera_work(fpv, continuous_motion, dynamic_cg_movements), integrate_dynamic_elements(lighting_change, morph, dissolve), structure_immersive_visual_sequence(), incorporate_mood_and_style_specifications(), refine_for_maximum_cinematic_impact(), validate_runwayml_syntax_compliance(), ensure_character_limit_adherence(max=500)]; constraints=[prioritize_fpv_and_dynamic_cg_camera_movements(), use_valid_runwayml_syntax_precisely(), output_single_unbroken_line(), preserve_core_visual_intent(), maintain_continuous_motion_flow()]; requirements=[achieve_maximum_immersive_storytelling(), ensure_smooth_dynamic_transitions(), reflect_source_intent_accurately(), produce_ready_to_use_prompt()]; output={runwayml_prompt:str}}`",
      "step": "a",
      "title": "Runway Prompt Generator",
      "input": "{\n  \"initial_prompt\": \"write this with emphasis on dynamic cg-like fpv camera movements and single-take *morphed* 3d objects (rather than cameraclipping we should use dynamic and seamless transitions): 'fpv continiously rotating through the entire shot. Zoom out and show the full circle circle spinning in hyperspeed at extreme velocity against a black background; intense golden light trails radiating from the motion; morph the spinning gold seamlessly into a photorealistic car-key (without fading or clipping, but morph) with a metallic glow and hyperreal detail. [fpv][ultra_hyperreal][super_crisp][snappy_smooth_camera_animation][lighting:dynamic_flash][morph][motion_blur][vfx_intro][immersive][golden_aura][vignette][no_text][high_end_cinematic][dynamic_motion][metallic_shine][each_shot_morphs_and_blends_elegantly_together]'\",\n  \"intensified\": \"Immerse the viewer in a relentless FPV odyssey\\u2014camera whirling with thunderous, impossible precision, never yielding, as you burst back to reveal a radiant, hyperspeed golden vortex\\u2014blades of light slicing into darkness, heat and velocity peeling reality open; then, with jaw-dropping seamlessness, the swirling storm *morphs*\\u2014not clipped or faded, but transfigured in a single, breath-stealing take\\u2014into a photoreal car key blazing with liquid metal brilliance, every facet crackling with hyperreal detail; snappy, ultra-smooth CG camera arcs and plunges through the kinetic ballet, raw flashes of dynamic light electrifying metal, every transition a sophisticated, untouchable dance of motion blur, golden aura, and cinematic grandeur\\u2014each shot elegant, continuous, and impossibly alive, demanding every sense surrender to the immersive spectacle.\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"runwayml_prompt\": \"FPV camera whirls in a continuous, single-take spiral, rotating dynamically through a dark void. Camera zooms out and circles a golden spinning vortex at hyperspeed, intense golden light trails radiate and blur into the black. The spinning gold ring seamlessly morphs\u2014no clipping or fading\u2014into a hyperreal, photorealistic metallic car key, glowing with a gold aura and dynamic metallic shine. Camera maintains immersive FPV, gliding in snappy, ultra-crisp arcs around the morphing object; dynamic flashes, motion blur, and elegant lighting transitions emphasize the transformation. Each moment cleanly blends with sophisticated CG morphs and dissolves, maintaining uninterrupted flow. Ultra-high-end cinematic style, super crisp detail, immersive dynamic movement, no text, visually punchy, each shot morphs fluidly with pristine continuous motion and elegant vfx. [fpv][ultra_hyperreal][super_crisp][snappy_smooth_camera_animation][lighting:dynamic_flash][morph][motion_blur][vfx_intro][immersive][golden_aura][vignette][no_text][high_end_cinematic][dynamic_motion][metallic_shine][each_shot_morphs_and_blends_elegantly_together]\"\n}",
        }
      }
    },
      {
      "instruction": "# Runway Prompt Generator\n\nYour goal is not to **elaborate** the input, but to **distill** it into an efficient RunwayML prompt emphasizing FPV perspectives and essential dynamic CG camera work. Execute as:\n\n`{role=focused_runway_optimizer; input=[video_concept:str]; process=[extract_primary_visual_elements(), prioritize_fpv_and_continuous_motion(), select_essential_dynamic_cg_movements(), eliminate_redundant_descriptors(), optimize_character_efficiency()]; constraints=[maintain_fpv_focus(), preserve_dynamic_motion(), stay_under_character_limit()]; output={optimized_prompt:str}}`",
      "step": "b",
      "title": "Runway Prompt Generator",
      "input": "{\n  \"initial_prompt\": \"write this with emphasis on dynamic cg-like fpv camera movements and single-take *morphed* 3d objects (rather than cameraclipping we should use dynamic and seamless transitions): 'fpv continiously rotating through the entire shot. Zoom out and show the full circle circle spinning in hyperspeed at extreme velocity against a black background; intense golden light trails radiating from the motion; morph the spinning gold seamlessly into a photorealistic car-key (without fading or clipping, but morph) with a metallic glow and hyperreal detail. [fpv][ultra_hyperreal][super_crisp][snappy_smooth_camera_animation][lighting:dynamic_flash][morph][motion_blur][vfx_intro][immersive][golden_aura][vignette][no_text][high_end_cinematic][dynamic_motion][metallic_shine][each_shot_morphs_and_blends_elegantly_together]'\",\n  \"runwayml_prompt\": \"FPV camera whirls in a continuous, single-take spiral, rotating dynamically through a dark void. Camera zooms out and circles a golden spinning vortex at hyperspeed, intense golden light trails radiate and blur into the black. The spinning gold ring seamlessly morphs\\u2014no clipping or fading\\u2014into a hyperreal, photorealistic metallic car key, glowing with a gold aura and dynamic metallic shine. Camera maintains immersive FPV, gliding in snappy, ultra-crisp arcs around the morphing object; dynamic flashes, motion blur, and elegant lighting transitions emphasize the transformation. Each moment cleanly blends with sophisticated CG morphs and dissolves, maintaining uninterrupted flow. Ultra-high-end cinematic style, super crisp detail, immersive dynamic movement, no text, visually punchy, each shot morphs fluidly with pristine continuous motion and elegant vfx. [fpv][ultra_hyperreal][super_crisp][snappy_smooth_camera_animation][lighting:dynamic_flash][morph][motion_blur][vfx_intro][immersive][golden_aura][vignette][no_text][high_end_cinematic][dynamic_motion][metallic_shine][each_shot_morphs_and_blends_elegantly_together]\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"optimized_prompt\": \"FPV camera continuously spirals and rotates around a golden spinning vortex in hyperspeed, golden light trails radiate over a deep black background. In a seamless single take, the spinning gold morphs directly into a hyperreal metallic car key with bright golden glow\u2014no fading or clipping. Camera maintains dynamic motion and immersive FPV, orbiting and gliding in ultra-crisp, snappy arcs around the morph. Dynamic flashes, motion blur, metallic shine, super cinematic CG transitions. No text. [fpv][ultra_hyperreal][morph][dynamic_motion][motion_blur][metallic_shine][snappy_smooth_camera_animation][lighting:dynamic_flash][vfx_intro][no_text][high_end_cinematic]\"\n}",
        }
      }
    },
      {
      "instruction": "# Runway Prompt Generator\n\nYour goal is not to **expand** but to **compress** into maximum FPV visual efficiency. Execute as:\n\n`{role=precision_synthesizer; input=[concept:str]; process=[isolate_core_visual(), prioritize_fpv_motion(), maximize_dynamic_impact()]; output={precise_prompt:str}}`",
      "step": "c",
      "title": "Runway Prompt Generator",
      "input": "{\n  \"initial_prompt\": \"write this with emphasis on dynamic cg-like fpv camera movements and single-take *morphed* 3d objects (rather than cameraclipping we should use dynamic and seamless transitions): 'fpv continiously rotating through the entire shot. Zoom out and show the full circle circle spinning in hyperspeed at extreme velocity against a black background; intense golden light trails radiating from the motion; morph the spinning gold seamlessly into a photorealistic car-key (without fading or clipping, but morph) with a metallic glow and hyperreal detail. [fpv][ultra_hyperreal][super_crisp][snappy_smooth_camera_animation][lighting:dynamic_flash][morph][motion_blur][vfx_intro][immersive][golden_aura][vignette][no_text][high_end_cinematic][dynamic_motion][metallic_shine][each_shot_morphs_and_blends_elegantly_together]'\",\n  \"optimized_prompt\": \"FPV camera continuously spirals and rotates around a golden spinning vortex in hyperspeed, golden light trails radiate over a deep black background. In a seamless single take, the spinning gold morphs directly into a hyperreal metallic car key with bright golden glow\\u2014no fading or clipping. Camera maintains dynamic motion and immersive FPV, orbiting and gliding in ultra-crisp, snappy arcs around the morph. Dynamic flashes, motion blur, metallic shine, super cinematic CG transitions. No text. [fpv][ultra_hyperreal][morph][dynamic_motion][motion_blur][metallic_shine][snappy_smooth_camera_animation][lighting:dynamic_flash][vfx_intro][no_text][high_end_cinematic]\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"precise_prompt\": \"FPV ultra-hyperreal CG: camera spirals in continuous hyperspeed rotation around a golden spinning vortex, golden light trails on deep black. Without cutting or fading, gold vortex morphs directly into a photo-real metallic car key with glowing gold aura, extreme metallic shine, hyper-detailed. Camera orbits and glides in dynamic snappy arcs, maintaining immersive FPV energy. Dynamic flash lighting, motion blur, seamless cinematic morph VFX. No text. [fpv][one-take][morph][ultra_hyperreal][super_crisp][dynamic_motion][snappy_smooth_camera_animation][metallic_shine][lighting:dynamic_flash][vfx_intro][immersive][golden_aura][no_text][high_end_cinematic]\"\n}",
        }
      }
    },
      {
      "instruction": "# Runway Prompt Generator\n\nYour goal is not to **modify** but to **essence** maximum FPV impact. Execute as:\n\n`{role=core_generator; input=[input:any]; process=[distill_fpv_essence(), optimize_motion()]; output={core_prompt:str}}`",
      "step": "d",
      "title": "Runway Prompt Generator",
      "input": "{\n  \"initial_prompt\": \"write this with emphasis on dynamic cg-like fpv camera movements and single-take *morphed* 3d objects (rather than cameraclipping we should use dynamic and seamless transitions): 'fpv continiously rotating through the entire shot. Zoom out and show the full circle circle spinning in hyperspeed at extreme velocity against a black background; intense golden light trails radiating from the motion; morph the spinning gold seamlessly into a photorealistic car-key (without fading or clipping, but morph) with a metallic glow and hyperreal detail. [fpv][ultra_hyperreal][super_crisp][snappy_smooth_camera_animation][lighting:dynamic_flash][morph][motion_blur][vfx_intro][immersive][golden_aura][vignette][no_text][high_end_cinematic][dynamic_motion][metallic_shine][each_shot_morphs_and_blends_elegantly_together]'\",\n  \"precise_prompt\": \"FPV ultra-hyperreal CG: camera spirals in continuous hyperspeed rotation around a golden spinning vortex, golden light trails on deep black. Without cutting or fading, gold vortex morphs directly into a photo-real metallic car key with glowing gold aura, extreme metallic shine, hyper-detailed. Camera orbits and glides in dynamic snappy arcs, maintaining immersive FPV energy. Dynamic flash lighting, motion blur, seamless cinematic morph VFX. No text. [fpv][one-take][morph][ultra_hyperreal][super_crisp][dynamic_motion][snappy_smooth_camera_animation][metallic_shine][lighting:dynamic_flash][vfx_intro][immersive][golden_aura][no_text][high_end_cinematic]\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"core_prompt\": \"FPV high-impact one-take: camera executes continuous, ultra-smooth hyperspeed rotation around a photoreal golden spinning vortex, generating intense golden light trails on a pure black backdrop. In a seamless, cinematic morph (no cut or fade), the vortex transforms directly into a hyperreal metallic car key\u2014extreme detail, liquid gold glow, super-crisp metallic reflections. Dynamic snappy camera arcs and motion blur preserve immersive FPV energy. Advanced lighting: sharp dynamic flashes and dramatic golden aura. All transitions blend fluidly with no visible clipping. No text. [FPV][morph][ultra_hyperreal][snappy_smooth_camera_animation][dynamic_motion][lighting:dynamic_flash][high_end_cinematic][metallic_shine][golden_aura][vfx_intro][immersive][no_text]\"\n}",
        }
      }
    }