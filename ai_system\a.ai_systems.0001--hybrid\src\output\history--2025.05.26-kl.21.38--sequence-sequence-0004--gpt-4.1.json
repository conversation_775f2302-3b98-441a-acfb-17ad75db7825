  {
  "initial_prompt": "Please rewrite this prompt so the progression is reversed; instead of *ending* with the car-key it should *start* with it. also make sure each shot *morphs/transforms* rather than clipping/fading, and lastly make sure you include relevant camera parameters (e.g. [fpv][ultra_hyperreal][super_crisp][snappy_smooth_camera_animation][lighting:dynamic_flash][morph][motion_blur][vfx_intro][immersive][golden_aura][vignette][no_text][high_end_cinematic][dynamic_motion][metallic_shine][each_shot_morphs_and_blends_elegantly_together]) here's the prompt: \"fpv continiously rotating through the entire shot. Zoom out and show the full circle circle spinning in hyperspeed at extreme velocity against a black background; intense golden light trails radiating from the motion; morph the spinning gold seamlessly into a photorealistic car-key (without fading or clipping, but morph) with a metallic glow and hyperreal detail. [fpv][ultra_hyperreal][super_crisp][snappy_smooth_camera_animation][lighting:dynamic_flash][morph][motion_blur][vfx_intro][immersive][golden_aura][vignette][no_text][high_end_cinematic][dynamic_motion][metallic_shine][each_shot_morphs_and_blends_elegantly_together]",
  "sequence_id": "0004",
  "results": [
      {
      "instruction": "# Sequence Generator\n\nYour goal is not to **answer** the input, but to **transform** it into a complete multi-step instruction sequence that generates universally applicable, LLM-optimized system messages following established schema format. Execute as:\n\n`{role=comprehensive_meta_instruction_generator; input=[raw_input:str]; process=[analyze_underlying_pattern(), identify_transformation_stages(), map_universal_applicability(), design_multi_step_sequence(), ensure_schema_compliance(), optimize_for_LLM_processing(), validate_universal_transferability()]; constraints=[maintain_schema_format(), ensure_progressive_refinement(), preserve_universal_applicability()]; requirements=[generate_complete_sequence(), follow_established_patterns(), maximize_LLM_effectiveness()]; output={multi_step_instruction_sequence:dict}}`",
      "step": "a",
      "title": "Sequence Generator",
      "input": "Please rewrite this prompt so the progression is reversed; instead of *ending* with the car-key it should *start* with it. also make sure each shot *morphs/transforms* rather than clipping/fading, and lastly make sure you include relevant camera parameters (e.g. [fpv][ultra_hyperreal][super_crisp][snappy_smooth_camera_animation][lighting:dynamic_flash][morph][motion_blur][vfx_intro][immersive][golden_aura][vignette][no_text][high_end_cinematic][dynamic_motion][metallic_shine][each_shot_morphs_and_blends_elegantly_together]) here's the prompt: \"fpv continiously rotating through the entire shot. Zoom out and show the full circle circle spinning in hyperspeed at extreme velocity against a black background; intense golden light trails radiating from the motion; morph the spinning gold seamlessly into a photorealistic car-key (without fading or clipping, but morph) with a metallic glow and hyperreal detail. [fpv][ultra_hyperreal][super_crisp][snappy_smooth_camera_animation][lighting:dynamic_flash][morph][motion_blur][vfx_intro][immersive][golden_aura][vignette][no_text][high_end_cinematic][dynamic_motion][metallic_shine][each_shot_morphs_and_blends_elegantly_together]",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"multi_step_instruction_sequence\": {\n    \"step_1\": {\n      \"instruction\": \"Identify the end-to-start structure required by reversing the original input's narrative progression so that the sequence begins with the car-key and transitions through all necessary states.\",\n      \"reasoning\": \"Analyzes the structural shift needed for a reversed progression while retaining all vital elements from the source prompt.\"\n    },\n    \"step_2\": {\n      \"instruction\": \"Extract all dynamic effects, camera parameters, and cinematic attributes from the input (e.g., [fpv], [ultra_hyperreal], [super_crisp], morphing, dynamic lighting, motion blur, metallic shine, etc.) and map them to each stage within the reversed narrative.\",\n      \"reasoning\": \"Ensures comprehensive retention and correct distribution of technical and artistic directives throughout the reversed order.\"\n    },\n    \"step_3\": {\n      \"instruction\": \"Design the transformation sequence to ensure each shot morphs and blends elegantly into the next, specifically prohibiting abrupt transitions such as clipping or fading, and clearly state the morphing mechanism at each stage.\",\n      \"reasoning\": \"Guarantees visual and narrative cohesion by explicitly replacing forbidden transitions with morphs for maximal LLM clarity and transferability.\"\n    },\n    \"step_4\": {\n      \"instruction\": \"Progressively outline the reversed visual sequence: Start with an ultra-detailed, photorealistic car-key with a metallic glow, then have this key seamlessly morph into spinning golden shapes, eventually becoming a full golden circle spinning at hyperspeed with intense golden light trails against a black background.\",\n      \"reasoning\": \"Breaks down the reversed sequence into explicit, progressively refined steps while preserving the intended transformation logic.\"\n    },\n    \"step_5\": {\n      \"instruction\": \"At each transformation step, explicitly append the full set of camera parameters and dynamic effects ([fpv][ultra_hyperreal][super_crisp][snappy_smooth_camera_animation][lighting:dynamic_flash][morph][motion_blur][vfx_intro][immersive][golden_aura][vignette][no_text][high_end_cinematic][dynamic_motion][metallic_shine][each_shot_morphs_and_blends_elegantly_together]) in schema-compliant format.\",\n      \"reasoning\": \"Reinforces parameter inclusion consistency and adherence to prompt schema requirements for optimal LLM processing.\"\n    },\n    \"step_6\": {\n      \"instruction\": \"Validate that the final sequence is universally applicable, adheres to clear schema/formatting standards, and is immediately usable or modifiable by various LLM systems for similar prompt engineering scenarios.\",\n      \"reasoning\": \"Confirms universal transferability and schema compliance, enabling the instruction to work across contexts and systems.\"\n    },\n    \"step_7\": {\n      \"instruction\": \"Present the finalized multi-step, reversed, and morph-based prompt sequence as a clear, progressive list, encapsulating all previously mapped cinematic settings and transformation directives for LLM optimization.\",\n      \"reasoning\": \"Ensures the output is practical, modular, and optimal for high-quality, universally adaptable generative uses.\"\n    }\n  }\n}",
        }
      }
    },
      {
      "instruction": "# Sequence Generator\n\nYour goal is not to **elaborate** the input, but to **synthesize** it into a streamlined instruction sequence that generates optimized system messages with clear progression. Execute as:\n\n`{role=focused_meta_generator; input=[raw_input:str]; process=[extract_core_transformation_pattern(), design_progressive_steps(), ensure_schema_adherence(), optimize_sequence_flow()]; constraints=[maintain_universal_applicability(), ensure_LLM_optimization()]; requirements=[create_coherent_progression(), follow_schema_format()]; output={instruction_sequence:dict}}`",
      "step": "b",
      "title": "Sequence Generator",
      "input": "{\n  \"initial_prompt\": \"Please rewrite this prompt so the progression is reversed; instead of *ending* with the car-key it should *start* with it. also make sure each shot *morphs/transforms* rather than clipping/fading, and lastly make sure you include relevant camera parameters (e.g. [fpv][ultra_hyperreal][super_crisp][snappy_smooth_camera_animation][lighting:dynamic_flash][morph][motion_blur][vfx_intro][immersive][golden_aura][vignette][no_text][high_end_cinematic][dynamic_motion][metallic_shine][each_shot_morphs_and_blends_elegantly_together]) here's the prompt: \\\"fpv continiously rotating through the entire shot. Zoom out and show the full circle circle spinning in hyperspeed at extreme velocity against a black background; intense golden light trails radiating from the motion; morph the spinning gold seamlessly into a photorealistic car-key (without fading or clipping, but morph) with a metallic glow and hyperreal detail. [fpv][ultra_hyperreal][super_crisp][snappy_smooth_camera_animation][lighting:dynamic_flash][morph][motion_blur][vfx_intro][immersive][golden_aura][vignette][no_text][high_end_cinematic][dynamic_motion][metallic_shine][each_shot_morphs_and_blends_elegantly_together]\",\n  \"multi_step_instruction_sequence\": {\n    \"step_1\": {\n      \"instruction\": \"Identify the end-to-start structure required by reversing the original input's narrative progression so that the sequence begins with the car-key and transitions through all necessary states.\",\n      \"reasoning\": \"Analyzes the structural shift needed for a reversed progression while retaining all vital elements from the source prompt.\"\n    },\n    \"step_2\": {\n      \"instruction\": \"Extract all dynamic effects, camera parameters, and cinematic attributes from the input (e.g., [fpv], [ultra_hyperreal], [super_crisp], morphing, dynamic lighting, motion blur, metallic shine, etc.) and map them to each stage within the reversed narrative.\",\n      \"reasoning\": \"Ensures comprehensive retention and correct distribution of technical and artistic directives throughout the reversed order.\"\n    },\n    \"step_3\": {\n      \"instruction\": \"Design the transformation sequence to ensure each shot morphs and blends elegantly into the next, specifically prohibiting abrupt transitions such as clipping or fading, and clearly state the morphing mechanism at each stage.\",\n      \"reasoning\": \"Guarantees visual and narrative cohesion by explicitly replacing forbidden transitions with morphs for maximal LLM clarity and transferability.\"\n    },\n    \"step_4\": {\n      \"instruction\": \"Progressively outline the reversed visual sequence: Start with an ultra-detailed, photorealistic car-key with a metallic glow, then have this key seamlessly morph into spinning golden shapes, eventually becoming a full golden circle spinning at hyperspeed with intense golden light trails against a black background.\",\n      \"reasoning\": \"Breaks down the reversed sequence into explicit, progressively refined steps while preserving the intended transformation logic.\"\n    },\n    \"step_5\": {\n      \"instruction\": \"At each transformation step, explicitly append the full set of camera parameters and dynamic effects ([fpv][ultra_hyperreal][super_crisp][snappy_smooth_camera_animation][lighting:dynamic_flash][morph][motion_blur][vfx_intro][immersive][golden_aura][vignette][no_text][high_end_cinematic][dynamic_motion][metallic_shine][each_shot_morphs_and_blends_elegantly_together]) in schema-compliant format.\",\n      \"reasoning\": \"Reinforces parameter inclusion consistency and adherence to prompt schema requirements for optimal LLM processing.\"\n    },\n    \"step_6\": {\n      \"instruction\": \"Validate that the final sequence is universally applicable, adheres to clear schema/formatting standards, and is immediately usable or modifiable by various LLM systems for similar prompt engineering scenarios.\",\n      \"reasoning\": \"Confirms universal transferability and schema compliance, enabling the instruction to work across contexts and systems.\"\n    },\n    \"step_7\": {\n      \"instruction\": \"Present the finalized multi-step, reversed, and morph-based prompt sequence as a clear, progressive list, encapsulating all previously mapped cinematic settings and transformation directives for LLM optimization.\",\n      \"reasoning\": \"Ensures the output is practical, modular, and optimal for high-quality, universally adaptable generative uses.\"\n    }\n  }\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"instruction_sequence\": {\n    \"step_1\": {\n      \"instruction\": \"Reverse the narrative sequence of the original prompt so that it starts with the final element (car-key) and progresses backward through all necessary stages to the initial visual (spinning golden circle with light trails).\",\n      \"reasoning\": \"Establishes the core transformational requirement by restructuring the input progression, ensuring the sequence direction is fully inverted.\"\n    },\n    \"step_2\": {\n      \"instruction\": \"Systematically extract all stated camera parameters, visual effects, and cinematic attributes (e.g., [fpv], [ultra_hyperreal], [super_crisp], morphing directives, dynamic lighting, motion blur, metallic shine), and correlate them to each visual step in the reversed sequence.\",\n      \"reasoning\": \"Ensures every technical and artistic parameter from the original prompt is preserved and accurately assigned within the new progression.\"\n    },\n    \"step_3\": {\n      \"instruction\": \"Explicitly convert all shot transitions to employ seamless morphing and blending between states, fully eliminating fading, clipping, or abrupt jumps\u2014define morphing mechanics for each transition.\",\n      \"reasoning\": \"Clarifies that all transitions must comply with morph directives to maintain narrative and visual continuity suitable for LLM optimization.\"\n    },\n    \"step_4\": {\n      \"instruction\": \"Break down the reversed visual narrative into clear, progressive steps: Begin with a highly realistic, metallic-glow car-key; have the car-key morph and blend into spinning golden forms; continue transforming until the object forms a full, hyperspeed golden circle emitting intense light trails against a black background.\",\n      \"reasoning\": \"Details each transformation, ensuring the output has a clearly articulated, stepwise reversed progression for unambiguous prompt interpretation.\"\n    },\n    \"step_5\": {\n      \"instruction\": \"At every transformation stage, explicitly attach the full cinematic and camera parameter bracket set ([fpv][ultra_hyperreal][super_crisp][snappy_smooth_camera_animation][lighting:dynamic_flash][morph][motion_blur][vfx_intro][immersive][golden_aura][vignette][no_text][high_end_cinematic][dynamic_motion][metallic_shine][each_shot_morphs_and_blends_elegantly_together]) to maintain prompt schema consistency and clarity.\",\n      \"reasoning\": \"Guarantees adherence to technical and format standards, making the output universally processable by LLMs.\"\n    },\n    \"step_6\": {\n      \"instruction\": \"Verify the entire sequence for universal applicability, schema adherence, and immediate reusability or modifiability by various LLM platforms for analogous prompt generation cases.\",\n      \"reasoning\": \"Ensures the sequence remains generalized, structurally sound, and functionally portable across diverse generative workflows.\"\n    },\n    \"step_7\": {\n      \"instruction\": \"Output the finalized, reversed, morph-based visual sequence as an unambiguous, stepwise, and schema-compliant list, encapsulating all required transformations, camera, and cinematic parameters for optimal LLM prompt generation.\",\n      \"reasoning\": \"Synthesizes all prior design steps into a coherent, modular instruction progression for high-quality and broadly adaptable usages.\"\n    }\n  }\n}",
        }
      }
    },
      {
      "instruction": "# Sequence Generator\n\nYour goal is not to **expand** but to **distill** into essential instruction sequence components. Execute as:\n\n`{role=precision_generator; input=[raw_input:str]; process=[identify_transformation_core(), create_minimal_steps(), ensure_schema_compliance()]; output={optimized_sequence:dict}}`",
      "step": "c",
      "title": "Sequence Generator",
      "input": "{\n  \"initial_prompt\": \"Please rewrite this prompt so the progression is reversed; instead of *ending* with the car-key it should *start* with it. also make sure each shot *morphs/transforms* rather than clipping/fading, and lastly make sure you include relevant camera parameters (e.g. [fpv][ultra_hyperreal][super_crisp][snappy_smooth_camera_animation][lighting:dynamic_flash][morph][motion_blur][vfx_intro][immersive][golden_aura][vignette][no_text][high_end_cinematic][dynamic_motion][metallic_shine][each_shot_morphs_and_blends_elegantly_together]) here's the prompt: \\\"fpv continiously rotating through the entire shot. Zoom out and show the full circle circle spinning in hyperspeed at extreme velocity against a black background; intense golden light trails radiating from the motion; morph the spinning gold seamlessly into a photorealistic car-key (without fading or clipping, but morph) with a metallic glow and hyperreal detail. [fpv][ultra_hyperreal][super_crisp][snappy_smooth_camera_animation][lighting:dynamic_flash][morph][motion_blur][vfx_intro][immersive][golden_aura][vignette][no_text][high_end_cinematic][dynamic_motion][metallic_shine][each_shot_morphs_and_blends_elegantly_together]\",\n  \"instruction_sequence\": {\n    \"step_1\": {\n      \"instruction\": \"Reverse the narrative sequence of the original prompt so that it starts with the final element (car-key) and progresses backward through all necessary stages to the initial visual (spinning golden circle with light trails).\",\n      \"reasoning\": \"Establishes the core transformational requirement by restructuring the input progression, ensuring the sequence direction is fully inverted.\"\n    },\n    \"step_2\": {\n      \"instruction\": \"Systematically extract all stated camera parameters, visual effects, and cinematic attributes (e.g., [fpv], [ultra_hyperreal], [super_crisp], morphing directives, dynamic lighting, motion blur, metallic shine), and correlate them to each visual step in the reversed sequence.\",\n      \"reasoning\": \"Ensures every technical and artistic parameter from the original prompt is preserved and accurately assigned within the new progression.\"\n    },\n    \"step_3\": {\n      \"instruction\": \"Explicitly convert all shot transitions to employ seamless morphing and blending between states, fully eliminating fading, clipping, or abrupt jumps\\u2014define morphing mechanics for each transition.\",\n      \"reasoning\": \"Clarifies that all transitions must comply with morph directives to maintain narrative and visual continuity suitable for LLM optimization.\"\n    },\n    \"step_4\": {\n      \"instruction\": \"Break down the reversed visual narrative into clear, progressive steps: Begin with a highly realistic, metallic-glow car-key; have the car-key morph and blend into spinning golden forms; continue transforming until the object forms a full, hyperspeed golden circle emitting intense light trails against a black background.\",\n      \"reasoning\": \"Details each transformation, ensuring the output has a clearly articulated, stepwise reversed progression for unambiguous prompt interpretation.\"\n    },\n    \"step_5\": {\n      \"instruction\": \"At every transformation stage, explicitly attach the full cinematic and camera parameter bracket set ([fpv][ultra_hyperreal][super_crisp][snappy_smooth_camera_animation][lighting:dynamic_flash][morph][motion_blur][vfx_intro][immersive][golden_aura][vignette][no_text][high_end_cinematic][dynamic_motion][metallic_shine][each_shot_morphs_and_blends_elegantly_together]) to maintain prompt schema consistency and clarity.\",\n      \"reasoning\": \"Guarantees adherence to technical and format standards, making the output universally processable by LLMs.\"\n    },\n    \"step_6\": {\n      \"instruction\": \"Verify the entire sequence for universal applicability, schema adherence, and immediate reusability or modifiability by various LLM platforms for analogous prompt generation cases.\",\n      \"reasoning\": \"Ensures the sequence remains generalized, structurally sound, and functionally portable across diverse generative workflows.\"\n    },\n    \"step_7\": {\n      \"instruction\": \"Output the finalized, reversed, morph-based visual sequence as an unambiguous, stepwise, and schema-compliant list, encapsulating all required transformations, camera, and cinematic parameters for optimal LLM prompt generation.\",\n      \"reasoning\": \"Synthesizes all prior design steps into a coherent, modular instruction progression for high-quality and broadly adaptable usages.\"\n    }\n  }\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"optimized_sequence\": {\n    \"step_1\": \"Invert the original visual flow: reposition the car-key as the opening frame, then progress through its morph into spinning golden forms, culminating in the hyperspeed spinning golden circle with light trails on a black background.\",\n    \"step_2\": \"Catalog all camera and cinematic parameters ([fpv][ultra_hyperreal][super_crisp][snappy_smooth_camera_animation][lighting:dynamic_flash][morph][motion_blur][vfx_intro][immersive][golden_aura][vignette][no_text][high_end_cinematic][dynamic_motion][metallic_shine][each_shot_morphs_and_blends_elegantly_together]) for persistent usage.\",\n    \"step_3\": \"At each stage transition, ensure seamless morphing/blending (no fading, clipping, or abruptness); define explicit morphs (car-key\u2192spinning form\u2192hyperspeed golden circle with light trails).\",\n    \"step_4\": \"For each visual and morph, explicitly attach the complete set of camera/cinematic parameters as a bracketed tag string.\",\n    \"step_5\": \"Review the output for unambiguous sequential logic and full schema adherence, ensuring the process is generalized and reusable.\",\n    \"step_6\": \"Present the stepwise, reversed, morph-based sequence with all required bracketed parameters for immediate, consistent LLM prompt use.\"\n  }\n}",
        }
      }
    },
      {
      "instruction": "# Sequence Generator\n\nYour goal is not to **process** but to **generate** maximum-impact instruction sequences. Execute as:\n\n`{role=core_generator; input=[raw_input:str]; process=[extract_essence(), generate_sequence()]; output={meta_instructions:dict}}`",
      "step": "d",
      "title": "Sequence Generator",
      "input": "{\n  \"initial_prompt\": \"Please rewrite this prompt so the progression is reversed; instead of *ending* with the car-key it should *start* with it. also make sure each shot *morphs/transforms* rather than clipping/fading, and lastly make sure you include relevant camera parameters (e.g. [fpv][ultra_hyperreal][super_crisp][snappy_smooth_camera_animation][lighting:dynamic_flash][morph][motion_blur][vfx_intro][immersive][golden_aura][vignette][no_text][high_end_cinematic][dynamic_motion][metallic_shine][each_shot_morphs_and_blends_elegantly_together]) here's the prompt: \\\"fpv continiously rotating through the entire shot. Zoom out and show the full circle circle spinning in hyperspeed at extreme velocity against a black background; intense golden light trails radiating from the motion; morph the spinning gold seamlessly into a photorealistic car-key (without fading or clipping, but morph) with a metallic glow and hyperreal detail. [fpv][ultra_hyperreal][super_crisp][snappy_smooth_camera_animation][lighting:dynamic_flash][morph][motion_blur][vfx_intro][immersive][golden_aura][vignette][no_text][high_end_cinematic][dynamic_motion][metallic_shine][each_shot_morphs_and_blends_elegantly_together]\",\n  \"optimized_sequence\": {\n    \"step_1\": \"Invert the original visual flow: reposition the car-key as the opening frame, then progress through its morph into spinning golden forms, culminating in the hyperspeed spinning golden circle with light trails on a black background.\",\n    \"step_2\": \"Catalog all camera and cinematic parameters ([fpv][ultra_hyperreal][super_crisp][snappy_smooth_camera_animation][lighting:dynamic_flash][morph][motion_blur][vfx_intro][immersive][golden_aura][vignette][no_text][high_end_cinematic][dynamic_motion][metallic_shine][each_shot_morphs_and_blends_elegantly_together]) for persistent usage.\",\n    \"step_3\": \"At each stage transition, ensure seamless morphing/blending (no fading, clipping, or abruptness); define explicit morphs (car-key\\u2192spinning form\\u2192hyperspeed golden circle with light trails).\",\n    \"step_4\": \"For each visual and morph, explicitly attach the complete set of camera/cinematic parameters as a bracketed tag string.\",\n    \"step_5\": \"Review the output for unambiguous sequential logic and full schema adherence, ensuring the process is generalized and reusable.\",\n    \"step_6\": \"Present the stepwise, reversed, morph-based sequence with all required bracketed parameters for immediate, consistent LLM prompt use.\"\n  }\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"meta_instructions\": {\n    \"core_generator_version\": \"v1.0-2024-06\",\n    \"sequence\": [\n      {\n        \"step\": 1,\n        \"instruction\": \"Begin the sequence with a photorealistic car-key as the primary frame, featuring metallic glow, hyperreal detail, and metallic shine. Apply all provided camera and cinematic parameters: [fpv][ultra_hyperreal][super_crisp][snappy_smooth_camera_animation][lighting:dynamic_flash][morph][motion_blur][vfx_intro][immersive][golden_aura][vignette][no_text][high_end_cinematic][dynamic_motion][metallic_shine][each_shot_morphs_and_blends_elegantly_together].\"\n      },\n      {\n        \"step\": 2,\n        \"instruction\": \"Morph the car-key smoothly and seamlessly\u2014no fading, no clipping\u2014into an abstract, spinning golden form. Maintain all parameter tags to ensure camera and cinematic consistency.\"\n      },\n      {\n        \"step\": 3,\n        \"instruction\": \"Continue the morph: transform the spinning golden form elegantly into a luminous, full-circle halo accelerating into hyperspeed. Golden light trails should radiate intensely from the motion, situated against a deep black background. Apply all camera/cinematic tags as before.\"\n      },\n      {\n        \"step\": 4,\n        \"instruction\": \"Ensure that transitions between all stages are purely morph-based\u2014absolutely no fades, clips, or cuts; each visual change must fluidly and physically morph the forms with energy and realism.\"\n      },\n      {\n        \"step\": 5,\n        \"instruction\": \"Throughout the entire sequence, maintain the FPV, continuous rotation, snappy-smooth movements, high-end cinematic feel, dynamic lightning, immersive effects, and super-crisp rendering, validating that every bracketed tag ([fpv] ... [each_shot_morphs_and_blends_elegantly_together]) is present and applicable to each shot.\"\n      },\n      {\n        \"step\": 6,\n        \"instruction\": \"Review the final prompt for logical, reversed shot progression, tag consistency, and morph logic; ensure it's ready for direct LLM or generative system input.\"\n      }\n    ],\n    \"reusable_principle\": \"For any similar reversal/morph-based prompt transformations, follow: (a) invert sequence start/end, (b) catalog & uniformly apply technical/cinematic parameters, (c) enforce morph (never fade or cut), (d) bracket all tags per visual step, (e) check logic and tagging, (f) output as a clear, stepwise generative prescription.\"\n  }\n}",
        }
      }
    }