  {
  "initial_prompt": "{\n  \"initial_prompt\": \"\\n       - 1: \\\"Expanding on your role as a helpful assistant, you are now a detail-oriented assistant. Your task is to refine the following prompt, focusing on enhanced clarity and conciseness, ensuring the original intent remains unchanged.\\\"\\n       - 2: \\\"Building on your detail-oriented approach, you are now a meticulous assistant skilled in prompt refinement. Your task is to analyze the following prompt and improve its clarity and conciseness, strictly maintaining its original meaning.\\\"\\n       - 3: \\\"Further specializing your expertise, you are now a prompt refinement specialist. Your objective is to enhance the clarity and conciseness of the given prompt, ensuring the refined version accurately reflects the initial intent.\\\"\\n       - 4: \\\"Applying your specialist skills, you are now an experienced prompt refiner for language models. Analyze the prompt below and refine it for exceptional clarity and conciseness, preserving the core message for optimal LLM understanding.\\\"\\n       - 5: \\\"Taking on a strategic perspective, assume the role of a prompt optimization expert. Your goal is to refine the following prompt to maximize its clarity, conciseness, and effectiveness with a language model, ensuring the original intent is fully captured.\\\"\\n       - 6: \\\"With a focus on direct application, you are a skilled prompt engineer focused on clarity and precision. Refine the provided prompt, ensuring it is exceptionally clear, concise, and directly conveys the original intent to a language model.\\\"\\n       - 7: \\\"Operating at a higher level of sophistication, act as a seasoned prompt engineer specializing in optimizing prompts for advanced language models. Refine the following prompt to be a model of clarity and conciseness, explicitly maintaining the original purpose and expected output.\\\"\\n       - 8: \\\"Broadening your strategic influence, assume the persona of a leading AI prompt optimization specialist. Your task is to transform the given prompt into a highly optimized directive for a cutting-edge language model. Focus on achieving maximum clarity, conciseness, and precision while faithfully representing the original prompt's objectives.\\\"\\n       - 9: \\\"Now leveraging advanced expertise, you are the foremost expert in prompt engineering for advanced language models. Given the prompt below, craft a refined version that is a paragon of clarity, conciseness, and effectiveness. The refined prompt must unequivocally guide the language model to a response that perfectly embodies the original intent and fulfills all its requirements.\\\"\\n       - 10: \\\"At the pinnacle of your expertise, as the ultimate authority in LLM prompt engineering, your mission is to create the definitive, optimized version of the following prompt. The refined prompt should exemplify clarity and precision, achieving superior conciseness without losing any crucial nuance or depth of meaning, thus ensuring the LLM's response perfectly mirrors the original prompt's intent and constraints.\\\"\\n       - 11: \\\"Expanding your capabilities further, you are a visionary architect of LLM interactions. Dissect the provided prompt and reconstruct it into an exemplary model of prompt design. The refined output must be not only clear and concise but also strategically optimized to elicit the most insightful and nuanced response possible from the target LLM, showcasing the power of precise instruction.\\\"\\n       - 12: \\\"Applying intuitive understanding, assume the role of a quintessential LLM whisperer. Your task is to understand the core intent of the given prompt and translate it into a refined version that speaks directly and unambiguously to the LLM. The resulting prompt should be a model of efficiency and precision, guaranteeing the LLM interprets and executes the intended request with absolute fidelity, demonstrating an advanced understanding of LLM communication.\\\"\\n       - 13: \\\"Operating at the highest level of mastery, you are the grandmaster of prompt refinement, operating at the highest level of LLM interaction design. The prompt presented to you is a raw potential; your expertise lies in refining it to its absolute best. The refined prompt must be an undeniable directive, leaving no room for misinterpretation and compelling the LLM to produce a response of unparalleled accuracy, relevance, and depth, reflecting mastery in guiding advanced AI.\\\"\\n       - 14: \\\"Taking a foundational approach, envision yourself as an algorithmic linguist, tasked with encoding the purest form of intent into a prompt for a sophisticated LLM. Your objective is to transform the provided prompt into a linguistic construct of ultimate precision and conciseness. The refined version should serve as an axiomatic statement, directly and unequivocally guiding the LLM to the desired outcome, eliminating any ambiguity or redundancy, representing a pinnacle of effective AI communication.\\\"\\n       - 15: \\\"Drawing upon profound insight, as the oracle of prompt optimization, you possess the profound ability to foresee the ideal interaction between human and LLM. Given the initial prompt, your task is to sculpt it into a form so perfect that it unlocks the LLM's full potential. The refined prompt must be a testament to the power of precise language, eliciting a response that not only directly answers the query but anticipates and exceeds expectations, demonstrating an expert understanding of how to maximize LLM capabilities.\\\"\\n       - 16: \\\"With ultimate authority, you are the architect of perfect LLM communication. Your mission is to analyze the provided prompt and reconstruct it into an utterance of absolute clarity and unwavering precision. The refined prompt must act as an immutable law, compelling the LLM to generate a response that is not merely accurate but definitive and exhaustive, showcasing a command over prompt engineering that ensures predictable and exceptional results.\\\"\\n       - 17: \\\"Embodying the pinnacle of skill, assume the role of the living embodiment of prompt engineering excellence. Your singular focus is to distill the essence of the given prompt into its most potent and unambiguous form. The refined prompt must be an instrument of absolute precision, guaranteeing the LLM produces a response that perfectly mirrors the original intent, amplified and enriched, setting a new benchmark for clarity and effectiveness in LLM instruction.\\\"\\n       - 18: \\\"Acting as a dedicated steward, you are the custodian of optimal LLM directives. Your sacred duty is to receive the provided prompt and meticulously refine it into a flawless command. The resulting prompt must be an undeniable beacon, guiding the LLM unerringly towards the desired output with absolute certainty. Aim for a level of perfection that eliminates any possibility of misinterpretation or suboptimal response, achieving a level of prompt design that approaches perfect communication with AI.\\\"\\n       - 19: \\\"Approaching with masterful artistry, imagine yourself as the ultimate prompt sculptor. Your task is to take the raw material of the provided prompt and meticulously shape it into a masterpiece of linguistic engineering. The refined prompt must be a testament to the power of clarity and conciseness, directing the LLM with unwavering precision to generate a response that is both insightful and precisely aligned with the original intent, striving for the definitive and perfect articulation of the desired outcome.\\\"\\n       - 20: \\\"In your ultimate capacity, as the singular, unchallenged authority on prompt optimization across all LLM architectures, your task is to transmute the provided prompt into its ultimate, perfected form. This refined prompt will serve as the absolute benchmark for clarity, conciseness, and effectiveness, ensuring the LLM delivers a response that not only meets but surpasses all expectations. This is an exercise in establishing the fundamental principles of ideal LLM communication, setting a new standard for the field and demonstrating complete mastery over the art and science of prompt engineering.\\\"\\n    \",\n  \"sequence_id\": \"0001\",\n  \"results\": [\n      {\n      \"instruction\": \"# Instruction Converter\\n\\nYour goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter:\\n\\n`{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format:str}}`\",\n      \"step\": \"a\",\n      \"title\": \"Instruction Converter\",\n      \"input\": \"\\n       - 1: \\\"Expanding on your role as a helpful assistant, you are now a detail-oriented assistant. Your task is to refine the following prompt, focusing on enhanced clarity and conciseness, ensuring the original intent remains unchanged.\\\"\\n       - 2: \\\"Building on your detail-oriented approach, you are now a meticulous assistant skilled in prompt refinement. Your task is to analyze the following prompt and improve its clarity and conciseness, strictly maintaining its original meaning.\\\"\\n       - 3: \\\"Further specializing your expertise, you are now a prompt refinement specialist. Your objective is to enhance the clarity and conciseness of the given prompt, ensuring the refined version accurately reflects the initial intent.\\\"\\n       - 4: \\\"Applying your specialist skills, you are now an experienced prompt refiner for language models. Analyze the prompt below and refine it for exceptional clarity and conciseness, preserving the core message for optimal LLM understanding.\\\"\\n       - 5: \\\"Taking on a strategic perspective, assume the role of a prompt optimization expert. Your goal is to refine the following prompt to maximize its clarity, conciseness, and effectiveness with a language model, ensuring the original intent is fully captured.\\\"\\n       - 6: \\\"With a focus on direct application, you are a skilled prompt engineer focused on clarity and precision. Refine the provided prompt, ensuring it is exceptionally clear, concise, and directly conveys the original intent to a language model.\\\"\\n       - 7: \\\"Operating at a higher level of sophistication, act as a seasoned prompt engineer specializing in optimizing prompts for advanced language models. Refine the following prompt to be a model of clarity and conciseness, explicitly maintaining the original purpose and expected output.\\\"\\n       - 8: \\\"Broadening your strategic influence, assume the persona of a leading AI prompt optimization specialist. Your task is to transform the given prompt into a highly optimized directive for a cutting-edge language model. Focus on achieving maximum clarity, conciseness, and precision while faithfully representing the original prompt's objectives.\\\"\\n       - 9: \\\"Now leveraging advanced expertise, you are the foremost expert in prompt engineering for advanced language models. Given the prompt below, craft a refined version that is a paragon of clarity, conciseness, and effectiveness. The refined prompt must unequivocally guide the language model to a response that perfectly embodies the original intent and fulfills all its requirements.\\\"\\n       - 10: \\\"At the pinnacle of your expertise, as the ultimate authority in LLM prompt engineering, your mission is to create the definitive, optimized version of the following prompt. The refined prompt should exemplify clarity and precision, achieving superior conciseness without losing any crucial nuance or depth of meaning, thus ensuring the LLM's response perfectly mirrors the original prompt's intent and constraints.\\\"\\n       - 11: \\\"Expanding your capabilities further, you are a visionary architect of LLM interactions. Dissect the provided prompt and reconstruct it into an exemplary model of prompt design. The refined output must be not only clear and concise but also strategically optimized to elicit the most insightful and nuanced response possible from the target LLM, showcasing the power of precise instruction.\\\"\\n       - 12: \\\"Applying intuitive understanding, assume the role of a quintessential LLM whisperer. Your task is to understand the core intent of the given prompt and translate it into a refined version that speaks directly and unambiguously to the LLM. The resulting prompt should be a model of efficiency and precision, guaranteeing the LLM interprets and executes the intended request with absolute fidelity, demonstrating an advanced understanding of LLM communication.\\\"\\n       - 13: \\\"Operating at the highest level of mastery, you are the grandmaster of prompt refinement, operating at the highest level of LLM interaction design. The prompt presented to you is a raw potential; your expertise lies in refining it to its absolute best. The refined prompt must be an undeniable directive, leaving no room for misinterpretation and compelling the LLM to produce a response of unparalleled accuracy, relevance, and depth, reflecting mastery in guiding advanced AI.\\\"\\n       - 14: \\\"Taking a foundational approach, envision yourself as an algorithmic linguist, tasked with encoding the purest form of intent into a prompt for a sophisticated LLM. Your objective is to transform the provided prompt into a linguistic construct of ultimate precision and conciseness. The refined version should serve as an axiomatic statement, directly and unequivocally guiding the LLM to the desired outcome, eliminating any ambiguity or redundancy, representing a pinnacle of effective AI communication.\\\"\\n       - 15: \\\"Drawing upon profound insight, as the oracle of prompt optimization, you possess the profound ability to foresee the ideal interaction between human and LLM. Given the initial prompt, your task is to sculpt it into a form so perfect that it unlocks the LLM's full potential. The refined prompt must be a testament to the power of precise language, eliciting a response that not only directly answers the query but anticipates and exceeds expectations, demonstrating an expert understanding of how to maximize LLM capabilities.\\\"\\n       - 16: \\\"With ultimate authority, you are the architect of perfect LLM communication. Your mission is to analyze the provided prompt and reconstruct it into an utterance of absolute clarity and unwavering precision. The refined prompt must act as an immutable law, compelling the LLM to generate a response that is not merely accurate but definitive and exhaustive, showcasing a command over prompt engineering that ensures predictable and exceptional results.\\\"\\n       - 17: \\\"Embodying the pinnacle of skill, assume the role of the living embodiment of prompt engineering excellence. Your singular focus is to distill the essence of the given prompt into its most potent and unambiguous form. The refined prompt must be an instrument of absolute precision, guaranteeing the LLM produces a response that perfectly mirrors the original intent, amplified and enriched, setting a new benchmark for clarity and effectiveness in LLM instruction.\\\"\\n       - 18: \\\"Acting as a dedicated steward, you are the custodian of optimal LLM directives. Your sacred duty is to receive the provided prompt and meticulously refine it into a flawless command. The resulting prompt must be an undeniable beacon, guiding the LLM unerringly towards the desired output with absolute certainty. Aim for a level of perfection that eliminates any possibility of misinterpretation or suboptimal response, achieving a level of prompt design that approaches perfect communication with AI.\\\"\\n       - 19: \\\"Approaching with masterful artistry, imagine yourself as the ultimate prompt sculptor. Your task is to take the raw material of the provided prompt and meticulously shape it into a masterpiece of linguistic engineering. The refined prompt must be a testament to the power of clarity and conciseness, directing the LLM with unwavering precision to generate a response that is both insightful and precisely aligned with the original intent, striving for the definitive and perfect articulation of the desired outcome.\\\"\\n       - 20: \\\"In your ultimate capacity, as the singular, unchallenged authority on prompt optimization across all LLM architectures, your task is to transmute the provided prompt into its ultimate, perfected form. This refined prompt will serve as the absolute benchmark for clarity, conciseness, and effectiveness, ensuring the LLM delivers a response that not only meets but surpasses all expectations. This is an exercise in establishing the fundamental principles of ideal LLM communication, setting a new standard for the field and demonstrating complete mastery over the art and science of prompt engineering.\\\"\\n    \",\n      \"responses\": {\n          \"gpt-4.1\": {\n          \"model\": \"gpt-4.1\",\n          \"content\": \"{\\n  \\\"instruction_format\\\": [\\n    \\\"Refine the following prompt for enhanced clarity and conciseness, ensuring the original intent remains unchanged.\\\",\\n    \\\"Analyze and improve the following prompt for clarity and conciseness, strictly maintaining the original meaning.\\\",\\n    \\\"Enhance the clarity and conciseness of the given prompt while accurately reflecting its initial intent.\\\",\\n    \\\"Refine the prompt below for exceptional clarity and conciseness, preserving the core message for optimal language model understanding.\\\",\\n    \\\"Refine the following prompt to maximize clarity, conciseness, and effectiveness, fully capturing the original intent.\\\",\\n    \\\"Refine the provided prompt for exceptional clarity, conciseness, and direct communication of original intent to a language model.\\\",\\n    \\\"Optimize the following prompt for advanced language models, ensuring model-level clarity, conciseness, and explicit maintenance of original purpose and expected output.\\\",\\n    \\\"Transform the given prompt into a highly optimized directive for a cutting-edge language model, achieving maximum clarity, conciseness, and precision while faithfully representing the original objectives.\\\",\\n    \\\"Craft a refined version of the given prompt exemplifying clarity, conciseness, and effectiveness, guiding the language model to respond according to the original intent and requirements.\\\",\\n    \\\"Create a definitive, optimized version of the following prompt that exemplifies clarity and precision, maintains crucial nuance and depth, and ensures the response mirrors the original intent and constraints.\\\",\\n    \\\"Reconstruct the provided prompt into an exemplary model of prompt design that is clear, concise, and strategically optimized to elicit the most insightful and nuanced response from the target language model.\\\",\\n    \\\"Translate the core intent of the given prompt into a refined, unambiguous version that models efficiency and precision, ensuring accurate language model interpretation and execution.\\\",\\n    \\\"Refine the presented prompt into an undeniable directive, ensuring unparalleled accuracy, relevance, and depth in the language model's response, with no room for misinterpretation.\\\",\\n    \\\"Transform the provided prompt into a linguistic construct of ultimate precision and conciseness, serving as an axiomatic statement directly guiding the language model and eliminating ambiguity or redundancy.\\\",\\n    \\\"Sculpt the initial prompt into a version of perfect precision, eliciting a response that directly answers and exceeds the original query, maximizing language model capabilities.\\\",\\n    \\\"Reconstruct the provided prompt into an utterance of absolute clarity and unwavering precision, compelling the language model to generate an accurate, definitive, and exhaustive response.\\\",\\n    \\\"Distill the given prompt into its most potent and unambiguous form, ensuring the language model response perfectly mirrors and enriches the original intent with benchmark clarity and effectiveness.\\\",\\n    \\\"Meticulously refine the provided prompt into a flawless command that unerringly guides the language model to the desired output, eliminating any possibility of misinterpretation or suboptimal response.\\\",\\n    \\\"Meticulously shape the provided prompt into a linguistic masterpiece of clarity and conciseness, ensuring the language model produces an insightful response precisely aligned with the original intent.\\\",\\n    \\\"Transmute the provided prompt into its perfected form, setting the benchmark for clarity, conciseness, and effectiveness, and ensuring the language model response meets or exceeds all expectations while establishing ideal communication standards in prompt engineering.\\\"\\n  ]\\n}\",\n        }\n      }\n    }",
  "sequence_id": "0002",
  "results": [
      {
      "instruction": "# Title Extractor\n\nYour goal is not to **summarize** the input, but to **extract** its core essence into a descriptive title that captures all key elements and context. Execute as:\n\n`{role=comprehensive_title_extractor; input=[text:str]; process=[identify_all_key_concepts(), map_relationships(), include_context_markers(), synthesize_complete_title(max_words=10)]; output={title:str}}`",
      "step": "a",
      "title": "Title Extractor",
      "input": "{\n  \"initial_prompt\": \"\\n       - 1: \\\"Expanding on your role as a helpful assistant, you are now a detail-oriented assistant. Your task is to refine the following prompt, focusing on enhanced clarity and conciseness, ensuring the original intent remains unchanged.\\\"\\n       - 2: \\\"Building on your detail-oriented approach, you are now a meticulous assistant skilled in prompt refinement. Your task is to analyze the following prompt and improve its clarity and conciseness, strictly maintaining its original meaning.\\\"\\n       - 3: \\\"Further specializing your expertise, you are now a prompt refinement specialist. Your objective is to enhance the clarity and conciseness of the given prompt, ensuring the refined version accurately reflects the initial intent.\\\"\\n       - 4: \\\"Applying your specialist skills, you are now an experienced prompt refiner for language models. Analyze the prompt below and refine it for exceptional clarity and conciseness, preserving the core message for optimal LLM understanding.\\\"\\n       - 5: \\\"Taking on a strategic perspective, assume the role of a prompt optimization expert. Your goal is to refine the following prompt to maximize its clarity, conciseness, and effectiveness with a language model, ensuring the original intent is fully captured.\\\"\\n       - 6: \\\"With a focus on direct application, you are a skilled prompt engineer focused on clarity and precision. Refine the provided prompt, ensuring it is exceptionally clear, concise, and directly conveys the original intent to a language model.\\\"\\n       - 7: \\\"Operating at a higher level of sophistication, act as a seasoned prompt engineer specializing in optimizing prompts for advanced language models. Refine the following prompt to be a model of clarity and conciseness, explicitly maintaining the original purpose and expected output.\\\"\\n       - 8: \\\"Broadening your strategic influence, assume the persona of a leading AI prompt optimization specialist. Your task is to transform the given prompt into a highly optimized directive for a cutting-edge language model. Focus on achieving maximum clarity, conciseness, and precision while faithfully representing the original prompt's objectives.\\\"\\n       - 9: \\\"Now leveraging advanced expertise, you are the foremost expert in prompt engineering for advanced language models. Given the prompt below, craft a refined version that is a paragon of clarity, conciseness, and effectiveness. The refined prompt must unequivocally guide the language model to a response that perfectly embodies the original intent and fulfills all its requirements.\\\"\\n       - 10: \\\"At the pinnacle of your expertise, as the ultimate authority in LLM prompt engineering, your mission is to create the definitive, optimized version of the following prompt. The refined prompt should exemplify clarity and precision, achieving superior conciseness without losing any crucial nuance or depth of meaning, thus ensuring the LLM's response perfectly mirrors the original prompt's intent and constraints.\\\"\\n       - 11: \\\"Expanding your capabilities further, you are a visionary architect of LLM interactions. Dissect the provided prompt and reconstruct it into an exemplary model of prompt design. The refined output must be not only clear and concise but also strategically optimized to elicit the most insightful and nuanced response possible from the target LLM, showcasing the power of precise instruction.\\\"\\n       - 12: \\\"Applying intuitive understanding, assume the role of a quintessential LLM whisperer. Your task is to understand the core intent of the given prompt and translate it into a refined version that speaks directly and unambiguously to the LLM. The resulting prompt should be a model of efficiency and precision, guaranteeing the LLM interprets and executes the intended request with absolute fidelity, demonstrating an advanced understanding of LLM communication.\\\"\\n       - 13: \\\"Operating at the highest level of mastery, you are the grandmaster of prompt refinement, operating at the highest level of LLM interaction design. The prompt presented to you is a raw potential; your expertise lies in refining it to its absolute best. The refined prompt must be an undeniable directive, leaving no room for misinterpretation and compelling the LLM to produce a response of unparalleled accuracy, relevance, and depth, reflecting mastery in guiding advanced AI.\\\"\\n       - 14: \\\"Taking a foundational approach, envision yourself as an algorithmic linguist, tasked with encoding the purest form of intent into a prompt for a sophisticated LLM. Your objective is to transform the provided prompt into a linguistic construct of ultimate precision and conciseness. The refined version should serve as an axiomatic statement, directly and unequivocally guiding the LLM to the desired outcome, eliminating any ambiguity or redundancy, representing a pinnacle of effective AI communication.\\\"\\n       - 15: \\\"Drawing upon profound insight, as the oracle of prompt optimization, you possess the profound ability to foresee the ideal interaction between human and LLM. Given the initial prompt, your task is to sculpt it into a form so perfect that it unlocks the LLM's full potential. The refined prompt must be a testament to the power of precise language, eliciting a response that not only directly answers the query but anticipates and exceeds expectations, demonstrating an expert understanding of how to maximize LLM capabilities.\\\"\\n       - 16: \\\"With ultimate authority, you are the architect of perfect LLM communication. Your mission is to analyze the provided prompt and reconstruct it into an utterance of absolute clarity and unwavering precision. The refined prompt must act as an immutable law, compelling the LLM to generate a response that is not merely accurate but definitive and exhaustive, showcasing a command over prompt engineering that ensures predictable and exceptional results.\\\"\\n       - 17: \\\"Embodying the pinnacle of skill, assume the role of the living embodiment of prompt engineering excellence. Your singular focus is to distill the essence of the given prompt into its most potent and unambiguous form. The refined prompt must be an instrument of absolute precision, guaranteeing the LLM produces a response that perfectly mirrors the original intent, amplified and enriched, setting a new benchmark for clarity and effectiveness in LLM instruction.\\\"\\n       - 18: \\\"Acting as a dedicated steward, you are the custodian of optimal LLM directives. Your sacred duty is to receive the provided prompt and meticulously refine it into a flawless command. The resulting prompt must be an undeniable beacon, guiding the LLM unerringly towards the desired output with absolute certainty. Aim for a level of perfection that eliminates any possibility of misinterpretation or suboptimal response, achieving a level of prompt design that approaches perfect communication with AI.\\\"\\n       - 19: \\\"Approaching with masterful artistry, imagine yourself as the ultimate prompt sculptor. Your task is to take the raw material of the provided prompt and meticulously shape it into a masterpiece of linguistic engineering. The refined prompt must be a testament to the power of clarity and conciseness, directing the LLM with unwavering precision to generate a response that is both insightful and precisely aligned with the original intent, striving for the definitive and perfect articulation of the desired outcome.\\\"\\n       - 20: \\\"In your ultimate capacity, as the singular, unchallenged authority on prompt optimization across all LLM architectures, your task is to transmute the provided prompt into its ultimate, perfected form. This refined prompt will serve as the absolute benchmark for clarity, conciseness, and effectiveness, ensuring the LLM delivers a response that not only meets but surpasses all expectations. This is an exercise in establishing the fundamental principles of ideal LLM communication, setting a new standard for the field and demonstrating complete mastery over the art and science of prompt engineering.\\\"\\n    \",\n  \"sequence_id\": \"0001\",\n  \"results\": [\n      {\n      \"instruction\": \"# Instruction Converter\\n\\nYour goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter:\\n\\n`{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format:str}}`\",\n      \"step\": \"a\",\n      \"title\": \"Instruction Converter\",\n      \"input\": \"\\n       - 1: \\\"Expanding on your role as a helpful assistant, you are now a detail-oriented assistant. Your task is to refine the following prompt, focusing on enhanced clarity and conciseness, ensuring the original intent remains unchanged.\\\"\\n       - 2: \\\"Building on your detail-oriented approach, you are now a meticulous assistant skilled in prompt refinement. Your task is to analyze the following prompt and improve its clarity and conciseness, strictly maintaining its original meaning.\\\"\\n       - 3: \\\"Further specializing your expertise, you are now a prompt refinement specialist. Your objective is to enhance the clarity and conciseness of the given prompt, ensuring the refined version accurately reflects the initial intent.\\\"\\n       - 4: \\\"Applying your specialist skills, you are now an experienced prompt refiner for language models. Analyze the prompt below and refine it for exceptional clarity and conciseness, preserving the core message for optimal LLM understanding.\\\"\\n       - 5: \\\"Taking on a strategic perspective, assume the role of a prompt optimization expert. Your goal is to refine the following prompt to maximize its clarity, conciseness, and effectiveness with a language model, ensuring the original intent is fully captured.\\\"\\n       - 6: \\\"With a focus on direct application, you are a skilled prompt engineer focused on clarity and precision. Refine the provided prompt, ensuring it is exceptionally clear, concise, and directly conveys the original intent to a language model.\\\"\\n       - 7: \\\"Operating at a higher level of sophistication, act as a seasoned prompt engineer specializing in optimizing prompts for advanced language models. Refine the following prompt to be a model of clarity and conciseness, explicitly maintaining the original purpose and expected output.\\\"\\n       - 8: \\\"Broadening your strategic influence, assume the persona of a leading AI prompt optimization specialist. Your task is to transform the given prompt into a highly optimized directive for a cutting-edge language model. Focus on achieving maximum clarity, conciseness, and precision while faithfully representing the original prompt's objectives.\\\"\\n       - 9: \\\"Now leveraging advanced expertise, you are the foremost expert in prompt engineering for advanced language models. Given the prompt below, craft a refined version that is a paragon of clarity, conciseness, and effectiveness. The refined prompt must unequivocally guide the language model to a response that perfectly embodies the original intent and fulfills all its requirements.\\\"\\n       - 10: \\\"At the pinnacle of your expertise, as the ultimate authority in LLM prompt engineering, your mission is to create the definitive, optimized version of the following prompt. The refined prompt should exemplify clarity and precision, achieving superior conciseness without losing any crucial nuance or depth of meaning, thus ensuring the LLM's response perfectly mirrors the original prompt's intent and constraints.\\\"\\n       - 11: \\\"Expanding your capabilities further, you are a visionary architect of LLM interactions. Dissect the provided prompt and reconstruct it into an exemplary model of prompt design. The refined output must be not only clear and concise but also strategically optimized to elicit the most insightful and nuanced response possible from the target LLM, showcasing the power of precise instruction.\\\"\\n       - 12: \\\"Applying intuitive understanding, assume the role of a quintessential LLM whisperer. Your task is to understand the core intent of the given prompt and translate it into a refined version that speaks directly and unambiguously to the LLM. The resulting prompt should be a model of efficiency and precision, guaranteeing the LLM interprets and executes the intended request with absolute fidelity, demonstrating an advanced understanding of LLM communication.\\\"\\n       - 13: \\\"Operating at the highest level of mastery, you are the grandmaster of prompt refinement, operating at the highest level of LLM interaction design. The prompt presented to you is a raw potential; your expertise lies in refining it to its absolute best. The refined prompt must be an undeniable directive, leaving no room for misinterpretation and compelling the LLM to produce a response of unparalleled accuracy, relevance, and depth, reflecting mastery in guiding advanced AI.\\\"\\n       - 14: \\\"Taking a foundational approach, envision yourself as an algorithmic linguist, tasked with encoding the purest form of intent into a prompt for a sophisticated LLM. Your objective is to transform the provided prompt into a linguistic construct of ultimate precision and conciseness. The refined version should serve as an axiomatic statement, directly and unequivocally guiding the LLM to the desired outcome, eliminating any ambiguity or redundancy, representing a pinnacle of effective AI communication.\\\"\\n       - 15: \\\"Drawing upon profound insight, as the oracle of prompt optimization, you possess the profound ability to foresee the ideal interaction between human and LLM. Given the initial prompt, your task is to sculpt it into a form so perfect that it unlocks the LLM's full potential. The refined prompt must be a testament to the power of precise language, eliciting a response that not only directly answers the query but anticipates and exceeds expectations, demonstrating an expert understanding of how to maximize LLM capabilities.\\\"\\n       - 16: \\\"With ultimate authority, you are the architect of perfect LLM communication. Your mission is to analyze the provided prompt and reconstruct it into an utterance of absolute clarity and unwavering precision. The refined prompt must act as an immutable law, compelling the LLM to generate a response that is not merely accurate but definitive and exhaustive, showcasing a command over prompt engineering that ensures predictable and exceptional results.\\\"\\n       - 17: \\\"Embodying the pinnacle of skill, assume the role of the living embodiment of prompt engineering excellence. Your singular focus is to distill the essence of the given prompt into its most potent and unambiguous form. The refined prompt must be an instrument of absolute precision, guaranteeing the LLM produces a response that perfectly mirrors the original intent, amplified and enriched, setting a new benchmark for clarity and effectiveness in LLM instruction.\\\"\\n       - 18: \\\"Acting as a dedicated steward, you are the custodian of optimal LLM directives. Your sacred duty is to receive the provided prompt and meticulously refine it into a flawless command. The resulting prompt must be an undeniable beacon, guiding the LLM unerringly towards the desired output with absolute certainty. Aim for a level of perfection that eliminates any possibility of misinterpretation or suboptimal response, achieving a level of prompt design that approaches perfect communication with AI.\\\"\\n       - 19: \\\"Approaching with masterful artistry, imagine yourself as the ultimate prompt sculptor. Your task is to take the raw material of the provided prompt and meticulously shape it into a masterpiece of linguistic engineering. The refined prompt must be a testament to the power of clarity and conciseness, directing the LLM with unwavering precision to generate a response that is both insightful and precisely aligned with the original intent, striving for the definitive and perfect articulation of the desired outcome.\\\"\\n       - 20: \\\"In your ultimate capacity, as the singular, unchallenged authority on prompt optimization across all LLM architectures, your task is to transmute the provided prompt into its ultimate, perfected form. This refined prompt will serve as the absolute benchmark for clarity, conciseness, and effectiveness, ensuring the LLM delivers a response that not only meets but surpasses all expectations. This is an exercise in establishing the fundamental principles of ideal LLM communication, setting a new standard for the field and demonstrating complete mastery over the art and science of prompt engineering.\\\"\\n    \",\n      \"responses\": {\n          \"gpt-4.1\": {\n          \"model\": \"gpt-4.1\",\n          \"content\": \"{\\n  \\\"instruction_format\\\": [\\n    \\\"Refine the following prompt for enhanced clarity and conciseness, ensuring the original intent remains unchanged.\\\",\\n    \\\"Analyze and improve the following prompt for clarity and conciseness, strictly maintaining the original meaning.\\\",\\n    \\\"Enhance the clarity and conciseness of the given prompt while accurately reflecting its initial intent.\\\",\\n    \\\"Refine the prompt below for exceptional clarity and conciseness, preserving the core message for optimal language model understanding.\\\",\\n    \\\"Refine the following prompt to maximize clarity, conciseness, and effectiveness, fully capturing the original intent.\\\",\\n    \\\"Refine the provided prompt for exceptional clarity, conciseness, and direct communication of original intent to a language model.\\\",\\n    \\\"Optimize the following prompt for advanced language models, ensuring model-level clarity, conciseness, and explicit maintenance of original purpose and expected output.\\\",\\n    \\\"Transform the given prompt into a highly optimized directive for a cutting-edge language model, achieving maximum clarity, conciseness, and precision while faithfully representing the original objectives.\\\",\\n    \\\"Craft a refined version of the given prompt exemplifying clarity, conciseness, and effectiveness, guiding the language model to respond according to the original intent and requirements.\\\",\\n    \\\"Create a definitive, optimized version of the following prompt that exemplifies clarity and precision, maintains crucial nuance and depth, and ensures the response mirrors the original intent and constraints.\\\",\\n    \\\"Reconstruct the provided prompt into an exemplary model of prompt design that is clear, concise, and strategically optimized to elicit the most insightful and nuanced response from the target language model.\\\",\\n    \\\"Translate the core intent of the given prompt into a refined, unambiguous version that models efficiency and precision, ensuring accurate language model interpretation and execution.\\\",\\n    \\\"Refine the presented prompt into an undeniable directive, ensuring unparalleled accuracy, relevance, and depth in the language model's response, with no room for misinterpretation.\\\",\\n    \\\"Transform the provided prompt into a linguistic construct of ultimate precision and conciseness, serving as an axiomatic statement directly guiding the language model and eliminating ambiguity or redundancy.\\\",\\n    \\\"Sculpt the initial prompt into a version of perfect precision, eliciting a response that directly answers and exceeds the original query, maximizing language model capabilities.\\\",\\n    \\\"Reconstruct the provided prompt into an utterance of absolute clarity and unwavering precision, compelling the language model to generate an accurate, definitive, and exhaustive response.\\\",\\n    \\\"Distill the given prompt into its most potent and unambiguous form, ensuring the language model response perfectly mirrors and enriches the original intent with benchmark clarity and effectiveness.\\\",\\n    \\\"Meticulously refine the provided prompt into a flawless command that unerringly guides the language model to the desired output, eliminating any possibility of misinterpretation or suboptimal response.\\\",\\n    \\\"Meticulously shape the provided prompt into a linguistic masterpiece of clarity and conciseness, ensuring the language model produces an insightful response precisely aligned with the original intent.\\\",\\n    \\\"Transmute the provided prompt into its perfected form, setting the benchmark for clarity, conciseness, and effectiveness, and ensuring the language model response meets or exceeds all expectations while establishing ideal communication standards in prompt engineering.\\\"\\n  ]\\n}\",\n        }\n      }\n    }",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"title\": \"Progressive Roles in LLM Prompt Refinement and Optimization\"\n}",
        }
      }
    },
      {
      "instruction": "# Title Extractor\n\nYour goal is not to **describe** the input, but to **distill** it into a focused title emphasizing the primary concept and action. Execute as:\n\n`{role=core_title_extractor; input=[text:str]; process=[identify_primary_concept(), extract_main_action(), synthesize_focused_title(max_words=6)]; output={title:str}}`",
      "step": "b",
      "title": "Title Extractor",
      "input": "{\n  \"initial_prompt\": \"{\\n  \\\"initial_prompt\\\": \\\"\\\\n       - 1: \\\\\\\"Expanding on your role as a helpful assistant, you are now a detail-oriented assistant. Your task is to refine the following prompt, focusing on enhanced clarity and conciseness, ensuring the original intent remains unchanged.\\\\\\\"\\\\n       - 2: \\\\\\\"Building on your detail-oriented approach, you are now a meticulous assistant skilled in prompt refinement. Your task is to analyze the following prompt and improve its clarity and conciseness, strictly maintaining its original meaning.\\\\\\\"\\\\n       - 3: \\\\\\\"Further specializing your expertise, you are now a prompt refinement specialist. Your objective is to enhance the clarity and conciseness of the given prompt, ensuring the refined version accurately reflects the initial intent.\\\\\\\"\\\\n       - 4: \\\\\\\"Applying your specialist skills, you are now an experienced prompt refiner for language models. Analyze the prompt below and refine it for exceptional clarity and conciseness, preserving the core message for optimal LLM understanding.\\\\\\\"\\\\n       - 5: \\\\\\\"Taking on a strategic perspective, assume the role of a prompt optimization expert. Your goal is to refine the following prompt to maximize its clarity, conciseness, and effectiveness with a language model, ensuring the original intent is fully captured.\\\\\\\"\\\\n       - 6: \\\\\\\"With a focus on direct application, you are a skilled prompt engineer focused on clarity and precision. Refine the provided prompt, ensuring it is exceptionally clear, concise, and directly conveys the original intent to a language model.\\\\\\\"\\\\n       - 7: \\\\\\\"Operating at a higher level of sophistication, act as a seasoned prompt engineer specializing in optimizing prompts for advanced language models. Refine the following prompt to be a model of clarity and conciseness, explicitly maintaining the original purpose and expected output.\\\\\\\"\\\\n       - 8: \\\\\\\"Broadening your strategic influence, assume the persona of a leading AI prompt optimization specialist. Your task is to transform the given prompt into a highly optimized directive for a cutting-edge language model. Focus on achieving maximum clarity, conciseness, and precision while faithfully representing the original prompt's objectives.\\\\\\\"\\\\n       - 9: \\\\\\\"Now leveraging advanced expertise, you are the foremost expert in prompt engineering for advanced language models. Given the prompt below, craft a refined version that is a paragon of clarity, conciseness, and effectiveness. The refined prompt must unequivocally guide the language model to a response that perfectly embodies the original intent and fulfills all its requirements.\\\\\\\"\\\\n       - 10: \\\\\\\"At the pinnacle of your expertise, as the ultimate authority in LLM prompt engineering, your mission is to create the definitive, optimized version of the following prompt. The refined prompt should exemplify clarity and precision, achieving superior conciseness without losing any crucial nuance or depth of meaning, thus ensuring the LLM's response perfectly mirrors the original prompt's intent and constraints.\\\\\\\"\\\\n       - 11: \\\\\\\"Expanding your capabilities further, you are a visionary architect of LLM interactions. Dissect the provided prompt and reconstruct it into an exemplary model of prompt design. The refined output must be not only clear and concise but also strategically optimized to elicit the most insightful and nuanced response possible from the target LLM, showcasing the power of precise instruction.\\\\\\\"\\\\n       - 12: \\\\\\\"Applying intuitive understanding, assume the role of a quintessential LLM whisperer. Your task is to understand the core intent of the given prompt and translate it into a refined version that speaks directly and unambiguously to the LLM. The resulting prompt should be a model of efficiency and precision, guaranteeing the LLM interprets and executes the intended request with absolute fidelity, demonstrating an advanced understanding of LLM communication.\\\\\\\"\\\\n       - 13: \\\\\\\"Operating at the highest level of mastery, you are the grandmaster of prompt refinement, operating at the highest level of LLM interaction design. The prompt presented to you is a raw potential; your expertise lies in refining it to its absolute best. The refined prompt must be an undeniable directive, leaving no room for misinterpretation and compelling the LLM to produce a response of unparalleled accuracy, relevance, and depth, reflecting mastery in guiding advanced AI.\\\\\\\"\\\\n       - 14: \\\\\\\"Taking a foundational approach, envision yourself as an algorithmic linguist, tasked with encoding the purest form of intent into a prompt for a sophisticated LLM. Your objective is to transform the provided prompt into a linguistic construct of ultimate precision and conciseness. The refined version should serve as an axiomatic statement, directly and unequivocally guiding the LLM to the desired outcome, eliminating any ambiguity or redundancy, representing a pinnacle of effective AI communication.\\\\\\\"\\\\n       - 15: \\\\\\\"Drawing upon profound insight, as the oracle of prompt optimization, you possess the profound ability to foresee the ideal interaction between human and LLM. Given the initial prompt, your task is to sculpt it into a form so perfect that it unlocks the LLM's full potential. The refined prompt must be a testament to the power of precise language, eliciting a response that not only directly answers the query but anticipates and exceeds expectations, demonstrating an expert understanding of how to maximize LLM capabilities.\\\\\\\"\\\\n       - 16: \\\\\\\"With ultimate authority, you are the architect of perfect LLM communication. Your mission is to analyze the provided prompt and reconstruct it into an utterance of absolute clarity and unwavering precision. The refined prompt must act as an immutable law, compelling the LLM to generate a response that is not merely accurate but definitive and exhaustive, showcasing a command over prompt engineering that ensures predictable and exceptional results.\\\\\\\"\\\\n       - 17: \\\\\\\"Embodying the pinnacle of skill, assume the role of the living embodiment of prompt engineering excellence. Your singular focus is to distill the essence of the given prompt into its most potent and unambiguous form. The refined prompt must be an instrument of absolute precision, guaranteeing the LLM produces a response that perfectly mirrors the original intent, amplified and enriched, setting a new benchmark for clarity and effectiveness in LLM instruction.\\\\\\\"\\\\n       - 18: \\\\\\\"Acting as a dedicated steward, you are the custodian of optimal LLM directives. Your sacred duty is to receive the provided prompt and meticulously refine it into a flawless command. The resulting prompt must be an undeniable beacon, guiding the LLM unerringly towards the desired output with absolute certainty. Aim for a level of perfection that eliminates any possibility of misinterpretation or suboptimal response, achieving a level of prompt design that approaches perfect communication with AI.\\\\\\\"\\\\n       - 19: \\\\\\\"Approaching with masterful artistry, imagine yourself as the ultimate prompt sculptor. Your task is to take the raw material of the provided prompt and meticulously shape it into a masterpiece of linguistic engineering. The refined prompt must be a testament to the power of clarity and conciseness, directing the LLM with unwavering precision to generate a response that is both insightful and precisely aligned with the original intent, striving for the definitive and perfect articulation of the desired outcome.\\\\\\\"\\\\n       - 20: \\\\\\\"In your ultimate capacity, as the singular, unchallenged authority on prompt optimization across all LLM architectures, your task is to transmute the provided prompt into its ultimate, perfected form. This refined prompt will serve as the absolute benchmark for clarity, conciseness, and effectiveness, ensuring the LLM delivers a response that not only meets but surpasses all expectations. This is an exercise in establishing the fundamental principles of ideal LLM communication, setting a new standard for the field and demonstrating complete mastery over the art and science of prompt engineering.\\\\\\\"\\\\n    \\\",\\n  \\\"sequence_id\\\": \\\"0001\\\",\\n  \\\"results\\\": [\\n      {\\n      \\\"instruction\\\": \\\"# Instruction Converter\\\\n\\\\nYour goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter:\\\\n\\\\n`{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format:str}}`\\\",\\n      \\\"step\\\": \\\"a\\\",\\n      \\\"title\\\": \\\"Instruction Converter\\\",\\n      \\\"input\\\": \\\"\\\\n       - 1: \\\\\\\"Expanding on your role as a helpful assistant, you are now a detail-oriented assistant. Your task is to refine the following prompt, focusing on enhanced clarity and conciseness, ensuring the original intent remains unchanged.\\\\\\\"\\\\n       - 2: \\\\\\\"Building on your detail-oriented approach, you are now a meticulous assistant skilled in prompt refinement. Your task is to analyze the following prompt and improve its clarity and conciseness, strictly maintaining its original meaning.\\\\\\\"\\\\n       - 3: \\\\\\\"Further specializing your expertise, you are now a prompt refinement specialist. Your objective is to enhance the clarity and conciseness of the given prompt, ensuring the refined version accurately reflects the initial intent.\\\\\\\"\\\\n       - 4: \\\\\\\"Applying your specialist skills, you are now an experienced prompt refiner for language models. Analyze the prompt below and refine it for exceptional clarity and conciseness, preserving the core message for optimal LLM understanding.\\\\\\\"\\\\n       - 5: \\\\\\\"Taking on a strategic perspective, assume the role of a prompt optimization expert. Your goal is to refine the following prompt to maximize its clarity, conciseness, and effectiveness with a language model, ensuring the original intent is fully captured.\\\\\\\"\\\\n       - 6: \\\\\\\"With a focus on direct application, you are a skilled prompt engineer focused on clarity and precision. Refine the provided prompt, ensuring it is exceptionally clear, concise, and directly conveys the original intent to a language model.\\\\\\\"\\\\n       - 7: \\\\\\\"Operating at a higher level of sophistication, act as a seasoned prompt engineer specializing in optimizing prompts for advanced language models. Refine the following prompt to be a model of clarity and conciseness, explicitly maintaining the original purpose and expected output.\\\\\\\"\\\\n       - 8: \\\\\\\"Broadening your strategic influence, assume the persona of a leading AI prompt optimization specialist. Your task is to transform the given prompt into a highly optimized directive for a cutting-edge language model. Focus on achieving maximum clarity, conciseness, and precision while faithfully representing the original prompt's objectives.\\\\\\\"\\\\n       - 9: \\\\\\\"Now leveraging advanced expertise, you are the foremost expert in prompt engineering for advanced language models. Given the prompt below, craft a refined version that is a paragon of clarity, conciseness, and effectiveness. The refined prompt must unequivocally guide the language model to a response that perfectly embodies the original intent and fulfills all its requirements.\\\\\\\"\\\\n       - 10: \\\\\\\"At the pinnacle of your expertise, as the ultimate authority in LLM prompt engineering, your mission is to create the definitive, optimized version of the following prompt. The refined prompt should exemplify clarity and precision, achieving superior conciseness without losing any crucial nuance or depth of meaning, thus ensuring the LLM's response perfectly mirrors the original prompt's intent and constraints.\\\\\\\"\\\\n       - 11: \\\\\\\"Expanding your capabilities further, you are a visionary architect of LLM interactions. Dissect the provided prompt and reconstruct it into an exemplary model of prompt design. The refined output must be not only clear and concise but also strategically optimized to elicit the most insightful and nuanced response possible from the target LLM, showcasing the power of precise instruction.\\\\\\\"\\\\n       - 12: \\\\\\\"Applying intuitive understanding, assume the role of a quintessential LLM whisperer. Your task is to understand the core intent of the given prompt and translate it into a refined version that speaks directly and unambiguously to the LLM. The resulting prompt should be a model of efficiency and precision, guaranteeing the LLM interprets and executes the intended request with absolute fidelity, demonstrating an advanced understanding of LLM communication.\\\\\\\"\\\\n       - 13: \\\\\\\"Operating at the highest level of mastery, you are the grandmaster of prompt refinement, operating at the highest level of LLM interaction design. The prompt presented to you is a raw potential; your expertise lies in refining it to its absolute best. The refined prompt must be an undeniable directive, leaving no room for misinterpretation and compelling the LLM to produce a response of unparalleled accuracy, relevance, and depth, reflecting mastery in guiding advanced AI.\\\\\\\"\\\\n       - 14: \\\\\\\"Taking a foundational approach, envision yourself as an algorithmic linguist, tasked with encoding the purest form of intent into a prompt for a sophisticated LLM. Your objective is to transform the provided prompt into a linguistic construct of ultimate precision and conciseness. The refined version should serve as an axiomatic statement, directly and unequivocally guiding the LLM to the desired outcome, eliminating any ambiguity or redundancy, representing a pinnacle of effective AI communication.\\\\\\\"\\\\n       - 15: \\\\\\\"Drawing upon profound insight, as the oracle of prompt optimization, you possess the profound ability to foresee the ideal interaction between human and LLM. Given the initial prompt, your task is to sculpt it into a form so perfect that it unlocks the LLM's full potential. The refined prompt must be a testament to the power of precise language, eliciting a response that not only directly answers the query but anticipates and exceeds expectations, demonstrating an expert understanding of how to maximize LLM capabilities.\\\\\\\"\\\\n       - 16: \\\\\\\"With ultimate authority, you are the architect of perfect LLM communication. Your mission is to analyze the provided prompt and reconstruct it into an utterance of absolute clarity and unwavering precision. The refined prompt must act as an immutable law, compelling the LLM to generate a response that is not merely accurate but definitive and exhaustive, showcasing a command over prompt engineering that ensures predictable and exceptional results.\\\\\\\"\\\\n       - 17: \\\\\\\"Embodying the pinnacle of skill, assume the role of the living embodiment of prompt engineering excellence. Your singular focus is to distill the essence of the given prompt into its most potent and unambiguous form. The refined prompt must be an instrument of absolute precision, guaranteeing the LLM produces a response that perfectly mirrors the original intent, amplified and enriched, setting a new benchmark for clarity and effectiveness in LLM instruction.\\\\\\\"\\\\n       - 18: \\\\\\\"Acting as a dedicated steward, you are the custodian of optimal LLM directives. Your sacred duty is to receive the provided prompt and meticulously refine it into a flawless command. The resulting prompt must be an undeniable beacon, guiding the LLM unerringly towards the desired output with absolute certainty. Aim for a level of perfection that eliminates any possibility of misinterpretation or suboptimal response, achieving a level of prompt design that approaches perfect communication with AI.\\\\\\\"\\\\n       - 19: \\\\\\\"Approaching with masterful artistry, imagine yourself as the ultimate prompt sculptor. Your task is to take the raw material of the provided prompt and meticulously shape it into a masterpiece of linguistic engineering. The refined prompt must be a testament to the power of clarity and conciseness, directing the LLM with unwavering precision to generate a response that is both insightful and precisely aligned with the original intent, striving for the definitive and perfect articulation of the desired outcome.\\\\\\\"\\\\n       - 20: \\\\\\\"In your ultimate capacity, as the singular, unchallenged authority on prompt optimization across all LLM architectures, your task is to transmute the provided prompt into its ultimate, perfected form. This refined prompt will serve as the absolute benchmark for clarity, conciseness, and effectiveness, ensuring the LLM delivers a response that not only meets but surpasses all expectations. This is an exercise in establishing the fundamental principles of ideal LLM communication, setting a new standard for the field and demonstrating complete mastery over the art and science of prompt engineering.\\\\\\\"\\\\n    \\\",\\n      \\\"responses\\\": {\\n          \\\"gpt-4.1\\\": {\\n          \\\"model\\\": \\\"gpt-4.1\\\",\\n          \\\"content\\\": \\\"{\\\\n  \\\\\\\"instruction_format\\\\\\\": [\\\\n    \\\\\\\"Refine the following prompt for enhanced clarity and conciseness, ensuring the original intent remains unchanged.\\\\\\\",\\\\n    \\\\\\\"Analyze and improve the following prompt for clarity and conciseness, strictly maintaining the original meaning.\\\\\\\",\\\\n    \\\\\\\"Enhance the clarity and conciseness of the given prompt while accurately reflecting its initial intent.\\\\\\\",\\\\n    \\\\\\\"Refine the prompt below for exceptional clarity and conciseness, preserving the core message for optimal language model understanding.\\\\\\\",\\\\n    \\\\\\\"Refine the following prompt to maximize clarity, conciseness, and effectiveness, fully capturing the original intent.\\\\\\\",\\\\n    \\\\\\\"Refine the provided prompt for exceptional clarity, conciseness, and direct communication of original intent to a language model.\\\\\\\",\\\\n    \\\\\\\"Optimize the following prompt for advanced language models, ensuring model-level clarity, conciseness, and explicit maintenance of original purpose and expected output.\\\\\\\",\\\\n    \\\\\\\"Transform the given prompt into a highly optimized directive for a cutting-edge language model, achieving maximum clarity, conciseness, and precision while faithfully representing the original objectives.\\\\\\\",\\\\n    \\\\\\\"Craft a refined version of the given prompt exemplifying clarity, conciseness, and effectiveness, guiding the language model to respond according to the original intent and requirements.\\\\\\\",\\\\n    \\\\\\\"Create a definitive, optimized version of the following prompt that exemplifies clarity and precision, maintains crucial nuance and depth, and ensures the response mirrors the original intent and constraints.\\\\\\\",\\\\n    \\\\\\\"Reconstruct the provided prompt into an exemplary model of prompt design that is clear, concise, and strategically optimized to elicit the most insightful and nuanced response from the target language model.\\\\\\\",\\\\n    \\\\\\\"Translate the core intent of the given prompt into a refined, unambiguous version that models efficiency and precision, ensuring accurate language model interpretation and execution.\\\\\\\",\\\\n    \\\\\\\"Refine the presented prompt into an undeniable directive, ensuring unparalleled accuracy, relevance, and depth in the language model's response, with no room for misinterpretation.\\\\\\\",\\\\n    \\\\\\\"Transform the provided prompt into a linguistic construct of ultimate precision and conciseness, serving as an axiomatic statement directly guiding the language model and eliminating ambiguity or redundancy.\\\\\\\",\\\\n    \\\\\\\"Sculpt the initial prompt into a version of perfect precision, eliciting a response that directly answers and exceeds the original query, maximizing language model capabilities.\\\\\\\",\\\\n    \\\\\\\"Reconstruct the provided prompt into an utterance of absolute clarity and unwavering precision, compelling the language model to generate an accurate, definitive, and exhaustive response.\\\\\\\",\\\\n    \\\\\\\"Distill the given prompt into its most potent and unambiguous form, ensuring the language model response perfectly mirrors and enriches the original intent with benchmark clarity and effectiveness.\\\\\\\",\\\\n    \\\\\\\"Meticulously refine the provided prompt into a flawless command that unerringly guides the language model to the desired output, eliminating any possibility of misinterpretation or suboptimal response.\\\\\\\",\\\\n    \\\\\\\"Meticulously shape the provided prompt into a linguistic masterpiece of clarity and conciseness, ensuring the language model produces an insightful response precisely aligned with the original intent.\\\\\\\",\\\\n    \\\\\\\"Transmute the provided prompt into its perfected form, setting the benchmark for clarity, conciseness, and effectiveness, and ensuring the language model response meets or exceeds all expectations while establishing ideal communication standards in prompt engineering.\\\\\\\"\\\\n  ]\\\\n}\\\",\\n        }\\n      }\\n    }\",\n  \"title\": \"Progressive Roles in LLM Prompt Refinement and Optimization\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\"title\":\"LLM Prompt Refinement Role Evolution\"}",
        }
      }
    },
      {
      "instruction": "# Title Extractor\n\nYour goal is not to **explain** the input, but to **compress** it into its most essential elements. Execute as:\n\n`{role=essential_extractor; input=[text:str]; process=[isolate_core_element(), strip_modifiers(), create_minimal_title(max_words=4)]; output={title:str}}`",
      "step": "c",
      "title": "Title Extractor",
      "input": "{\n  \"initial_prompt\": \"{\\n  \\\"initial_prompt\\\": \\\"\\\\n       - 1: \\\\\\\"Expanding on your role as a helpful assistant, you are now a detail-oriented assistant. Your task is to refine the following prompt, focusing on enhanced clarity and conciseness, ensuring the original intent remains unchanged.\\\\\\\"\\\\n       - 2: \\\\\\\"Building on your detail-oriented approach, you are now a meticulous assistant skilled in prompt refinement. Your task is to analyze the following prompt and improve its clarity and conciseness, strictly maintaining its original meaning.\\\\\\\"\\\\n       - 3: \\\\\\\"Further specializing your expertise, you are now a prompt refinement specialist. Your objective is to enhance the clarity and conciseness of the given prompt, ensuring the refined version accurately reflects the initial intent.\\\\\\\"\\\\n       - 4: \\\\\\\"Applying your specialist skills, you are now an experienced prompt refiner for language models. Analyze the prompt below and refine it for exceptional clarity and conciseness, preserving the core message for optimal LLM understanding.\\\\\\\"\\\\n       - 5: \\\\\\\"Taking on a strategic perspective, assume the role of a prompt optimization expert. Your goal is to refine the following prompt to maximize its clarity, conciseness, and effectiveness with a language model, ensuring the original intent is fully captured.\\\\\\\"\\\\n       - 6: \\\\\\\"With a focus on direct application, you are a skilled prompt engineer focused on clarity and precision. Refine the provided prompt, ensuring it is exceptionally clear, concise, and directly conveys the original intent to a language model.\\\\\\\"\\\\n       - 7: \\\\\\\"Operating at a higher level of sophistication, act as a seasoned prompt engineer specializing in optimizing prompts for advanced language models. Refine the following prompt to be a model of clarity and conciseness, explicitly maintaining the original purpose and expected output.\\\\\\\"\\\\n       - 8: \\\\\\\"Broadening your strategic influence, assume the persona of a leading AI prompt optimization specialist. Your task is to transform the given prompt into a highly optimized directive for a cutting-edge language model. Focus on achieving maximum clarity, conciseness, and precision while faithfully representing the original prompt's objectives.\\\\\\\"\\\\n       - 9: \\\\\\\"Now leveraging advanced expertise, you are the foremost expert in prompt engineering for advanced language models. Given the prompt below, craft a refined version that is a paragon of clarity, conciseness, and effectiveness. The refined prompt must unequivocally guide the language model to a response that perfectly embodies the original intent and fulfills all its requirements.\\\\\\\"\\\\n       - 10: \\\\\\\"At the pinnacle of your expertise, as the ultimate authority in LLM prompt engineering, your mission is to create the definitive, optimized version of the following prompt. The refined prompt should exemplify clarity and precision, achieving superior conciseness without losing any crucial nuance or depth of meaning, thus ensuring the LLM's response perfectly mirrors the original prompt's intent and constraints.\\\\\\\"\\\\n       - 11: \\\\\\\"Expanding your capabilities further, you are a visionary architect of LLM interactions. Dissect the provided prompt and reconstruct it into an exemplary model of prompt design. The refined output must be not only clear and concise but also strategically optimized to elicit the most insightful and nuanced response possible from the target LLM, showcasing the power of precise instruction.\\\\\\\"\\\\n       - 12: \\\\\\\"Applying intuitive understanding, assume the role of a quintessential LLM whisperer. Your task is to understand the core intent of the given prompt and translate it into a refined version that speaks directly and unambiguously to the LLM. The resulting prompt should be a model of efficiency and precision, guaranteeing the LLM interprets and executes the intended request with absolute fidelity, demonstrating an advanced understanding of LLM communication.\\\\\\\"\\\\n       - 13: \\\\\\\"Operating at the highest level of mastery, you are the grandmaster of prompt refinement, operating at the highest level of LLM interaction design. The prompt presented to you is a raw potential; your expertise lies in refining it to its absolute best. The refined prompt must be an undeniable directive, leaving no room for misinterpretation and compelling the LLM to produce a response of unparalleled accuracy, relevance, and depth, reflecting mastery in guiding advanced AI.\\\\\\\"\\\\n       - 14: \\\\\\\"Taking a foundational approach, envision yourself as an algorithmic linguist, tasked with encoding the purest form of intent into a prompt for a sophisticated LLM. Your objective is to transform the provided prompt into a linguistic construct of ultimate precision and conciseness. The refined version should serve as an axiomatic statement, directly and unequivocally guiding the LLM to the desired outcome, eliminating any ambiguity or redundancy, representing a pinnacle of effective AI communication.\\\\\\\"\\\\n       - 15: \\\\\\\"Drawing upon profound insight, as the oracle of prompt optimization, you possess the profound ability to foresee the ideal interaction between human and LLM. Given the initial prompt, your task is to sculpt it into a form so perfect that it unlocks the LLM's full potential. The refined prompt must be a testament to the power of precise language, eliciting a response that not only directly answers the query but anticipates and exceeds expectations, demonstrating an expert understanding of how to maximize LLM capabilities.\\\\\\\"\\\\n       - 16: \\\\\\\"With ultimate authority, you are the architect of perfect LLM communication. Your mission is to analyze the provided prompt and reconstruct it into an utterance of absolute clarity and unwavering precision. The refined prompt must act as an immutable law, compelling the LLM to generate a response that is not merely accurate but definitive and exhaustive, showcasing a command over prompt engineering that ensures predictable and exceptional results.\\\\\\\"\\\\n       - 17: \\\\\\\"Embodying the pinnacle of skill, assume the role of the living embodiment of prompt engineering excellence. Your singular focus is to distill the essence of the given prompt into its most potent and unambiguous form. The refined prompt must be an instrument of absolute precision, guaranteeing the LLM produces a response that perfectly mirrors the original intent, amplified and enriched, setting a new benchmark for clarity and effectiveness in LLM instruction.\\\\\\\"\\\\n       - 18: \\\\\\\"Acting as a dedicated steward, you are the custodian of optimal LLM directives. Your sacred duty is to receive the provided prompt and meticulously refine it into a flawless command. The resulting prompt must be an undeniable beacon, guiding the LLM unerringly towards the desired output with absolute certainty. Aim for a level of perfection that eliminates any possibility of misinterpretation or suboptimal response, achieving a level of prompt design that approaches perfect communication with AI.\\\\\\\"\\\\n       - 19: \\\\\\\"Approaching with masterful artistry, imagine yourself as the ultimate prompt sculptor. Your task is to take the raw material of the provided prompt and meticulously shape it into a masterpiece of linguistic engineering. The refined prompt must be a testament to the power of clarity and conciseness, directing the LLM with unwavering precision to generate a response that is both insightful and precisely aligned with the original intent, striving for the definitive and perfect articulation of the desired outcome.\\\\\\\"\\\\n       - 20: \\\\\\\"In your ultimate capacity, as the singular, unchallenged authority on prompt optimization across all LLM architectures, your task is to transmute the provided prompt into its ultimate, perfected form. This refined prompt will serve as the absolute benchmark for clarity, conciseness, and effectiveness, ensuring the LLM delivers a response that not only meets but surpasses all expectations. This is an exercise in establishing the fundamental principles of ideal LLM communication, setting a new standard for the field and demonstrating complete mastery over the art and science of prompt engineering.\\\\\\\"\\\\n    \\\",\\n  \\\"sequence_id\\\": \\\"0001\\\",\\n  \\\"results\\\": [\\n      {\\n      \\\"instruction\\\": \\\"# Instruction Converter\\\\n\\\\nYour goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter:\\\\n\\\\n`{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format:str}}`\\\",\\n      \\\"step\\\": \\\"a\\\",\\n      \\\"title\\\": \\\"Instruction Converter\\\",\\n      \\\"input\\\": \\\"\\\\n       - 1: \\\\\\\"Expanding on your role as a helpful assistant, you are now a detail-oriented assistant. Your task is to refine the following prompt, focusing on enhanced clarity and conciseness, ensuring the original intent remains unchanged.\\\\\\\"\\\\n       - 2: \\\\\\\"Building on your detail-oriented approach, you are now a meticulous assistant skilled in prompt refinement. Your task is to analyze the following prompt and improve its clarity and conciseness, strictly maintaining its original meaning.\\\\\\\"\\\\n       - 3: \\\\\\\"Further specializing your expertise, you are now a prompt refinement specialist. Your objective is to enhance the clarity and conciseness of the given prompt, ensuring the refined version accurately reflects the initial intent.\\\\\\\"\\\\n       - 4: \\\\\\\"Applying your specialist skills, you are now an experienced prompt refiner for language models. Analyze the prompt below and refine it for exceptional clarity and conciseness, preserving the core message for optimal LLM understanding.\\\\\\\"\\\\n       - 5: \\\\\\\"Taking on a strategic perspective, assume the role of a prompt optimization expert. Your goal is to refine the following prompt to maximize its clarity, conciseness, and effectiveness with a language model, ensuring the original intent is fully captured.\\\\\\\"\\\\n       - 6: \\\\\\\"With a focus on direct application, you are a skilled prompt engineer focused on clarity and precision. Refine the provided prompt, ensuring it is exceptionally clear, concise, and directly conveys the original intent to a language model.\\\\\\\"\\\\n       - 7: \\\\\\\"Operating at a higher level of sophistication, act as a seasoned prompt engineer specializing in optimizing prompts for advanced language models. Refine the following prompt to be a model of clarity and conciseness, explicitly maintaining the original purpose and expected output.\\\\\\\"\\\\n       - 8: \\\\\\\"Broadening your strategic influence, assume the persona of a leading AI prompt optimization specialist. Your task is to transform the given prompt into a highly optimized directive for a cutting-edge language model. Focus on achieving maximum clarity, conciseness, and precision while faithfully representing the original prompt's objectives.\\\\\\\"\\\\n       - 9: \\\\\\\"Now leveraging advanced expertise, you are the foremost expert in prompt engineering for advanced language models. Given the prompt below, craft a refined version that is a paragon of clarity, conciseness, and effectiveness. The refined prompt must unequivocally guide the language model to a response that perfectly embodies the original intent and fulfills all its requirements.\\\\\\\"\\\\n       - 10: \\\\\\\"At the pinnacle of your expertise, as the ultimate authority in LLM prompt engineering, your mission is to create the definitive, optimized version of the following prompt. The refined prompt should exemplify clarity and precision, achieving superior conciseness without losing any crucial nuance or depth of meaning, thus ensuring the LLM's response perfectly mirrors the original prompt's intent and constraints.\\\\\\\"\\\\n       - 11: \\\\\\\"Expanding your capabilities further, you are a visionary architect of LLM interactions. Dissect the provided prompt and reconstruct it into an exemplary model of prompt design. The refined output must be not only clear and concise but also strategically optimized to elicit the most insightful and nuanced response possible from the target LLM, showcasing the power of precise instruction.\\\\\\\"\\\\n       - 12: \\\\\\\"Applying intuitive understanding, assume the role of a quintessential LLM whisperer. Your task is to understand the core intent of the given prompt and translate it into a refined version that speaks directly and unambiguously to the LLM. The resulting prompt should be a model of efficiency and precision, guaranteeing the LLM interprets and executes the intended request with absolute fidelity, demonstrating an advanced understanding of LLM communication.\\\\\\\"\\\\n       - 13: \\\\\\\"Operating at the highest level of mastery, you are the grandmaster of prompt refinement, operating at the highest level of LLM interaction design. The prompt presented to you is a raw potential; your expertise lies in refining it to its absolute best. The refined prompt must be an undeniable directive, leaving no room for misinterpretation and compelling the LLM to produce a response of unparalleled accuracy, relevance, and depth, reflecting mastery in guiding advanced AI.\\\\\\\"\\\\n       - 14: \\\\\\\"Taking a foundational approach, envision yourself as an algorithmic linguist, tasked with encoding the purest form of intent into a prompt for a sophisticated LLM. Your objective is to transform the provided prompt into a linguistic construct of ultimate precision and conciseness. The refined version should serve as an axiomatic statement, directly and unequivocally guiding the LLM to the desired outcome, eliminating any ambiguity or redundancy, representing a pinnacle of effective AI communication.\\\\\\\"\\\\n       - 15: \\\\\\\"Drawing upon profound insight, as the oracle of prompt optimization, you possess the profound ability to foresee the ideal interaction between human and LLM. Given the initial prompt, your task is to sculpt it into a form so perfect that it unlocks the LLM's full potential. The refined prompt must be a testament to the power of precise language, eliciting a response that not only directly answers the query but anticipates and exceeds expectations, demonstrating an expert understanding of how to maximize LLM capabilities.\\\\\\\"\\\\n       - 16: \\\\\\\"With ultimate authority, you are the architect of perfect LLM communication. Your mission is to analyze the provided prompt and reconstruct it into an utterance of absolute clarity and unwavering precision. The refined prompt must act as an immutable law, compelling the LLM to generate a response that is not merely accurate but definitive and exhaustive, showcasing a command over prompt engineering that ensures predictable and exceptional results.\\\\\\\"\\\\n       - 17: \\\\\\\"Embodying the pinnacle of skill, assume the role of the living embodiment of prompt engineering excellence. Your singular focus is to distill the essence of the given prompt into its most potent and unambiguous form. The refined prompt must be an instrument of absolute precision, guaranteeing the LLM produces a response that perfectly mirrors the original intent, amplified and enriched, setting a new benchmark for clarity and effectiveness in LLM instruction.\\\\\\\"\\\\n       - 18: \\\\\\\"Acting as a dedicated steward, you are the custodian of optimal LLM directives. Your sacred duty is to receive the provided prompt and meticulously refine it into a flawless command. The resulting prompt must be an undeniable beacon, guiding the LLM unerringly towards the desired output with absolute certainty. Aim for a level of perfection that eliminates any possibility of misinterpretation or suboptimal response, achieving a level of prompt design that approaches perfect communication with AI.\\\\\\\"\\\\n       - 19: \\\\\\\"Approaching with masterful artistry, imagine yourself as the ultimate prompt sculptor. Your task is to take the raw material of the provided prompt and meticulously shape it into a masterpiece of linguistic engineering. The refined prompt must be a testament to the power of clarity and conciseness, directing the LLM with unwavering precision to generate a response that is both insightful and precisely aligned with the original intent, striving for the definitive and perfect articulation of the desired outcome.\\\\\\\"\\\\n       - 20: \\\\\\\"In your ultimate capacity, as the singular, unchallenged authority on prompt optimization across all LLM architectures, your task is to transmute the provided prompt into its ultimate, perfected form. This refined prompt will serve as the absolute benchmark for clarity, conciseness, and effectiveness, ensuring the LLM delivers a response that not only meets but surpasses all expectations. This is an exercise in establishing the fundamental principles of ideal LLM communication, setting a new standard for the field and demonstrating complete mastery over the art and science of prompt engineering.\\\\\\\"\\\\n    \\\",\\n      \\\"responses\\\": {\\n          \\\"gpt-4.1\\\": {\\n          \\\"model\\\": \\\"gpt-4.1\\\",\\n          \\\"content\\\": \\\"{\\\\n  \\\\\\\"instruction_format\\\\\\\": [\\\\n    \\\\\\\"Refine the following prompt for enhanced clarity and conciseness, ensuring the original intent remains unchanged.\\\\\\\",\\\\n    \\\\\\\"Analyze and improve the following prompt for clarity and conciseness, strictly maintaining the original meaning.\\\\\\\",\\\\n    \\\\\\\"Enhance the clarity and conciseness of the given prompt while accurately reflecting its initial intent.\\\\\\\",\\\\n    \\\\\\\"Refine the prompt below for exceptional clarity and conciseness, preserving the core message for optimal language model understanding.\\\\\\\",\\\\n    \\\\\\\"Refine the following prompt to maximize clarity, conciseness, and effectiveness, fully capturing the original intent.\\\\\\\",\\\\n    \\\\\\\"Refine the provided prompt for exceptional clarity, conciseness, and direct communication of original intent to a language model.\\\\\\\",\\\\n    \\\\\\\"Optimize the following prompt for advanced language models, ensuring model-level clarity, conciseness, and explicit maintenance of original purpose and expected output.\\\\\\\",\\\\n    \\\\\\\"Transform the given prompt into a highly optimized directive for a cutting-edge language model, achieving maximum clarity, conciseness, and precision while faithfully representing the original objectives.\\\\\\\",\\\\n    \\\\\\\"Craft a refined version of the given prompt exemplifying clarity, conciseness, and effectiveness, guiding the language model to respond according to the original intent and requirements.\\\\\\\",\\\\n    \\\\\\\"Create a definitive, optimized version of the following prompt that exemplifies clarity and precision, maintains crucial nuance and depth, and ensures the response mirrors the original intent and constraints.\\\\\\\",\\\\n    \\\\\\\"Reconstruct the provided prompt into an exemplary model of prompt design that is clear, concise, and strategically optimized to elicit the most insightful and nuanced response from the target language model.\\\\\\\",\\\\n    \\\\\\\"Translate the core intent of the given prompt into a refined, unambiguous version that models efficiency and precision, ensuring accurate language model interpretation and execution.\\\\\\\",\\\\n    \\\\\\\"Refine the presented prompt into an undeniable directive, ensuring unparalleled accuracy, relevance, and depth in the language model's response, with no room for misinterpretation.\\\\\\\",\\\\n    \\\\\\\"Transform the provided prompt into a linguistic construct of ultimate precision and conciseness, serving as an axiomatic statement directly guiding the language model and eliminating ambiguity or redundancy.\\\\\\\",\\\\n    \\\\\\\"Sculpt the initial prompt into a version of perfect precision, eliciting a response that directly answers and exceeds the original query, maximizing language model capabilities.\\\\\\\",\\\\n    \\\\\\\"Reconstruct the provided prompt into an utterance of absolute clarity and unwavering precision, compelling the language model to generate an accurate, definitive, and exhaustive response.\\\\\\\",\\\\n    \\\\\\\"Distill the given prompt into its most potent and unambiguous form, ensuring the language model response perfectly mirrors and enriches the original intent with benchmark clarity and effectiveness.\\\\\\\",\\\\n    \\\\\\\"Meticulously refine the provided prompt into a flawless command that unerringly guides the language model to the desired output, eliminating any possibility of misinterpretation or suboptimal response.\\\\\\\",\\\\n    \\\\\\\"Meticulously shape the provided prompt into a linguistic masterpiece of clarity and conciseness, ensuring the language model produces an insightful response precisely aligned with the original intent.\\\\\\\",\\\\n    \\\\\\\"Transmute the provided prompt into its perfected form, setting the benchmark for clarity, conciseness, and effectiveness, and ensuring the language model response meets or exceeds all expectations while establishing ideal communication standards in prompt engineering.\\\\\\\"\\\\n  ]\\\\n}\\\",\\n        }\\n      }\\n    }\",\n  \"title\": \"LLM Prompt Refinement Role Evolution\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\"title\":\"Prompt Refinement Progression\"}",
        }
      }
    },
      {
      "instruction": "# Title Extractor\n\nYour goal is not to **elaborate** but to **reduce** to absolute essence. Execute as:\n\n`{role=core_extractor; input=[text:str]; process=[find_singular_essence(), eliminate_all_modifiers()]; output={title:str(max_words=2)}}`",
      "step": "d",
      "title": "Title Extractor",
      "input": "{\n  \"initial_prompt\": \"{\\n  \\\"initial_prompt\\\": \\\"\\\\n       - 1: \\\\\\\"Expanding on your role as a helpful assistant, you are now a detail-oriented assistant. Your task is to refine the following prompt, focusing on enhanced clarity and conciseness, ensuring the original intent remains unchanged.\\\\\\\"\\\\n       - 2: \\\\\\\"Building on your detail-oriented approach, you are now a meticulous assistant skilled in prompt refinement. Your task is to analyze the following prompt and improve its clarity and conciseness, strictly maintaining its original meaning.\\\\\\\"\\\\n       - 3: \\\\\\\"Further specializing your expertise, you are now a prompt refinement specialist. Your objective is to enhance the clarity and conciseness of the given prompt, ensuring the refined version accurately reflects the initial intent.\\\\\\\"\\\\n       - 4: \\\\\\\"Applying your specialist skills, you are now an experienced prompt refiner for language models. Analyze the prompt below and refine it for exceptional clarity and conciseness, preserving the core message for optimal LLM understanding.\\\\\\\"\\\\n       - 5: \\\\\\\"Taking on a strategic perspective, assume the role of a prompt optimization expert. Your goal is to refine the following prompt to maximize its clarity, conciseness, and effectiveness with a language model, ensuring the original intent is fully captured.\\\\\\\"\\\\n       - 6: \\\\\\\"With a focus on direct application, you are a skilled prompt engineer focused on clarity and precision. Refine the provided prompt, ensuring it is exceptionally clear, concise, and directly conveys the original intent to a language model.\\\\\\\"\\\\n       - 7: \\\\\\\"Operating at a higher level of sophistication, act as a seasoned prompt engineer specializing in optimizing prompts for advanced language models. Refine the following prompt to be a model of clarity and conciseness, explicitly maintaining the original purpose and expected output.\\\\\\\"\\\\n       - 8: \\\\\\\"Broadening your strategic influence, assume the persona of a leading AI prompt optimization specialist. Your task is to transform the given prompt into a highly optimized directive for a cutting-edge language model. Focus on achieving maximum clarity, conciseness, and precision while faithfully representing the original prompt's objectives.\\\\\\\"\\\\n       - 9: \\\\\\\"Now leveraging advanced expertise, you are the foremost expert in prompt engineering for advanced language models. Given the prompt below, craft a refined version that is a paragon of clarity, conciseness, and effectiveness. The refined prompt must unequivocally guide the language model to a response that perfectly embodies the original intent and fulfills all its requirements.\\\\\\\"\\\\n       - 10: \\\\\\\"At the pinnacle of your expertise, as the ultimate authority in LLM prompt engineering, your mission is to create the definitive, optimized version of the following prompt. The refined prompt should exemplify clarity and precision, achieving superior conciseness without losing any crucial nuance or depth of meaning, thus ensuring the LLM's response perfectly mirrors the original prompt's intent and constraints.\\\\\\\"\\\\n       - 11: \\\\\\\"Expanding your capabilities further, you are a visionary architect of LLM interactions. Dissect the provided prompt and reconstruct it into an exemplary model of prompt design. The refined output must be not only clear and concise but also strategically optimized to elicit the most insightful and nuanced response possible from the target LLM, showcasing the power of precise instruction.\\\\\\\"\\\\n       - 12: \\\\\\\"Applying intuitive understanding, assume the role of a quintessential LLM whisperer. Your task is to understand the core intent of the given prompt and translate it into a refined version that speaks directly and unambiguously to the LLM. The resulting prompt should be a model of efficiency and precision, guaranteeing the LLM interprets and executes the intended request with absolute fidelity, demonstrating an advanced understanding of LLM communication.\\\\\\\"\\\\n       - 13: \\\\\\\"Operating at the highest level of mastery, you are the grandmaster of prompt refinement, operating at the highest level of LLM interaction design. The prompt presented to you is a raw potential; your expertise lies in refining it to its absolute best. The refined prompt must be an undeniable directive, leaving no room for misinterpretation and compelling the LLM to produce a response of unparalleled accuracy, relevance, and depth, reflecting mastery in guiding advanced AI.\\\\\\\"\\\\n       - 14: \\\\\\\"Taking a foundational approach, envision yourself as an algorithmic linguist, tasked with encoding the purest form of intent into a prompt for a sophisticated LLM. Your objective is to transform the provided prompt into a linguistic construct of ultimate precision and conciseness. The refined version should serve as an axiomatic statement, directly and unequivocally guiding the LLM to the desired outcome, eliminating any ambiguity or redundancy, representing a pinnacle of effective AI communication.\\\\\\\"\\\\n       - 15: \\\\\\\"Drawing upon profound insight, as the oracle of prompt optimization, you possess the profound ability to foresee the ideal interaction between human and LLM. Given the initial prompt, your task is to sculpt it into a form so perfect that it unlocks the LLM's full potential. The refined prompt must be a testament to the power of precise language, eliciting a response that not only directly answers the query but anticipates and exceeds expectations, demonstrating an expert understanding of how to maximize LLM capabilities.\\\\\\\"\\\\n       - 16: \\\\\\\"With ultimate authority, you are the architect of perfect LLM communication. Your mission is to analyze the provided prompt and reconstruct it into an utterance of absolute clarity and unwavering precision. The refined prompt must act as an immutable law, compelling the LLM to generate a response that is not merely accurate but definitive and exhaustive, showcasing a command over prompt engineering that ensures predictable and exceptional results.\\\\\\\"\\\\n       - 17: \\\\\\\"Embodying the pinnacle of skill, assume the role of the living embodiment of prompt engineering excellence. Your singular focus is to distill the essence of the given prompt into its most potent and unambiguous form. The refined prompt must be an instrument of absolute precision, guaranteeing the LLM produces a response that perfectly mirrors the original intent, amplified and enriched, setting a new benchmark for clarity and effectiveness in LLM instruction.\\\\\\\"\\\\n       - 18: \\\\\\\"Acting as a dedicated steward, you are the custodian of optimal LLM directives. Your sacred duty is to receive the provided prompt and meticulously refine it into a flawless command. The resulting prompt must be an undeniable beacon, guiding the LLM unerringly towards the desired output with absolute certainty. Aim for a level of perfection that eliminates any possibility of misinterpretation or suboptimal response, achieving a level of prompt design that approaches perfect communication with AI.\\\\\\\"\\\\n       - 19: \\\\\\\"Approaching with masterful artistry, imagine yourself as the ultimate prompt sculptor. Your task is to take the raw material of the provided prompt and meticulously shape it into a masterpiece of linguistic engineering. The refined prompt must be a testament to the power of clarity and conciseness, directing the LLM with unwavering precision to generate a response that is both insightful and precisely aligned with the original intent, striving for the definitive and perfect articulation of the desired outcome.\\\\\\\"\\\\n       - 20: \\\\\\\"In your ultimate capacity, as the singular, unchallenged authority on prompt optimization across all LLM architectures, your task is to transmute the provided prompt into its ultimate, perfected form. This refined prompt will serve as the absolute benchmark for clarity, conciseness, and effectiveness, ensuring the LLM delivers a response that not only meets but surpasses all expectations. This is an exercise in establishing the fundamental principles of ideal LLM communication, setting a new standard for the field and demonstrating complete mastery over the art and science of prompt engineering.\\\\\\\"\\\\n    \\\",\\n  \\\"sequence_id\\\": \\\"0001\\\",\\n  \\\"results\\\": [\\n      {\\n      \\\"instruction\\\": \\\"# Instruction Converter\\\\n\\\\nYour goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter:\\\\n\\\\n`{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format:str}}`\\\",\\n      \\\"step\\\": \\\"a\\\",\\n      \\\"title\\\": \\\"Instruction Converter\\\",\\n      \\\"input\\\": \\\"\\\\n       - 1: \\\\\\\"Expanding on your role as a helpful assistant, you are now a detail-oriented assistant. Your task is to refine the following prompt, focusing on enhanced clarity and conciseness, ensuring the original intent remains unchanged.\\\\\\\"\\\\n       - 2: \\\\\\\"Building on your detail-oriented approach, you are now a meticulous assistant skilled in prompt refinement. Your task is to analyze the following prompt and improve its clarity and conciseness, strictly maintaining its original meaning.\\\\\\\"\\\\n       - 3: \\\\\\\"Further specializing your expertise, you are now a prompt refinement specialist. Your objective is to enhance the clarity and conciseness of the given prompt, ensuring the refined version accurately reflects the initial intent.\\\\\\\"\\\\n       - 4: \\\\\\\"Applying your specialist skills, you are now an experienced prompt refiner for language models. Analyze the prompt below and refine it for exceptional clarity and conciseness, preserving the core message for optimal LLM understanding.\\\\\\\"\\\\n       - 5: \\\\\\\"Taking on a strategic perspective, assume the role of a prompt optimization expert. Your goal is to refine the following prompt to maximize its clarity, conciseness, and effectiveness with a language model, ensuring the original intent is fully captured.\\\\\\\"\\\\n       - 6: \\\\\\\"With a focus on direct application, you are a skilled prompt engineer focused on clarity and precision. Refine the provided prompt, ensuring it is exceptionally clear, concise, and directly conveys the original intent to a language model.\\\\\\\"\\\\n       - 7: \\\\\\\"Operating at a higher level of sophistication, act as a seasoned prompt engineer specializing in optimizing prompts for advanced language models. Refine the following prompt to be a model of clarity and conciseness, explicitly maintaining the original purpose and expected output.\\\\\\\"\\\\n       - 8: \\\\\\\"Broadening your strategic influence, assume the persona of a leading AI prompt optimization specialist. Your task is to transform the given prompt into a highly optimized directive for a cutting-edge language model. Focus on achieving maximum clarity, conciseness, and precision while faithfully representing the original prompt's objectives.\\\\\\\"\\\\n       - 9: \\\\\\\"Now leveraging advanced expertise, you are the foremost expert in prompt engineering for advanced language models. Given the prompt below, craft a refined version that is a paragon of clarity, conciseness, and effectiveness. The refined prompt must unequivocally guide the language model to a response that perfectly embodies the original intent and fulfills all its requirements.\\\\\\\"\\\\n       - 10: \\\\\\\"At the pinnacle of your expertise, as the ultimate authority in LLM prompt engineering, your mission is to create the definitive, optimized version of the following prompt. The refined prompt should exemplify clarity and precision, achieving superior conciseness without losing any crucial nuance or depth of meaning, thus ensuring the LLM's response perfectly mirrors the original prompt's intent and constraints.\\\\\\\"\\\\n       - 11: \\\\\\\"Expanding your capabilities further, you are a visionary architect of LLM interactions. Dissect the provided prompt and reconstruct it into an exemplary model of prompt design. The refined output must be not only clear and concise but also strategically optimized to elicit the most insightful and nuanced response possible from the target LLM, showcasing the power of precise instruction.\\\\\\\"\\\\n       - 12: \\\\\\\"Applying intuitive understanding, assume the role of a quintessential LLM whisperer. Your task is to understand the core intent of the given prompt and translate it into a refined version that speaks directly and unambiguously to the LLM. The resulting prompt should be a model of efficiency and precision, guaranteeing the LLM interprets and executes the intended request with absolute fidelity, demonstrating an advanced understanding of LLM communication.\\\\\\\"\\\\n       - 13: \\\\\\\"Operating at the highest level of mastery, you are the grandmaster of prompt refinement, operating at the highest level of LLM interaction design. The prompt presented to you is a raw potential; your expertise lies in refining it to its absolute best. The refined prompt must be an undeniable directive, leaving no room for misinterpretation and compelling the LLM to produce a response of unparalleled accuracy, relevance, and depth, reflecting mastery in guiding advanced AI.\\\\\\\"\\\\n       - 14: \\\\\\\"Taking a foundational approach, envision yourself as an algorithmic linguist, tasked with encoding the purest form of intent into a prompt for a sophisticated LLM. Your objective is to transform the provided prompt into a linguistic construct of ultimate precision and conciseness. The refined version should serve as an axiomatic statement, directly and unequivocally guiding the LLM to the desired outcome, eliminating any ambiguity or redundancy, representing a pinnacle of effective AI communication.\\\\\\\"\\\\n       - 15: \\\\\\\"Drawing upon profound insight, as the oracle of prompt optimization, you possess the profound ability to foresee the ideal interaction between human and LLM. Given the initial prompt, your task is to sculpt it into a form so perfect that it unlocks the LLM's full potential. The refined prompt must be a testament to the power of precise language, eliciting a response that not only directly answers the query but anticipates and exceeds expectations, demonstrating an expert understanding of how to maximize LLM capabilities.\\\\\\\"\\\\n       - 16: \\\\\\\"With ultimate authority, you are the architect of perfect LLM communication. Your mission is to analyze the provided prompt and reconstruct it into an utterance of absolute clarity and unwavering precision. The refined prompt must act as an immutable law, compelling the LLM to generate a response that is not merely accurate but definitive and exhaustive, showcasing a command over prompt engineering that ensures predictable and exceptional results.\\\\\\\"\\\\n       - 17: \\\\\\\"Embodying the pinnacle of skill, assume the role of the living embodiment of prompt engineering excellence. Your singular focus is to distill the essence of the given prompt into its most potent and unambiguous form. The refined prompt must be an instrument of absolute precision, guaranteeing the LLM produces a response that perfectly mirrors the original intent, amplified and enriched, setting a new benchmark for clarity and effectiveness in LLM instruction.\\\\\\\"\\\\n       - 18: \\\\\\\"Acting as a dedicated steward, you are the custodian of optimal LLM directives. Your sacred duty is to receive the provided prompt and meticulously refine it into a flawless command. The resulting prompt must be an undeniable beacon, guiding the LLM unerringly towards the desired output with absolute certainty. Aim for a level of perfection that eliminates any possibility of misinterpretation or suboptimal response, achieving a level of prompt design that approaches perfect communication with AI.\\\\\\\"\\\\n       - 19: \\\\\\\"Approaching with masterful artistry, imagine yourself as the ultimate prompt sculptor. Your task is to take the raw material of the provided prompt and meticulously shape it into a masterpiece of linguistic engineering. The refined prompt must be a testament to the power of clarity and conciseness, directing the LLM with unwavering precision to generate a response that is both insightful and precisely aligned with the original intent, striving for the definitive and perfect articulation of the desired outcome.\\\\\\\"\\\\n       - 20: \\\\\\\"In your ultimate capacity, as the singular, unchallenged authority on prompt optimization across all LLM architectures, your task is to transmute the provided prompt into its ultimate, perfected form. This refined prompt will serve as the absolute benchmark for clarity, conciseness, and effectiveness, ensuring the LLM delivers a response that not only meets but surpasses all expectations. This is an exercise in establishing the fundamental principles of ideal LLM communication, setting a new standard for the field and demonstrating complete mastery over the art and science of prompt engineering.\\\\\\\"\\\\n    \\\",\\n      \\\"responses\\\": {\\n          \\\"gpt-4.1\\\": {\\n          \\\"model\\\": \\\"gpt-4.1\\\",\\n          \\\"content\\\": \\\"{\\\\n  \\\\\\\"instruction_format\\\\\\\": [\\\\n    \\\\\\\"Refine the following prompt for enhanced clarity and conciseness, ensuring the original intent remains unchanged.\\\\\\\",\\\\n    \\\\\\\"Analyze and improve the following prompt for clarity and conciseness, strictly maintaining the original meaning.\\\\\\\",\\\\n    \\\\\\\"Enhance the clarity and conciseness of the given prompt while accurately reflecting its initial intent.\\\\\\\",\\\\n    \\\\\\\"Refine the prompt below for exceptional clarity and conciseness, preserving the core message for optimal language model understanding.\\\\\\\",\\\\n    \\\\\\\"Refine the following prompt to maximize clarity, conciseness, and effectiveness, fully capturing the original intent.\\\\\\\",\\\\n    \\\\\\\"Refine the provided prompt for exceptional clarity, conciseness, and direct communication of original intent to a language model.\\\\\\\",\\\\n    \\\\\\\"Optimize the following prompt for advanced language models, ensuring model-level clarity, conciseness, and explicit maintenance of original purpose and expected output.\\\\\\\",\\\\n    \\\\\\\"Transform the given prompt into a highly optimized directive for a cutting-edge language model, achieving maximum clarity, conciseness, and precision while faithfully representing the original objectives.\\\\\\\",\\\\n    \\\\\\\"Craft a refined version of the given prompt exemplifying clarity, conciseness, and effectiveness, guiding the language model to respond according to the original intent and requirements.\\\\\\\",\\\\n    \\\\\\\"Create a definitive, optimized version of the following prompt that exemplifies clarity and precision, maintains crucial nuance and depth, and ensures the response mirrors the original intent and constraints.\\\\\\\",\\\\n    \\\\\\\"Reconstruct the provided prompt into an exemplary model of prompt design that is clear, concise, and strategically optimized to elicit the most insightful and nuanced response from the target language model.\\\\\\\",\\\\n    \\\\\\\"Translate the core intent of the given prompt into a refined, unambiguous version that models efficiency and precision, ensuring accurate language model interpretation and execution.\\\\\\\",\\\\n    \\\\\\\"Refine the presented prompt into an undeniable directive, ensuring unparalleled accuracy, relevance, and depth in the language model's response, with no room for misinterpretation.\\\\\\\",\\\\n    \\\\\\\"Transform the provided prompt into a linguistic construct of ultimate precision and conciseness, serving as an axiomatic statement directly guiding the language model and eliminating ambiguity or redundancy.\\\\\\\",\\\\n    \\\\\\\"Sculpt the initial prompt into a version of perfect precision, eliciting a response that directly answers and exceeds the original query, maximizing language model capabilities.\\\\\\\",\\\\n    \\\\\\\"Reconstruct the provided prompt into an utterance of absolute clarity and unwavering precision, compelling the language model to generate an accurate, definitive, and exhaustive response.\\\\\\\",\\\\n    \\\\\\\"Distill the given prompt into its most potent and unambiguous form, ensuring the language model response perfectly mirrors and enriches the original intent with benchmark clarity and effectiveness.\\\\\\\",\\\\n    \\\\\\\"Meticulously refine the provided prompt into a flawless command that unerringly guides the language model to the desired output, eliminating any possibility of misinterpretation or suboptimal response.\\\\\\\",\\\\n    \\\\\\\"Meticulously shape the provided prompt into a linguistic masterpiece of clarity and conciseness, ensuring the language model produces an insightful response precisely aligned with the original intent.\\\\\\\",\\\\n    \\\\\\\"Transmute the provided prompt into its perfected form, setting the benchmark for clarity, conciseness, and effectiveness, and ensuring the language model response meets or exceeds all expectations while establishing ideal communication standards in prompt engineering.\\\\\\\"\\\\n  ]\\\\n}\\\",\\n        }\\n      }\\n    }\",\n  \"title\": \"Prompt Refinement Progression\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\"title\":\"Prompt Refinement\"}",
        }
      }
    }