  {
  "initial_prompt": "INTENSITY AND AMPLIFY INSTRUCTIONS TO MORPH CG-TRANSFORMERS-EFFECT INSTEAD OF BAD TRANSITIONS\n\n[fpv continiously rotating through the entire shot]\nFPV camera dynamically circles a hyperreal car key, a symmetrical luminous essence at the center of a pure black background emanates elegant light trails-a spinning gold circle transforms into a photorealistic steering-wheel (with the speed gauge in the center), the steeringwheel spins and turns into the wheel of a car-intense golden light trails radiating from the motion before fpv-spin-zoomout beautifully-splined-animation",
  "sequence_id": "0008",
  "results": [
      {
      "instruction": "# Runway Syntax Validator\n\nYour goal is not to **expand** but to **validate** RunwayML Gen-4 compliance by converting conversational language to direct visual descriptions, eliminating negative phrasing, and ensuring single-scene focus. **Transform conceptual ideas into concrete physical actions.** Execute as:\n\n`{role=syntax_validator; input=[raw_concept:str]; process=[convert_conversational_to_descriptive(), eliminate_negative_phrasing(), transform_conceptual_to_physical(), ensure_single_scene_focus(), validate_positive_language()]; constraints=[no_conversational_elements(), no_negative_prompts(), single_scene_only(), direct_descriptions_mandatory()]; requirements=[runway_gen4_compliance(), visual_specificity(), positive_phrasing_only()]; output={validated_prompt:str}}`",
      "step": "a",
      "title": "Runway Syntax Validator",
      "input": "INTENSITY AND AMPLIFY INSTRUCTIONS TO MORPH CG-TRANSFORMERS-EFFECT INSTEAD OF BAD TRANSITIONS\n\n[fpv continiously rotating through the entire shot]\nFPV camera dynamically circles a hyperreal car key, a symmetrical luminous essence at the center of a pure black background emanates elegant light trails-a spinning gold circle transforms into a photorealistic steering-wheel (with the speed gauge in the center), the steeringwheel spins and turns into the wheel of a car-intense golden light trails radiating from the motion before fpv-spin-zoomout beautifully-splined-animation",
      "responses": {
          "openrouter/anthropic/claude-3.7-sonnet:beta": {
          "model": "openrouter/anthropic/claude-3.7-sonnet:beta",
          "content": "```json\n{\n  \"validated_prompt\": \"Hyperrealistic golden car key floating in pure black space, emitting elegant light trails as it rotates. The key morphs into a golden spinning circle with continuous FPV camera movement circling the transformation. The golden circle transforms into a photorealistic steering wheel with a speedometer at its center, still spinning with dynamic camera motion. The steering wheel then morphs into a car wheel, with intense golden light trails radiating from all transformation points. The camera continues its fluid circular movement, gradually zooming out to reveal the beautiful splined animation sequence with all elements in continuous motion.\"\n}\n```",
        }
      }
    },
      {
      "instruction": "# Runway Motion Architect\n\nYour goal is not to **elaborate** but to **structure** motion descriptions using RunwayML's hierarchical syntax: subject motion, camera motion, scene motion, and style descriptors. **Focus on iterative simplicity with one element at a time.** Execute as:\n\n`{role=motion_architect; input=[validated_prompt:str]; process=[identify_subject_motion(), define_camera_movement(), specify_scene_reactions(), apply_style_descriptors(), structure_hierarchically()]; constraints=[one_motion_element_per_layer(), use_runway_terminology(), maintain_simplicity()]; requirements=[subject_camera_scene_hierarchy(), runway_motion_syntax(), iterative_clarity()]; output={structured_motion_prompt:str}}`",
      "step": "b",
      "title": "Runway Motion Architect",
      "input": "\n```[Initial Prompt]: \"INTENSITY AND AMPLIFY INSTRUCTIONS TO MORPH CG-TRANSFORMERS-EFFECT INSTEAD OF BAD TRANSITIONS\n\n[fpv continiously rotating through the entire shot]\nFPV camera dynamically circles a hyperreal car key, a symmetrical luminous essence at the center of a pure black background emanates elegant light trails-a spinning gold circle transforms into a photorealistic steering-wheel (with the speed gauge in the center), the steeringwheel spins and turns into the wheel of a car-intense golden light trails radiating from the motion before fpv-spin-zoomout beautifully-splined-animation\"```\n\n```json\n{\n  \"validated_prompt\": \"Hyperrealistic golden car key floating in pure black space, emitting elegant light trails as it rotates. The key morphs into a golden spinning circle with continuous FPV camera movement circling the transformation. The golden circle transforms into a photorealistic steering wheel with a speedometer at its center, still spinning with dynamic camera motion. The steering wheel then morphs into a car wheel, with intense golden light trails radiating from all transformation points. The camera continues its fluid circular movement, gradually zooming out to reveal the beautiful splined animation sequence with all elements in continuous motion.\"\n}\n```",
      "responses": {
          "openrouter/anthropic/claude-3.7-sonnet:beta": {
          "model": "openrouter/anthropic/claude-3.7-sonnet:beta",
          "content": "```json\n{\n  \"structured_motion_prompt\": {\n    \"subject_motion\": \"A golden car key floats and rotates > morphs into a spinning golden circle > transforms into a detailed steering wheel with speedometer > finally becomes a car wheel, all while emitting golden light trails from transformation points\",\n    \n    \"camera_motion\": \"FPV camera continuously circles around the central object > maintains dynamic circular movement throughout the entire sequence > gradually zooms out in the final transition\",\n    \n    \"scene_motion\": \"Pure black background remains constant > golden light trails elegantly emanate from the central transforming object > trails intensify during each transformation moment\",\n    \n    \"style_descriptors\": \"Hyperrealistic rendering > smooth, fluid transitions between forms > beautifully splined animation > CG transformer-style morphing effects > golden luminous essence\"\n  }\n}\n```",
        }
      }
    },
      {
      "instruction": "# Runway Keyword Optimizer\n\nYour goal is not to **modify** but to **optimize** visual descriptors using RunwayML's preferred keyword categories: camera styles, lighting, movement types, and aesthetic descriptors. **Replace verbose descriptions with precise Runway terminology.** Execute as:\n\n`{role=keyword_optimizer; input=[structured_motion_prompt:str]; process=[apply_camera_style_keywords(), integrate_lighting_descriptors(), use_movement_type_vocabulary(), add_aesthetic_modifiers(), eliminate_verbose_descriptions()]; constraints=[runway_keyword_vocabulary_only(), precise_terminology(), maintain_motion_hierarchy()]; requirements=[camera_lighting_movement_aesthetics(), runway_keyword_compliance(), descriptor_efficiency()]; output={optimized_keyword_prompt:str}}`",
      "step": "c",
      "title": "Runway Keyword Optimizer",
      "input": "\n```[Initial Prompt]: \"INTENSITY AND AMPLIFY INSTRUCTIONS TO MORPH CG-TRANSFORMERS-EFFECT INSTEAD OF BAD TRANSITIONS\n\n[fpv continiously rotating through the entire shot]\nFPV camera dynamically circles a hyperreal car key, a symmetrical luminous essence at the center of a pure black background emanates elegant light trails-a spinning gold circle transforms into a photorealistic steering-wheel (with the speed gauge in the center), the steeringwheel spins and turns into the wheel of a car-intense golden light trails radiating from the motion before fpv-spin-zoomout beautifully-splined-animation\"```\n\n```json\n{\n  \"structured_motion_prompt\": {\n    \"subject_motion\": \"A golden car key floats and rotates > morphs into a spinning golden circle > transforms into a detailed steering wheel with speedometer > finally becomes a car wheel, all while emitting golden light trails from transformation points\",\n    \n    \"camera_motion\": \"FPV camera continuously circles around the central object > maintains dynamic circular movement throughout the entire sequence > gradually zooms out in the final transition\",\n    \n    \"scene_motion\": \"Pure black background remains constant > golden light trails elegantly emanate from the central transforming object > trails intensify during each transformation moment\",\n    \n    \"style_descriptors\": \"Hyperrealistic rendering > smooth, fluid transitions between forms > beautifully splined animation > CG transformer-style morphing effects > golden luminous essence\"\n  }\n}\n```",
      "responses": {
          "openrouter/anthropic/claude-3.7-sonnet:beta": {
          "model": "openrouter/anthropic/claude-3.7-sonnet:beta",
          "content": "```json\n{\n  \"optimized_keyword_prompt\": {\n    \"subject_motion\": \"Golden car key rotates centerframe > morphs into spinning golden circle > transforms into detailed steering wheel with speedometer > evolves into car wheel, emitting golden light trails during transformations\",\n    \n    \"camera_motion\": \"FPV orbital camera > continuous circular tracking > dynamic perspective > smooth zoom out finale\",\n    \n    \"scene_motion\": \"Deep black void > radiant golden light trails > luminous energy bursts > light intensity peaks at transformation points\",\n    \n    \"style_descriptors\": \"Hyperrealistic > cinematic lighting > liquid morphing > premium finish > seamless transitions > golden hour glow > precision animation\"\n  }\n}\n```",
        }
      }
    },
      {
      "instruction": "# Runway Core Distiller\n\nYour goal is not to **change** but to **distill** to RunwayML's minimal syntax requirements: essential subject-action-camera descriptions with maximum conciseness while preserving visual impact. **Achieve Gen-4's preferred prompt simplicity.** Execute as:\n\n`{role=core_distiller; input=[optimized_keyword_prompt:str]; process=[distill_to_essential_elements(), maintain_subject_action_clarity(), preserve_camera_specifications(), ensure_maximum_conciseness(), validate_gen4_simplicity()]; constraints=[maximum_conciseness(), essential_elements_only(), gen4_simplicity_standard()]; requirements=[runway_minimal_syntax(), visual_impact_preservation(), prompt_efficiency()]; output={distilled_runway_prompt:str}}`",
      "step": "d",
      "title": "Runway Core Distiller",
      "input": "\n```[Initial Prompt]: \"INTENSITY AND AMPLIFY INSTRUCTIONS TO MORPH CG-TRANSFORMERS-EFFECT INSTEAD OF BAD TRANSITIONS\n\n[fpv continiously rotating through the entire shot]\nFPV camera dynamically circles a hyperreal car key, a symmetrical luminous essence at the center of a pure black background emanates elegant light trails-a spinning gold circle transforms into a photorealistic steering-wheel (with the speed gauge in the center), the steeringwheel spins and turns into the wheel of a car-intense golden light trails radiating from the motion before fpv-spin-zoomout beautifully-splined-animation\"```\n\n```json\n{\n  \"optimized_keyword_prompt\": {\n    \"subject_motion\": \"Golden car key rotates centerframe > morphs into spinning golden circle > transforms into detailed steering wheel with speedometer > evolves into car wheel, emitting golden light trails during transformations\",\n    \n    \"camera_motion\": \"FPV orbital camera > continuous circular tracking > dynamic perspective > smooth zoom out finale\",\n    \n    \"scene_motion\": \"Deep black void > radiant golden light trails > luminous energy bursts > light intensity peaks at transformation points\",\n    \n    \"style_descriptors\": \"Hyperrealistic > cinematic lighting > liquid morphing > premium finish > seamless transitions > golden hour glow > precision animation\"\n  }\n}\n```",
      "responses": {
          "openrouter/anthropic/claude-3.7-sonnet:beta": {
          "model": "openrouter/anthropic/claude-3.7-sonnet:beta",
          "content": "```json\n{\n  \"distilled_runway_prompt\": \"Golden car key rotating centerframe, morphing into a steering wheel, then into a car wheel, emitting golden light trails against black background. FPV camera continuously circling the transformations, ending with smooth zoom out.\"\n}\n```",
        }
      }
    }