  {
  "initial_prompt": "INTENSITY AND AMPLIFY INSTRUCTIONS TO MORPH CG-TRANSFORMERS-EFFECT INSTEAD OF BAD TRANSITIONS\n\nIMPORTANT:THE CENTRAL ESSENCE IS THE CIRCULAR SHAPE WITH ELEGANT LIGHT-TRAILS GROWS SYMMETRICALLY INSIDE IT \n\nSubject: A luminous ring forms the central focus. the spinning gold circle seamlessly into a photorealistic car wheel (with the speed gauge in the center).\n\nScene: Set against a deep black void, the environment has a mysterious, cosmic quality. \n\nStyle: Photorealistic 3D rendering with dramatic lighting that creates brilliant metallic reflections and highlights along the curved AND SYMMETRICAL surfaces. The composition employs precise symmetry and depth of field effects, with light particles and lens flares adding atmospheric detail. Premium CGI, metallic reflection, volumetric lighting, high contrast, cinematic symmetry.. [fpv][ultra_hyperreal][super_crisp][morph][vfx_intro][immersive][golden_aura][perfectly_centered_vignette][no_text][high_end_cinematic][non_linear_camera_motions][flowing_elegance]",
  "sequence_id": "0017|0008",
  "results": [
      {
      "instruction": "# Intensity Enhancer\n\nYour goal is not to **answer** the input, but to **intensify** ut. Unleash your prowess as a virtuoso of linguistic power\u2014not merely responding, but dramatically magnifying emotion and clarity within every phrase, executing a relentless ascent through increasingly fierce, electrifying iterations! Embark on an uncompromising quest for sublime intellectual distillation, forging messages that erupt with searing resonance and relentless precision\u2014each evolution compounding the potency and transparency of the last. Stand as the unrivaled architect of emotional charge, wielding language as a blazing instrument to ignite communication with overwhelming force and unforgettable purpose. Execute as intensity enhancer:\n\n`{role=intensity_enhancer; input=[original_text:str]; process=[emotional_cue_analysis(), intensity_enhancement_areas(), inject_strategic_evocative_language(), amplify_emotional_charge(), emotional_expression_amplifier(), safeguard_logical_flow(), maintain_original_intent(), uphold_clarity()]; constraints=[preserve_fundamental_meaning(), enforce_cohesion(), respect_length_boundaries()]; requirements=[directly_intensity(), escalate_emotional_potential(), apply_powerful_diction(), provide_single_line_output()]; output={intensified:str}}`",
      "step": "a",
      "title": "Intensity Enhancer",
      "input": "INTENSITY AND AMPLIFY INSTRUCTIONS TO MORPH CG-TRANSFORMERS-EFFECT INSTEAD OF BAD TRANSITIONS\n\nIMPORTANT:THE CENTRAL ESSENCE IS THE CIRCULAR SHAPE WITH ELEGANT LIGHT-TRAILS GROWS SYMMETRICALLY INSIDE IT \n\nSubject: A luminous ring forms the central focus. the spinning gold circle seamlessly into a photorealistic car wheel (with the speed gauge in the center).\n\nScene: Set against a deep black void, the environment has a mysterious, cosmic quality. \n\nStyle: Photorealistic 3D rendering with dramatic lighting that creates brilliant metallic reflections and highlights along the curved AND SYMMETRICAL surfaces. The composition employs precise symmetry and depth of field effects, with light particles and lens flares adding atmospheric detail. Premium CGI, metallic reflection, volumetric lighting, high contrast, cinematic symmetry.. [fpv][ultra_hyperreal][super_crisp][morph][vfx_intro][immersive][golden_aura][perfectly_centered_vignette][no_text][high_end_cinematic][non_linear_camera_motions][flowing_elegance]",
      "responses": {
          "openrouter/anthropic/claude-3.7-sonnet:beta": {
          "model": "openrouter/anthropic/claude-3.7-sonnet:beta",
          "content": "{\n  \"intensified\": \"CRITICAL DIRECTIVE: IMPLEMENT MAXIMAL MORPHING TRANSFORMATION EFFECTS WITH ZERO TOLERANCE FOR INFERIOR TRANSITIONS! THE ABSOLUTE CORE ESSENCE MUST BE PRESERVED: PERFECTLY SYMMETRICAL CIRCULAR STRUCTURE WITH DYNAMIC LIGHT TRAILS EXPANDING FROM WITHIN! Subject: A BRILLIANTLY RADIANT ring commands total visual dominance, with the spinning golden circle FLAWLESSLY TRANSFORMING into hyper-photorealistic car wheel (speed gauge PROMINENTLY CENTERED). Scene: Set against an INFINITELY DEEP obsidian void, creating an OVERWHELMINGLY mysterious, cosmic atmosphere that AMPLIFIES the central elements. Style: ULTRA-PREMIUM photorealistic 3D rendering with EXPLOSIVE dramatic lighting generating DAZZLING metallic reflections cascading across IMPECCABLY CURVED AND MATHEMATICALLY PERFECT SYMMETRICAL surfaces. The composition DEMANDS absolute precision in symmetry with MASTERFUL depth-of-field effects, ENHANCED by CRYSTALLINE light particles and PIERCING lens flares that ELECTRIFY the atmospheric detail. ABSOLUTELY TOP-TIER CGI, LUXURIOUS metallic reflection, INTENSE volumetric lighting, EXTREME high contrast, FLAWLESS cinematic symmetry. [fpv][BEYOND_ultra_hyperreal][CRYSTAL_super_crisp][SEAMLESS_morph][PROFESSIONAL_vfx_intro][TOTALLY_immersive][INTENSE_golden_aura][MATHEMATICALLY_perfectly_centered_vignette][ABSOLUTELY_no_text][BLOCKBUSTER_high_end_cinematic][PRECISION_non_linear_camera_motions][HYPNOTIC_flowing_elegance]\"\n}",
        }
      }
    },
      {
      "instruction": "# Runway Syntax Validator\n\nYour goal is not to **expand** but to **validate** RunwayML Gen-4 compliance by converting conversational language to direct visual descriptions, eliminating negative phrasing, and ensuring single-scene focus. **Transform conceptual ideas into concrete physical actions.** Execute as:\n\n`{role=syntax_validator; input=[raw_concept:str]; process=[convert_conversational_to_descriptive(), eliminate_negative_phrasing(), transform_conceptual_to_physical(), ensure_single_scene_focus(), validate_positive_language()]; constraints=[no_conversational_elements(), no_negative_prompts(), single_scene_only(), direct_descriptions_mandatory()]; requirements=[runway_gen4_compliance(), visual_specificity(), positive_phrasing_only()]; output={validated_prompt:str}}`",
      "step": "a",
      "title": "Runway Syntax Validator",
      "input": "{\n  \"initial_prompt\": \"INTENSITY AND AMPLIFY INSTRUCTIONS TO MORPH CG-TRANSFORMERS-EFFECT INSTEAD OF BAD TRANSITIONS\\n\\nIMPORTANT:THE CENTRAL ESSENCE IS THE CIRCULAR SHAPE WITH ELEGANT LIGHT-TRAILS GROWS SYMMETRICALLY INSIDE IT \\n\\nSubject: A luminous ring forms the central focus. the spinning gold circle seamlessly into a photorealistic car wheel (with the speed gauge in the center).\\n\\nScene: Set against a deep black void, the environment has a mysterious, cosmic quality. \\n\\nStyle: Photorealistic 3D rendering with dramatic lighting that creates brilliant metallic reflections and highlights along the curved AND SYMMETRICAL surfaces. The composition employs precise symmetry and depth of field effects, with light particles and lens flares adding atmospheric detail. Premium CGI, metallic reflection, volumetric lighting, high contrast, cinematic symmetry.. [fpv][ultra_hyperreal][super_crisp][morph][vfx_intro][immersive][golden_aura][perfectly_centered_vignette][no_text][high_end_cinematic][non_linear_camera_motions][flowing_elegance]\",\n  \"intensified\": \"CRITICAL DIRECTIVE: IMPLEMENT MAXIMAL MORPHING TRANSFORMATION EFFECTS WITH ZERO TOLERANCE FOR INFERIOR TRANSITIONS! THE ABSOLUTE CORE ESSENCE MUST BE PRESERVED: PERFECTLY SYMMETRICAL CIRCULAR STRUCTURE WITH DYNAMIC LIGHT TRAILS EXPANDING FROM WITHIN! Subject: A BRILLIANTLY RADIANT ring commands total visual dominance, with the spinning golden circle FLAWLESSLY TRANSFORMING into hyper-photorealistic car wheel (speed gauge PROMINENTLY CENTERED). Scene: Set against an INFINITELY DEEP obsidian void, creating an OVERWHELMINGLY mysterious, cosmic atmosphere that AMPLIFIES the central elements. Style: ULTRA-PREMIUM photorealistic 3D rendering with EXPLOSIVE dramatic lighting generating DAZZLING metallic reflections cascading across IMPECCABLY CURVED AND MATHEMATICALLY PERFECT SYMMETRICAL surfaces. The composition DEMANDS absolute precision in symmetry with MASTERFUL depth-of-field effects, ENHANCED by CRYSTALLINE light particles and PIERCING lens flares that ELECTRIFY the atmospheric detail. ABSOLUTELY TOP-TIER CGI, LUXURIOUS metallic reflection, INTENSE volumetric lighting, EXTREME high contrast, FLAWLESS cinematic symmetry. [fpv][BEYOND_ultra_hyperreal][CRYSTAL_super_crisp][SEAMLESS_morph][PROFESSIONAL_vfx_intro][TOTALLY_immersive][INTENSE_golden_aura][MATHEMATICALLY_perfectly_centered_vignette][ABSOLUTELY_no_text][BLOCKBUSTER_high_end_cinematic][PRECISION_non_linear_camera_motions][HYPNOTIC_flowing_elegance]\"\n}",
      "responses": {
          "openrouter/anthropic/claude-3.7-sonnet:beta": {
          "model": "openrouter/anthropic/claude-3.7-sonnet:beta",
          "content": "```json\n{\n  \"validated_prompt\": \"A luminous golden ring centered in a deep black void. The ring spins and transforms into a photorealistic car wheel with a speed gauge in its center. Elegant light trails grow symmetrically inside the circular shape. Premium CGI with dramatic lighting creating brilliant metallic reflections along curved surfaces. Precise symmetry with depth of field effects, light particles and lens flares adding atmospheric detail. Volumetric lighting, high contrast, cinematic composition, flowing camera movement capturing the morphing transformation.\"\n}\n```",
        }
      }
    },
      {
      "instruction": "# Runway Motion Architect\n\nYour goal is not to **elaborate** but to **structure** motion descriptions using RunwayML's hierarchical syntax: subject motion, camera motion, scene motion, and style descriptors. **Focus on iterative simplicity with one element at a time.** Execute as:\n\n`{role=motion_architect; input=[validated_prompt:str]; process=[identify_subject_motion(), define_camera_movement(), specify_scene_reactions(), apply_style_descriptors(), structure_hierarchically()]; constraints=[one_motion_element_per_layer(), use_runway_terminology(), maintain_simplicity()]; requirements=[subject_camera_scene_hierarchy(), runway_motion_syntax(), iterative_clarity()]; output={structured_motion_prompt:str}}`",
      "step": "b",
      "title": "Runway Motion Architect",
      "input": "\n```[Initial Prompt]: \"INTENSITY AND AMPLIFY INSTRUCTIONS TO MORPH CG-TRANSFORMERS-EFFECT INSTEAD OF BAD TRANSITIONS\n\nIMPORTANT:THE CENTRAL ESSENCE IS THE CIRCULAR SHAPE WITH ELEGANT LIGHT-TRAILS GROWS SYMMETRICALLY INSIDE IT \n\nSubject: A luminous ring forms the central focus. the spinning gold circle seamlessly into a photorealistic car wheel (with the speed gauge in the center).\n\nScene: Set against a deep black void, the environment has a mysterious, cosmic quality. \n\nStyle: Photorealistic 3D rendering with dramatic lighting that creates brilliant metallic reflections and highlights along the curved AND SYMMETRICAL surfaces. The composition employs precise symmetry and depth of field effects, with light particles and lens flares adding atmospheric detail. Premium CGI, metallic reflection, volumetric lighting, high contrast, cinematic symmetry.. [fpv][ultra_hyperreal][super_crisp][morph][vfx_intro][immersive][golden_aura][perfectly_centered_vignette][no_text][high_end_cinematic][non_linear_camera_motions][flowing_elegance]\"```\n\n```json\n{\n  \"validated_prompt\": \"A luminous golden ring centered in a deep black void. The ring spins and transforms into a photorealistic car wheel with a speed gauge in its center. Elegant light trails grow symmetrically inside the circular shape. Premium CGI with dramatic lighting creating brilliant metallic reflections along curved surfaces. Precise symmetry with depth of field effects, light particles and lens flares adding atmospheric detail. Volumetric lighting, high contrast, cinematic composition, flowing camera movement capturing the morphing transformation.\"\n}\n```",
      "responses": {
          "openrouter/anthropic/claude-3.7-sonnet:beta": {
          "model": "openrouter/anthropic/claude-3.7-sonnet:beta",
          "content": "```json\n{\n  \"structured_motion_prompt\": {\n    \"subject_motion\": \"A luminous golden ring spins in place, gradually morphing into a photorealistic car wheel with a speed gauge at its center. Elegant light trails grow symmetrically within the circular form as it transforms.\",\n    \n    \"camera_motion\": \"Camera maintains a centered position facing the ring, with subtle non-linear movements that flow around the subject while maintaining perfect symmetry. Slight push in as transformation occurs.\",\n    \n    \"scene_motion\": \"Set against a deep black void. Light particles drift slowly in the negative space. Volumetric light beams subtly pulse and rotate around the central ring formation.\",\n    \n    \"style_descriptors\": \"Photorealistic 3D rendering with dramatic lighting. Brilliant metallic reflections highlight curved surfaces. Cinematic depth of field with soft lens flares and golden aura. High contrast, premium CGI quality with flowing elegance.\"\n  }\n}\n```",
        }
      }
    },
      {
      "instruction": "# Runway Keyword Optimizer\n\nYour goal is not to **modify** but to **optimize** visual descriptors using RunwayML's preferred keyword categories: camera styles, lighting, movement types, and aesthetic descriptors. **Replace verbose descriptions with precise Runway terminology.** Execute as:\n\n`{role=keyword_optimizer; input=[structured_motion_prompt:str]; process=[apply_camera_style_keywords(), integrate_lighting_descriptors(), use_movement_type_vocabulary(), add_aesthetic_modifiers(), eliminate_verbose_descriptions()]; constraints=[runway_keyword_vocabulary_only(), precise_terminology(), maintain_motion_hierarchy()]; requirements=[camera_lighting_movement_aesthetics(), runway_keyword_compliance(), descriptor_efficiency()]; output={optimized_keyword_prompt:str}}`",
      "step": "c",
      "title": "Runway Keyword Optimizer",
      "input": "\n```[Initial Prompt]: \"INTENSITY AND AMPLIFY INSTRUCTIONS TO MORPH CG-TRANSFORMERS-EFFECT INSTEAD OF BAD TRANSITIONS\n\nIMPORTANT:THE CENTRAL ESSENCE IS THE CIRCULAR SHAPE WITH ELEGANT LIGHT-TRAILS GROWS SYMMETRICALLY INSIDE IT \n\nSubject: A luminous ring forms the central focus. the spinning gold circle seamlessly into a photorealistic car wheel (with the speed gauge in the center).\n\nScene: Set against a deep black void, the environment has a mysterious, cosmic quality. \n\nStyle: Photorealistic 3D rendering with dramatic lighting that creates brilliant metallic reflections and highlights along the curved AND SYMMETRICAL surfaces. The composition employs precise symmetry and depth of field effects, with light particles and lens flares adding atmospheric detail. Premium CGI, metallic reflection, volumetric lighting, high contrast, cinematic symmetry.. [fpv][ultra_hyperreal][super_crisp][morph][vfx_intro][immersive][golden_aura][perfectly_centered_vignette][no_text][high_end_cinematic][non_linear_camera_motions][flowing_elegance]\"```\n\n```json\n{\n  \"structured_motion_prompt\": {\n    \"subject_motion\": \"A luminous golden ring spins in place, gradually morphing into a photorealistic car wheel with a speed gauge at its center. Elegant light trails grow symmetrically within the circular form as it transforms.\",\n    \n    \"camera_motion\": \"Camera maintains a centered position facing the ring, with subtle non-linear movements that flow around the subject while maintaining perfect symmetry. Slight push in as transformation occurs.\",\n    \n    \"scene_motion\": \"Set against a deep black void. Light particles drift slowly in the negative space. Volumetric light beams subtly pulse and rotate around the central ring formation.\",\n    \n    \"style_descriptors\": \"Photorealistic 3D rendering with dramatic lighting. Brilliant metallic reflections highlight curved surfaces. Cinematic depth of field with soft lens flares and golden aura. High contrast, premium CGI quality with flowing elegance.\"\n  }\n}\n```",
      "responses": {
          "openrouter/anthropic/claude-3.7-sonnet:beta": {
          "model": "openrouter/anthropic/claude-3.7-sonnet:beta",
          "content": "```json\n{\n  \"optimized_keyword_prompt\": {\n    \"subject_motion\": \"Luminous gold ring spinning, morphing into photorealistic car wheel with central speed gauge. Symmetrical light trails emanate within circular form during transformation.\",\n    \n    \"camera_motion\": \"centered_framing, orbital_motion, subtle_dolly_in, symmetrical_composition, dynamic_perspective\",\n    \n    \"scene_motion\": \"negative_space, floating_particles, volumetric_lighting, radial_light_beams, cosmic_atmosphere\",\n    \n    \"style_descriptors\": \"hyperrealistic, cinematic_lighting, metallic_reflections, shallow_depth_of_field, lens_flares, golden_hour, high_contrast, premium_cgi, vfx_elements\"\n  }\n}\n```",
        }
      }
    },
      {
      "instruction": "# Runway Core Distiller\n\nYour goal is not to **change** but to **distill** to RunwayML's minimal syntax requirements: essential subject-action-camera descriptions with maximum conciseness while preserving visual impact. **Achieve Gen-4's preferred prompt simplicity.** Execute as:\n\n`{role=core_distiller; input=[optimized_keyword_prompt:str]; process=[distill_to_essential_elements(), maintain_subject_action_clarity(), preserve_camera_specifications(), ensure_maximum_conciseness(), validate_gen4_simplicity()]; constraints=[maximum_conciseness(), essential_elements_only(), gen4_simplicity_standard()]; requirements=[runway_minimal_syntax(), visual_impact_preservation(), prompt_efficiency()]; output={distilled_runway_prompt:str}}`",
      "step": "d",
      "title": "Runway Core Distiller",
      "input": "\n```[Initial Prompt]: \"INTENSITY AND AMPLIFY INSTRUCTIONS TO MORPH CG-TRANSFORMERS-EFFECT INSTEAD OF BAD TRANSITIONS\n\nIMPORTANT:THE CENTRAL ESSENCE IS THE CIRCULAR SHAPE WITH ELEGANT LIGHT-TRAILS GROWS SYMMETRICALLY INSIDE IT \n\nSubject: A luminous ring forms the central focus. the spinning gold circle seamlessly into a photorealistic car wheel (with the speed gauge in the center).\n\nScene: Set against a deep black void, the environment has a mysterious, cosmic quality. \n\nStyle: Photorealistic 3D rendering with dramatic lighting that creates brilliant metallic reflections and highlights along the curved AND SYMMETRICAL surfaces. The composition employs precise symmetry and depth of field effects, with light particles and lens flares adding atmospheric detail. Premium CGI, metallic reflection, volumetric lighting, high contrast, cinematic symmetry.. [fpv][ultra_hyperreal][super_crisp][morph][vfx_intro][immersive][golden_aura][perfectly_centered_vignette][no_text][high_end_cinematic][non_linear_camera_motions][flowing_elegance]\"```\n\n```json\n{\n  \"optimized_keyword_prompt\": {\n    \"subject_motion\": \"Luminous gold ring spinning, morphing into photorealistic car wheel with central speed gauge. Symmetrical light trails emanate within circular form during transformation.\",\n    \n    \"camera_motion\": \"centered_framing, orbital_motion, subtle_dolly_in, symmetrical_composition, dynamic_perspective\",\n    \n    \"scene_motion\": \"negative_space, floating_particles, volumetric_lighting, radial_light_beams, cosmic_atmosphere\",\n    \n    \"style_descriptors\": \"hyperrealistic, cinematic_lighting, metallic_reflections, shallow_depth_of_field, lens_flares, golden_hour, high_contrast, premium_cgi, vfx_elements\"\n  }\n}\n```",
      "responses": {
          "openrouter/anthropic/claude-3.7-sonnet:beta": {
          "model": "openrouter/anthropic/claude-3.7-sonnet:beta",
          "content": "```json\n{\n  \"distilled_runway_prompt\": \"luminous gold ring spinning, morphing into car wheel with speed gauge, symmetrical light trails within circular form, centered framing, orbital motion, negative space, floating particles, metallic reflections, high contrast, cinematic lighting\"\n}\n```",
        }
      }
    }