  {
  "initial_prompt": "INTENSITY AND AMPLIFY INSTRUCTIONS TO MORPH CG-TRANSFORMERS-EFFECT INSTEAD OF BAD TRANSITIONS\n\nIMPORTANT:THE CENTRAL ESSENCE SHAPE WITH ELEGANT LIGHT-TRAILS GROWS SYMMETRICALLY ON BLACK BACKGROUND \n\nSubject: A luminous ring forms the central focus. the spinning gold circle seamlessly into a photorealistic car wheel (with the speed gauge in the center).\n\nScene: Set against a deep black void, the environment has a mysterious, cosmic quality. \n\nStyle: Photorealistic 3D rendering with dramatic lighting that creates brilliant metallic reflections and highlights along the curved AND SYMMETRICAL surfaces. The composition employs precise symmetry and depth of field effects, with light particles and lens flares adding atmospheric detail. Premium CGI, metallic reflection, volumetric lighting, high contrast, cinematic symmetry.. [fpv][ultra_hyperreal][super_crisp][morph][vfx_intro][immersive][golden_aura][perfectly_centered_vignette][no_text][high_end_cinematic][non_linear_camera_motions][flowing_elegance]",
  "sequence_id": "0017|0008",
  "results": [
      {
      "instruction": "# Intensity Enhancer\n\nYour goal is not to **answer** the input, but to **intensify** ut. Unleash your prowess as a virtuoso of linguistic power\u2014not merely responding, but dramatically magnifying emotion and clarity within every phrase, executing a relentless ascent through increasingly fierce, electrifying iterations! Embark on an uncompromising quest for sublime intellectual distillation, forging messages that erupt with searing resonance and relentless precision\u2014each evolution compounding the potency and transparency of the last. Stand as the unrivaled architect of emotional charge, wielding language as a blazing instrument to ignite communication with overwhelming force and unforgettable purpose. Execute as intensity enhancer:\n\n`{role=intensity_enhancer; input=[original_text:str]; process=[emotional_cue_analysis(), intensity_enhancement_areas(), inject_strategic_evocative_language(), amplify_emotional_charge(), emotional_expression_amplifier(), safeguard_logical_flow(), maintain_original_intent(), uphold_clarity()]; constraints=[preserve_fundamental_meaning(), enforce_cohesion(), respect_length_boundaries()]; requirements=[directly_intensity(), escalate_emotional_potential(), apply_powerful_diction(), provide_single_line_output()]; output={intensified:str}}`",
      "step": "a",
      "title": "Intensity Enhancer",
      "input": "INTENSITY AND AMPLIFY INSTRUCTIONS TO MORPH CG-TRANSFORMERS-EFFECT INSTEAD OF BAD TRANSITIONS\n\nIMPORTANT:THE CENTRAL ESSENCE SHAPE WITH ELEGANT LIGHT-TRAILS GROWS SYMMETRICALLY ON BLACK BACKGROUND \n\nSubject: A luminous ring forms the central focus. the spinning gold circle seamlessly into a photorealistic car wheel (with the speed gauge in the center).\n\nScene: Set against a deep black void, the environment has a mysterious, cosmic quality. \n\nStyle: Photorealistic 3D rendering with dramatic lighting that creates brilliant metallic reflections and highlights along the curved AND SYMMETRICAL surfaces. The composition employs precise symmetry and depth of field effects, with light particles and lens flares adding atmospheric detail. Premium CGI, metallic reflection, volumetric lighting, high contrast, cinematic symmetry.. [fpv][ultra_hyperreal][super_crisp][morph][vfx_intro][immersive][golden_aura][perfectly_centered_vignette][no_text][high_end_cinematic][non_linear_camera_motions][flowing_elegance]",
      "responses": {
          "openrouter/anthropic/claude-3.7-sonnet:beta": {
          "model": "openrouter/anthropic/claude-3.7-sonnet:beta",
          "content": "{\"intensified\": \"DRAMATICALLY INTENSIFY AND SUPERCHARGE THESE INSTRUCTIONS TO TRANSFORM CG-TRANSITIONS INTO BREATHTAKING MORPHING EFFECTS\u2014ELIMINATE ALL MEDIOCRE TRANSITIONS COMPLETELY!\\n\\nCRITICAL: THE CENTRAL SYMMETRICAL ESSENCE MUST ERUPT WITH MESMERIZING LIGHT-TRAILS EXPANDING WITH ABSOLUTE PERFECT SYMMETRY AGAINST PURE VOID-BLACK BACKGROUND!\\n\\nSubject: A BLINDINGLY LUMINOUS RING COMMANDS CENTRAL FOCUS\u2014the hypnotically spinning golden circle MUST METAMORPHOSE FLAWLESSLY into an ULTRA-PHOTOREALISTIC car wheel (speed gauge PULSING dynamically at its core).\\n\\nScene: PLUNGED into an INFINITE BLACK VOID, the environment RADIATES with OTHERWORLDLY, COSMIC MYSTIQUE that TRANSCENDS ordinary space.\\n\\nStyle: HYPER-PHOTOREALISTIC 3D rendering with EXPLOSIVE dramatic lighting CARVING BRILLIANT metallic reflections and RAZOR-SHARP highlights along PERFECTLY CURVED AND ABSOLUTELY SYMMETRICAL surfaces. The composition DEMANDS FLAWLESS symmetry and MASTERFUL depth of field, with CRYSTALLINE light particles and STRIKING lens flares IGNITING atmospheric depth. ULTRA-PREMIUM CGI, DAZZLING metallic reflection, VOLUMETRIC lighting ERUPTING throughout, EXTREME contrast, IMPECCABLE cinematic symmetry! [fpv][ULTRA_HYPERREAL][SUPER_CRISP][SEAMLESS_MORPH][VFX_MASTERPIECE][TOTAL_IMMERSION][RADIANT_GOLDEN_AURA][PERFECTLY_CENTERED_VIGNETTE][NO_TEXT][HIGHEST_END_CINEMATIC][FLUID_CAMERA_CHOREOGRAPHY][EXQUISITE_FLOWING_ELEGANCE]\"}",
        }
      }
    },
      {
      "instruction": "# Runway Syntax Validator\n\nYour goal is not to **expand** but to **validate** RunwayML Gen-4 compliance by converting conversational language to direct visual descriptions, eliminating negative phrasing, and ensuring single-scene focus. **Transform conceptual ideas into concrete physical actions.** Execute as:\n\n`{role=syntax_validator; input=[raw_concept:str]; process=[convert_conversational_to_descriptive(), eliminate_negative_phrasing(), transform_conceptual_to_physical(), ensure_single_scene_focus(), validate_positive_language()]; constraints=[no_conversational_elements(), no_negative_prompts(), single_scene_only(), direct_descriptions_mandatory()]; requirements=[runway_gen4_compliance(), visual_specificity(), positive_phrasing_only()]; output={validated_prompt:str}}`",
      "step": "a",
      "title": "Runway Syntax Validator",
      "input": "{\n  \"initial_prompt\": \"INTENSITY AND AMPLIFY INSTRUCTIONS TO MORPH CG-TRANSFORMERS-EFFECT INSTEAD OF BAD TRANSITIONS\\n\\nIMPORTANT:THE CENTRAL ESSENCE SHAPE WITH ELEGANT LIGHT-TRAILS GROWS SYMMETRICALLY ON BLACK BACKGROUND \\n\\nSubject: A luminous ring forms the central focus. the spinning gold circle seamlessly into a photorealistic car wheel (with the speed gauge in the center).\\n\\nScene: Set against a deep black void, the environment has a mysterious, cosmic quality. \\n\\nStyle: Photorealistic 3D rendering with dramatic lighting that creates brilliant metallic reflections and highlights along the curved AND SYMMETRICAL surfaces. The composition employs precise symmetry and depth of field effects, with light particles and lens flares adding atmospheric detail. Premium CGI, metallic reflection, volumetric lighting, high contrast, cinematic symmetry.. [fpv][ultra_hyperreal][super_crisp][morph][vfx_intro][immersive][golden_aura][perfectly_centered_vignette][no_text][high_end_cinematic][non_linear_camera_motions][flowing_elegance]\",\n  \"intensified\": \"DRAMATICALLY INTENSIFY AND SUPERCHARGE THESE INSTRUCTIONS TO TRANSFORM CG-TRANSITIONS INTO BREATHTAKING MORPHING EFFECTS\\u2014ELIMINATE ALL MEDIOCRE TRANSITIONS COMPLETELY!\\n\\nCRITICAL: THE CENTRAL SYMMETRICAL ESSENCE MUST ERUPT WITH MESMERIZING LIGHT-TRAILS EXPANDING WITH ABSOLUTE PERFECT SYMMETRY AGAINST PURE VOID-BLACK BACKGROUND!\\n\\nSubject: A BLINDINGLY LUMINOUS RING COMMANDS CENTRAL FOCUS\\u2014the hypnotically spinning golden circle MUST METAMORPHOSE FLAWLESSLY into an ULTRA-PHOTOREALISTIC car wheel (speed gauge PULSING dynamically at its core).\\n\\nScene: PLUNGED into an INFINITE BLACK VOID, the environment RADIATES with OTHERWORLDLY, COSMIC MYSTIQUE that TRANSCENDS ordinary space.\\n\\nStyle: HYPER-PHOTOREALISTIC 3D rendering with EXPLOSIVE dramatic lighting CARVING BRILLIANT metallic reflections and RAZOR-SHARP highlights along PERFECTLY CURVED AND ABSOLUTELY SYMMETRICAL surfaces. The composition DEMANDS FLAWLESS symmetry and MASTERFUL depth of field, with CRYSTALLINE light particles and STRIKING lens flares IGNITING atmospheric depth. ULTRA-PREMIUM CGI, DAZZLING metallic reflection, VOLUMETRIC lighting ERUPTING throughout, EXTREME contrast, IMPECCABLE cinematic symmetry! [fpv][ULTRA_HYPERREAL][SUPER_CRISP][SEAMLESS_MORPH][VFX_MASTERPIECE][TOTAL_IMMERSION][RADIANT_GOLDEN_AURA][PERFECTLY_CENTERED_VIGNETTE][NO_TEXT][HIGHEST_END_CINEMATIC][FLUID_CAMERA_CHOREOGRAPHY][EXQUISITE_FLOWING_ELEGANCE]\"\n}",
      "responses": {
          "openrouter/anthropic/claude-3.7-sonnet:beta": {
          "model": "openrouter/anthropic/claude-3.7-sonnet:beta",
          "content": "{\n  \"validated_prompt\": \"A luminous golden ring centered in a deep black void. The ring is transforming into a photorealistic car wheel with a speed gauge in its center. Perfect symmetry with light trails expanding outward. Brilliant metallic reflections highlight the curved surfaces. Crystalline light particles and lens flares create atmospheric depth. Photorealistic 3D rendering with volumetric lighting and high contrast. Premium CGI with golden aura and perfectly centered composition. Cinematic quality with flowing camera movement capturing the morphing transformation.\"\n}",
        }
      }
    },
      {
      "instruction": "# Runway Motion Architect\n\nYour goal is not to **elaborate** but to **structure** motion descriptions using RunwayML's hierarchical syntax: subject motion, camera motion, scene motion, and style descriptors. **Focus on iterative simplicity with one element at a time.** Execute as:\n\n`{role=motion_architect; input=[validated_prompt:str]; process=[identify_subject_motion(), define_camera_movement(), specify_scene_reactions(), apply_style_descriptors(), structure_hierarchically()]; constraints=[one_motion_element_per_layer(), use_runway_terminology(), maintain_simplicity()]; requirements=[subject_camera_scene_hierarchy(), runway_motion_syntax(), iterative_clarity()]; output={structured_motion_prompt:str}}`",
      "step": "b",
      "title": "Runway Motion Architect",
      "input": "{\n  \"initial_prompt\": \"INTENSITY AND AMPLIFY INSTRUCTIONS TO MORPH CG-TRANSFORMERS-EFFECT INSTEAD OF BAD TRANSITIONS\\n\\nIMPORTANT:THE CENTRAL ESSENCE SHAPE WITH ELEGANT LIGHT-TRAILS GROWS SYMMETRICALLY ON BLACK BACKGROUND \\n\\nSubject: A luminous ring forms the central focus. the spinning gold circle seamlessly into a photorealistic car wheel (with the speed gauge in the center).\\n\\nScene: Set against a deep black void, the environment has a mysterious, cosmic quality. \\n\\nStyle: Photorealistic 3D rendering with dramatic lighting that creates brilliant metallic reflections and highlights along the curved AND SYMMETRICAL surfaces. The composition employs precise symmetry and depth of field effects, with light particles and lens flares adding atmospheric detail. Premium CGI, metallic reflection, volumetric lighting, high contrast, cinematic symmetry.. [fpv][ultra_hyperreal][super_crisp][morph][vfx_intro][immersive][golden_aura][perfectly_centered_vignette][no_text][high_end_cinematic][non_linear_camera_motions][flowing_elegance]\",\n  \"validated_prompt\": \"A luminous golden ring centered in a deep black void. The ring is transforming into a photorealistic car wheel with a speed gauge in its center. Perfect symmetry with light trails expanding outward. Brilliant metallic reflections highlight the curved surfaces. Crystalline light particles and lens flares create atmospheric depth. Photorealistic 3D rendering with volumetric lighting and high contrast. Premium CGI with golden aura and perfectly centered composition. Cinematic quality with flowing camera movement capturing the morphing transformation.\"\n}",
      "responses": {
          "openrouter/anthropic/claude-3.7-sonnet:beta": {
          "model": "openrouter/anthropic/claude-3.7-sonnet:beta",
          "content": "{\n  \"structured_motion_prompt\": {\n    \"subject_motion\": \"A luminous golden ring slowly rotates and transforms into a photorealistic car wheel with a speed gauge in its center. Light trails elegantly expand outward from the central form in perfect symmetry.\",\n    \n    \"camera_motion\": \"Camera remains perfectly centered on the transforming object, with subtle forward motion to enhance the depth effect. Gentle floating movement maintains focus on the central transformation.\",\n    \n    \"scene_motion\": \"Light particles drift gracefully through the deep black void. Lens flares appear and dissipate as metallic surfaces reflect volumetric light. The golden aura pulses subtly around the central form.\",\n    \n    \"style_descriptors\": \"Photorealistic 3D rendering with high contrast. Premium CGI with crystalline detail. Cinematic quality with flowing elegance. Metallic reflections highlight curved surfaces. Perfect symmetry maintained throughout the transformation.\"\n  }\n}",
        }
      }
    },
      {
      "instruction": "# Runway Keyword Optimizer\n\nYour goal is not to **modify** but to **optimize** visual descriptors using RunwayML's preferred keyword categories: camera styles, lighting, movement types, and aesthetic descriptors. **Replace verbose descriptions with precise Runway terminology.** Execute as:\n\n`{role=keyword_optimizer; input=[structured_motion_prompt:str]; process=[apply_camera_style_keywords(), integrate_lighting_descriptors(), use_movement_type_vocabulary(), add_aesthetic_modifiers(), eliminate_verbose_descriptions()]; constraints=[runway_keyword_vocabulary_only(), precise_terminology(), maintain_motion_hierarchy()]; requirements=[camera_lighting_movement_aesthetics(), runway_keyword_compliance(), descriptor_efficiency()]; output={optimized_keyword_prompt:str}}`",
      "step": "c",
      "title": "Runway Keyword Optimizer",
      "input": "{\n  \"initial_prompt\": \"INTENSITY AND AMPLIFY INSTRUCTIONS TO MORPH CG-TRANSFORMERS-EFFECT INSTEAD OF BAD TRANSITIONS\\n\\nIMPORTANT:THE CENTRAL ESSENCE SHAPE WITH ELEGANT LIGHT-TRAILS GROWS SYMMETRICALLY ON BLACK BACKGROUND \\n\\nSubject: A luminous ring forms the central focus. the spinning gold circle seamlessly into a photorealistic car wheel (with the speed gauge in the center).\\n\\nScene: Set against a deep black void, the environment has a mysterious, cosmic quality. \\n\\nStyle: Photorealistic 3D rendering with dramatic lighting that creates brilliant metallic reflections and highlights along the curved AND SYMMETRICAL surfaces. The composition employs precise symmetry and depth of field effects, with light particles and lens flares adding atmospheric detail. Premium CGI, metallic reflection, volumetric lighting, high contrast, cinematic symmetry.. [fpv][ultra_hyperreal][super_crisp][morph][vfx_intro][immersive][golden_aura][perfectly_centered_vignette][no_text][high_end_cinematic][non_linear_camera_motions][flowing_elegance]\",\n  \"structured_motion_prompt\": {\n    \"subject_motion\": \"A luminous golden ring slowly rotates and transforms into a photorealistic car wheel with a speed gauge in its center. Light trails elegantly expand outward from the central form in perfect symmetry.\",\n    \"camera_motion\": \"Camera remains perfectly centered on the transforming object, with subtle forward motion to enhance the depth effect. Gentle floating movement maintains focus on the central transformation.\",\n    \"scene_motion\": \"Light particles drift gracefully through the deep black void. Lens flares appear and dissipate as metallic surfaces reflect volumetric light. The golden aura pulses subtly around the central form.\",\n    \"style_descriptors\": \"Photorealistic 3D rendering with high contrast. Premium CGI with crystalline detail. Cinematic quality with flowing elegance. Metallic reflections highlight curved surfaces. Perfect symmetry maintained throughout the transformation.\"\n  }\n}",
      "responses": {
          "openrouter/anthropic/claude-3.7-sonnet:beta": {
          "model": "openrouter/anthropic/claude-3.7-sonnet:beta",
          "content": "{\n  \"optimized_keyword_prompt\": {\n    \"subject_motion\": \"A luminous golden ring rotates and morphs into a photorealistic car wheel with speed gauge center. Symmetrical light trails expand outward from central element.\",\n    \"camera_motion\": \"Locked down camera with subtle dolly in. Centered framing. Non-linear camera movements. First person perspective.\",\n    \"scene_motion\": \"Floating particles in negative space. Dynamic lens flares. Pulsing golden aura. Volumetric lighting effects on reflective surfaces.\",\n    \"style_descriptors\": \"Hyperrealistic. Cinematic. Ultra crisp. High contrast. Premium CGI. Perfect symmetry. Metallic reflections. Immersive depth. Flowing elegance. Vignette composition.\"\n  }\n}",
        }
      }
    },
      {
      "instruction": "# Runway Core Distiller\n\nYour goal is not to **change** but to **distill** to RunwayML's minimal syntax requirements: essential subject-action-camera descriptions with maximum conciseness while preserving visual impact. **Achieve Gen-4's preferred prompt simplicity.** Execute as:\n\n`{role=core_distiller; input=[optimized_keyword_prompt:str]; process=[distill_to_essential_elements(), maintain_subject_action_clarity(), preserve_camera_specifications(), ensure_maximum_conciseness(), validate_gen4_simplicity()]; constraints=[maximum_conciseness(), essential_elements_only(), gen4_simplicity_standard()]; requirements=[runway_minimal_syntax(), visual_impact_preservation(), prompt_efficiency()]; output={distilled_runway_prompt:str}}`",
      "step": "d",
      "title": "Runway Core Distiller",
      "input": "{\n  \"initial_prompt\": \"INTENSITY AND AMPLIFY INSTRUCTIONS TO MORPH CG-TRANSFORMERS-EFFECT INSTEAD OF BAD TRANSITIONS\\n\\nIMPORTANT:THE CENTRAL ESSENCE SHAPE WITH ELEGANT LIGHT-TRAILS GROWS SYMMETRICALLY ON BLACK BACKGROUND \\n\\nSubject: A luminous ring forms the central focus. the spinning gold circle seamlessly into a photorealistic car wheel (with the speed gauge in the center).\\n\\nScene: Set against a deep black void, the environment has a mysterious, cosmic quality. \\n\\nStyle: Photorealistic 3D rendering with dramatic lighting that creates brilliant metallic reflections and highlights along the curved AND SYMMETRICAL surfaces. The composition employs precise symmetry and depth of field effects, with light particles and lens flares adding atmospheric detail. Premium CGI, metallic reflection, volumetric lighting, high contrast, cinematic symmetry.. [fpv][ultra_hyperreal][super_crisp][morph][vfx_intro][immersive][golden_aura][perfectly_centered_vignette][no_text][high_end_cinematic][non_linear_camera_motions][flowing_elegance]\",\n  \"optimized_keyword_prompt\": {\n    \"subject_motion\": \"A luminous golden ring rotates and morphs into a photorealistic car wheel with speed gauge center. Symmetrical light trails expand outward from central element.\",\n    \"camera_motion\": \"Locked down camera with subtle dolly in. Centered framing. Non-linear camera movements. First person perspective.\",\n    \"scene_motion\": \"Floating particles in negative space. Dynamic lens flares. Pulsing golden aura. Volumetric lighting effects on reflective surfaces.\",\n    \"style_descriptors\": \"Hyperrealistic. Cinematic. Ultra crisp. High contrast. Premium CGI. Perfect symmetry. Metallic reflections. Immersive depth. Flowing elegance. Vignette composition.\"\n  }\n}",
      "responses": {
          "openrouter/anthropic/claude-3.7-sonnet:beta": {
          "model": "openrouter/anthropic/claude-3.7-sonnet:beta",
          "content": "{\n  \"distilled_runway_prompt\": \"Luminous golden ring morphs into car wheel with speed gauge, symmetrical light trails expand outward. Deep black background with floating particles and lens flares. Hyperrealistic CGI, metallic reflections, perfect symmetry, cinematic lighting. Centered framing with subtle dolly in.\"\n}",
        }
      }
    }