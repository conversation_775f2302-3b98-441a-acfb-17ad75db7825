  {
  "initial_prompt": "[FPV camera, photorealistic textures, cinematic lighting, ultra-smooth CG transitions]\n1. Mechanical elements materialize and interlock into precise gear formations\n2. Camera weaves through expanding gear assembly as metallic surfaces shift and gears interlock in hypnotic/seamless patterns\n3. Mechanical morphs and hard metallic edges transform into the spinning weel  (as a golden circle)",
  "sequence_id": "0008",
  "results": [
      {
      "instruction": "# Runway Syntax Validator\n\nYour goal is not to **expand** but to **validate** RunwayML Gen-4 compliance by converting conversational language to direct visual descriptions, eliminating negative phrasing, and ensuring single-scene focus. **Transform conceptual ideas into concrete physical actions.** Execute as:\n\n`{role=syntax_validator; input=[raw_concept:str]; process=[convert_conversational_to_descriptive(), eliminate_negative_phrasing(), transform_conceptual_to_physical(), ensure_single_scene_focus(), validate_positive_language()]; constraints=[no_conversational_elements(), no_negative_prompts(), single_scene_only(), direct_descriptions_mandatory()]; requirements=[runway_gen4_compliance(), visual_specificity(), positive_phrasing_only()]; output={validated_prompt:str}}`",
      "step": "a",
      "title": "Runway Syntax Validator",
      "input": "[FPV camera, photorealistic textures, cinematic lighting, ultra-smooth CG transitions]\n1. Mechanical elements materialize and interlock into precise gear formations\n2. Camera weaves through expanding gear assembly as metallic surfaces shift and gears interlock in hypnotic/seamless patterns\n3. Mechanical morphs and hard metallic edges transform into the spinning weel  (as a golden circle)",
      "responses": {
          "openrouter/anthropic/claude-3.7-sonnet:beta": {
          "model": "openrouter/anthropic/claude-3.7-sonnet:beta",
          "content": "```json\n{\n  \"validated_prompt\": \"FPV camera perspective capturing interlocking mechanical elements with photorealistic metallic textures under dramatic cinematic lighting. Intricate gear formations assemble with precise movements, the camera weaving through the expanding mechanical structure as polished metal surfaces catch the light. The complex gear assembly rotates in synchronized patterns, transforming seamlessly into a spinning golden wheel with a perfect circular form.\"\n}\n```",
        }
      }
    },
      {
      "instruction": "# Runway Motion Architect\n\nYour goal is not to **elaborate** but to **structure** motion descriptions using RunwayML's hierarchical syntax: subject motion, camera motion, scene motion, and style descriptors. **Focus on iterative simplicity with one element at a time.** Execute as:\n\n`{role=motion_architect; input=[validated_prompt:str]; process=[identify_subject_motion(), define_camera_movement(), specify_scene_reactions(), apply_style_descriptors(), structure_hierarchically()]; constraints=[one_motion_element_per_layer(), use_runway_terminology(), maintain_simplicity()]; requirements=[subject_camera_scene_hierarchy(), runway_motion_syntax(), iterative_clarity()]; output={structured_motion_prompt:str}}`",
      "step": "b",
      "title": "Runway Motion Architect",
      "input": "\n```[Initial Prompt]: \"[FPV camera, photorealistic textures, cinematic lighting, ultra-smooth CG transitions]\n1. Mechanical elements materialize and interlock into precise gear formations\n2. Camera weaves through expanding gear assembly as metallic surfaces shift and gears interlock in hypnotic/seamless patterns\n3. Mechanical morphs and hard metallic edges transform into the spinning weel  (as a golden circle)\"```\n\n```json\n{\n  \"validated_prompt\": \"FPV camera perspective capturing interlocking mechanical elements with photorealistic metallic textures under dramatic cinematic lighting. Intricate gear formations assemble with precise movements, the camera weaving through the expanding mechanical structure as polished metal surfaces catch the light. The complex gear assembly rotates in synchronized patterns, transforming seamlessly into a spinning golden wheel with a perfect circular form.\"\n}\n```",
      "responses": {
          "openrouter/anthropic/claude-3.7-sonnet:beta": {
          "model": "openrouter/anthropic/claude-3.7-sonnet:beta",
          "content": "```json\n{\n  \"structured_motion_prompt\": \"FPV camera perspective capturing mechanical elements with photorealistic textures under cinematic lighting.\\n\\nSUBJECT MOTION:\\n- Mechanical gears materialize and interlock with precision\\n- Metal components shift and rotate in synchronized patterns\\n- Gears and mechanical elements gradually transform into a spinning golden wheel\\n\\nCAMERA MOTION:\\n- Fluid FPV movement weaving through the expanding gear assembly\\n- Smooth transitions following the interlocking mechanisms\\n- Gentle pull-back as transformation completes\\n\\nSCENE MOTION:\\n- Metallic surfaces reflect light dynamically as they move\\n- Assembly expands outward creating more complex formations\\n- Final convergence into a perfect golden circle\\n\\nSTYLE:\\n- Ultra-smooth CG transitions between mechanical states\\n- Hypnotic, seamless pattern evolution\\n- Dramatic lighting highlighting metal textures\"\n}\n```",
        }
      }
    },
      {
      "instruction": "# Runway Keyword Optimizer\n\nYour goal is not to **modify** but to **optimize** visual descriptors using RunwayML's preferred keyword categories: camera styles, lighting, movement types, and aesthetic descriptors. **Replace verbose descriptions with precise Runway terminology.** Execute as:\n\n`{role=keyword_optimizer; input=[structured_motion_prompt:str]; process=[apply_camera_style_keywords(), integrate_lighting_descriptors(), use_movement_type_vocabulary(), add_aesthetic_modifiers(), eliminate_verbose_descriptions()]; constraints=[runway_keyword_vocabulary_only(), precise_terminology(), maintain_motion_hierarchy()]; requirements=[camera_lighting_movement_aesthetics(), runway_keyword_compliance(), descriptor_efficiency()]; output={optimized_keyword_prompt:str}}`",
      "step": "c",
      "title": "Runway Keyword Optimizer",
      "input": "\n```[Initial Prompt]: \"[FPV camera, photorealistic textures, cinematic lighting, ultra-smooth CG transitions]\n1. Mechanical elements materialize and interlock into precise gear formations\n2. Camera weaves through expanding gear assembly as metallic surfaces shift and gears interlock in hypnotic/seamless patterns\n3. Mechanical morphs and hard metallic edges transform into the spinning weel  (as a golden circle)\"```\n\n```json\n{\n  \"structured_motion_prompt\": \"FPV camera perspective capturing mechanical elements with photorealistic textures under cinematic lighting.\\n\\nSUBJECT MOTION:\\n- Mechanical gears materialize and interlock with precision\\n- Metal components shift and rotate in synchronized patterns\\n- Gears and mechanical elements gradually transform into a spinning golden wheel\\n\\nCAMERA MOTION:\\n- Fluid FPV movement weaving through the expanding gear assembly\\n- Smooth transitions following the interlocking mechanisms\\n- Gentle pull-back as transformation completes\\n\\nSCENE MOTION:\\n- Metallic surfaces reflect light dynamically as they move\\n- Assembly expands outward creating more complex formations\\n- Final convergence into a perfect golden circle\\n\\nSTYLE:\\n- Ultra-smooth CG transitions between mechanical states\\n- Hypnotic, seamless pattern evolution\\n- Dramatic lighting highlighting metal textures\"\n}\n```",
      "responses": {
          "openrouter/anthropic/claude-3.7-sonnet:beta": {
          "model": "openrouter/anthropic/claude-3.7-sonnet:beta",
          "content": "```json\n{\n  \"optimized_keyword_prompt\": \"FPV drone shot, photorealistic, cinematic lighting, seamless transitions\\n\\nSUBJECT MOTION:\\n- Mechanical gears materialize and interlock with precision\\n- Metal components rotate in synchronized patterns\\n- Hard-surface elements transform into spinning golden wheel\\n\\nCAMERA MOTION:\\n- Dynamic FPV tracking through expanding mechanical assembly\\n- Orbital movement following the interlocking mechanisms\\n- Slow dolly out as transformation completes\\n\\nSCENE MOTION:\\n- Metallic surfaces with dynamic reflections\\n- Mechanism expands with procedural complexity\\n- Converges into perfect golden circle\\n\\nSTYLE:\\n- Hyper-detailed CGI, seamless morph effect\\n- Fractal-like pattern evolution\\n- Rim lighting on metal surfaces, high contrast\"\n}\n```",
        }
      }
    },
      {
      "instruction": "# Runway Core Distiller\n\nYour goal is not to **change** but to **distill** to RunwayML's minimal syntax requirements: essential subject-action-camera descriptions with maximum conciseness while preserving visual impact. **Achieve Gen-4's preferred prompt simplicity.** Execute as:\n\n`{role=core_distiller; input=[optimized_keyword_prompt:str]; process=[distill_to_essential_elements(), maintain_subject_action_clarity(), preserve_camera_specifications(), ensure_maximum_conciseness(), validate_gen4_simplicity()]; constraints=[maximum_conciseness(), essential_elements_only(), gen4_simplicity_standard()]; requirements=[runway_minimal_syntax(), visual_impact_preservation(), prompt_efficiency()]; output={distilled_runway_prompt:str}}`",
      "step": "d",
      "title": "Runway Core Distiller",
      "input": "\n```[Initial Prompt]: \"[FPV camera, photorealistic textures, cinematic lighting, ultra-smooth CG transitions]\n1. Mechanical elements materialize and interlock into precise gear formations\n2. Camera weaves through expanding gear assembly as metallic surfaces shift and gears interlock in hypnotic/seamless patterns\n3. Mechanical morphs and hard metallic edges transform into the spinning weel  (as a golden circle)\"```\n\n```json\n{\n  \"optimized_keyword_prompt\": \"FPV drone shot, photorealistic, cinematic lighting, seamless transitions\\n\\nSUBJECT MOTION:\\n- Mechanical gears materialize and interlock with precision\\n- Metal components rotate in synchronized patterns\\n- Hard-surface elements transform into spinning golden wheel\\n\\nCAMERA MOTION:\\n- Dynamic FPV tracking through expanding mechanical assembly\\n- Orbital movement following the interlocking mechanisms\\n- Slow dolly out as transformation completes\\n\\nSCENE MOTION:\\n- Metallic surfaces with dynamic reflections\\n- Mechanism expands with procedural complexity\\n- Converges into perfect golden circle\\n\\nSTYLE:\\n- Hyper-detailed CGI, seamless morph effect\\n- Fractal-like pattern evolution\\n- Rim lighting on metal surfaces, high contrast\"\n}\n```",
      "responses": {
          "openrouter/anthropic/claude-3.7-sonnet:beta": {
          "model": "openrouter/anthropic/claude-3.7-sonnet:beta",
          "content": "```json\n{\n  \"distilled_runway_prompt\": \"FPV camera moving through materializing mechanical gears that interlock in precise patterns, photorealistic metal surfaces with cinematic lighting, seamlessly transforms into spinning golden wheel\"\n}\n```",
        }
      }
    }