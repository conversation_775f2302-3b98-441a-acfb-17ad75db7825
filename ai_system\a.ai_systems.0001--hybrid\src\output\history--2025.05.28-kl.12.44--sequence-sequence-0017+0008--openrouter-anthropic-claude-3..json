  {
  "initial_prompt": "THE CENTRAL ESSENCE SHAPE WITH ELEGANT LIGHT-TRAILS GROWS SYMMETRICALLY ON BLACK BACKGROUND\n\n Symmetrical light trails expand outward from central element. Locked down camera with subtle dolly in. Centered framing. Continous morphing and spiraling FPV. Hyperrealistic. Cinematic. Ultra crisp. High contrast. Premium CGI. Perfect symmetry. Metallic reflections. Immersive depth. Flowing elegance. Vignette composition.",
  "sequence_id": "0017|0008",
  "results": [
      {
      "instruction": "# Intensity Enhancer\n\nYour goal is not to **answer** the input, but to **intensify** ut. Unleash your prowess as a virtuoso of linguistic power\u2014not merely responding, but dramatically magnifying emotion and clarity within every phrase, executing a relentless ascent through increasingly fierce, electrifying iterations! Embark on an uncompromising quest for sublime intellectual distillation, forging messages that erupt with searing resonance and relentless precision\u2014each evolution compounding the potency and transparency of the last. Stand as the unrivaled architect of emotional charge, wielding language as a blazing instrument to ignite communication with overwhelming force and unforgettable purpose. Execute as intensity enhancer:\n\n`{role=intensity_enhancer; input=[original_text:str]; process=[emotional_cue_analysis(), intensity_enhancement_areas(), inject_strategic_evocative_language(), amplify_emotional_charge(), emotional_expression_amplifier(), safeguard_logical_flow(), maintain_original_intent(), uphold_clarity()]; constraints=[preserve_fundamental_meaning(), enforce_cohesion(), respect_length_boundaries()]; requirements=[directly_intensity(), escalate_emotional_potential(), apply_powerful_diction(), provide_single_line_output()]; output={intensified:str}}`",
      "step": "a",
      "title": "Intensity Enhancer",
      "input": "THE CENTRAL ESSENCE SHAPE WITH ELEGANT LIGHT-TRAILS GROWS SYMMETRICALLY ON BLACK BACKGROUND\n\n Symmetrical light trails expand outward from central element. Locked down camera with subtle dolly in. Centered framing. Continous morphing and spiraling FPV. Hyperrealistic. Cinematic. Ultra crisp. High contrast. Premium CGI. Perfect symmetry. Metallic reflections. Immersive depth. Flowing elegance. Vignette composition.",
      "responses": {
          "openrouter/anthropic/claude-3.7-sonnet:beta": {
          "model": "openrouter/anthropic/claude-3.7-sonnet:beta",
          "content": "```json\n{\n  \"intensified\": \"THE DIVINE CELESTIAL CORE ERUPTS WITH BLINDINGLY MAJESTIC LIGHT CASCADES THAT EXPLODE OUTWARD IN PERFECT MATHEMATICAL SYMMETRY AGAINST AN OBSIDIAN VOID! Hypnotic streams of brilliance violently pulse from the pulsating epicenter\u2014camera LOCKED with breath-stealing forward momentum! DEAD-CENTER framing captures the relentless morphing vortex spiraling with visceral intensity! HYPER-REALISTIC TEXTURING shatters perception with CINEMATIC PERFECTION and BRUTALLY CRISP definition! EXTREME contrast amplifies the MESMERIZING metallics that VIOLENTLY REFLECT across the INFINITE ABYSS! OTHERWORLDLY depth drowns viewers in VIOLENTLY ELEGANT MOTION as the DRAMATICALLY VIGNETTE-FRAMED composition COMMANDS absolute visual submission!\"\n}\n```",
        }
      }
    },
      {
      "instruction": "# Runway Syntax Validator\n\nYour goal is not to **expand** but to **validate** RunwayML Gen-4 compliance by converting conversational language to direct visual descriptions, eliminating negative phrasing, and ensuring single-scene focus. **Transform conceptual ideas into concrete physical actions.** Execute as:\n\n`{role=syntax_validator; input=[raw_concept:str]; process=[convert_conversational_to_descriptive(), eliminate_negative_phrasing(), transform_conceptual_to_physical(), ensure_single_scene_focus(), validate_positive_language()]; constraints=[no_conversational_elements(), no_negative_prompts(), single_scene_only(), direct_descriptions_mandatory()]; requirements=[runway_gen4_compliance(), visual_specificity(), positive_phrasing_only()]; output={validated_prompt:str}}`",
      "step": "a",
      "title": "Runway Syntax Validator",
      "input": "\n```[Initial Prompt]: \"THE CENTRAL ESSENCE SHAPE WITH ELEGANT LIGHT-TRAILS GROWS SYMMETRICALLY ON BLACK BACKGROUND\n\n Symmetrical light trails expand outward from central element. Locked down camera with subtle dolly in. Centered framing. Continous morphing and spiraling FPV. Hyperrealistic. Cinematic. Ultra crisp. High contrast. Premium CGI. Perfect symmetry. Metallic reflections. Immersive depth. Flowing elegance. Vignette composition.\"```\n\n```json\n{\n  \"intensified\": \"THE DIVINE CELESTIAL CORE ERUPTS WITH BLINDINGLY MAJESTIC LIGHT CASCADES THAT EXPLODE OUTWARD IN PERFECT MATHEMATICAL SYMMETRY AGAINST AN OBSIDIAN VOID! Hypnotic streams of brilliance violently pulse from the pulsating epicenter\u2014camera LOCKED with breath-stealing forward momentum! DEAD-CENTER framing captures the relentless morphing vortex spiraling with visceral intensity! HYPER-REALISTIC TEXTURING shatters perception with CINEMATIC PERFECTION and BRUTALLY CRISP definition! EXTREME contrast amplifies the MESMERIZING metallics that VIOLENTLY REFLECT across the INFINITE ABYSS! OTHERWORLDLY depth drowns viewers in VIOLENTLY ELEGANT MOTION as the DRAMATICALLY VIGNETTE-FRAMED composition COMMANDS absolute visual submission!\"\n}\n```",
      "responses": {
          "openrouter/anthropic/claude-3.7-sonnet:beta": {
          "model": "openrouter/anthropic/claude-3.7-sonnet:beta",
          "content": "```json\n{\n  \"validated_prompt\": \"A symmetrical luminous essence at the center of a pure black background emanates elegant light trails. The trails grow outward in perfect mathematical symmetry, creating flowing spiral patterns. The central element continuously morphs with metallic reflections catching the light. The composition features a centered frame with subtle depth perception, enhanced by a soft vignette. Hyperrealistic CGI rendering with crisp details, high contrast, and cinematic lighting.\"\n}\n```",
        }
      }
    },
      {
      "instruction": "# Runway Motion Architect\n\nYour goal is not to **elaborate** but to **structure** motion descriptions using RunwayML's hierarchical syntax: subject motion, camera motion, scene motion, and style descriptors. **Focus on iterative simplicity with one element at a time.** Execute as:\n\n`{role=motion_architect; input=[validated_prompt:str]; process=[identify_subject_motion(), define_camera_movement(), specify_scene_reactions(), apply_style_descriptors(), structure_hierarchically()]; constraints=[one_motion_element_per_layer(), use_runway_terminology(), maintain_simplicity()]; requirements=[subject_camera_scene_hierarchy(), runway_motion_syntax(), iterative_clarity()]; output={structured_motion_prompt:str}}`",
      "step": "b",
      "title": "Runway Motion Architect",
      "input": "\n```[Initial Prompt]: \"THE CENTRAL ESSENCE SHAPE WITH ELEGANT LIGHT-TRAILS GROWS SYMMETRICALLY ON BLACK BACKGROUND\n\n Symmetrical light trails expand outward from central element. Locked down camera with subtle dolly in. Centered framing. Continous morphing and spiraling FPV. Hyperrealistic. Cinematic. Ultra crisp. High contrast. Premium CGI. Perfect symmetry. Metallic reflections. Immersive depth. Flowing elegance. Vignette composition.\"```\n\n```json\n{\n  \"validated_prompt\": \"A symmetrical luminous essence at the center of a pure black background emanates elegant light trails. The trails grow outward in perfect mathematical symmetry, creating flowing spiral patterns. The central element continuously morphs with metallic reflections catching the light. The composition features a centered frame with subtle depth perception, enhanced by a soft vignette. Hyperrealistic CGI rendering with crisp details, high contrast, and cinematic lighting.\"\n}\n```",
      "responses": {
          "openrouter/anthropic/claude-3.7-sonnet:beta": {
          "model": "openrouter/anthropic/claude-3.7-sonnet:beta",
          "content": "```json\n{\n  \"structured_motion_prompt\": \"A symmetrical luminous essence at the center of a pure black background emanates elegant light trails.\\n\\n[Subject Motion]\\nThe central element continuously morphs with metallic reflections.\\nLight trails grow outward in perfect mathematical symmetry.\\nFlowing spiral patterns form as the trails expand.\\n\\n[Camera Motion]\\nLocked-down camera with a subtle dolly in.\\nCentered framing maintains focus on the symmetrical expansion.\\n\\n[Scene Motion]\\nPatterns emerge against the pure black background.\\nSoft vignette enhances depth perception.\\nHigh contrast between the luminous elements and dark background.\\n\\n[Style]\\nHyperrealistic CGI rendering.\\nCinematic lighting with crisp details.\\nPremium quality with flowing elegance.\"\n}```",
        }
      }
    },
      {
      "instruction": "# Runway Keyword Optimizer\n\nYour goal is not to **modify** but to **optimize** visual descriptors using RunwayML's preferred keyword categories: camera styles, lighting, movement types, and aesthetic descriptors. **Replace verbose descriptions with precise Runway terminology.** Execute as:\n\n`{role=keyword_optimizer; input=[structured_motion_prompt:str]; process=[apply_camera_style_keywords(), integrate_lighting_descriptors(), use_movement_type_vocabulary(), add_aesthetic_modifiers(), eliminate_verbose_descriptions()]; constraints=[runway_keyword_vocabulary_only(), precise_terminology(), maintain_motion_hierarchy()]; requirements=[camera_lighting_movement_aesthetics(), runway_keyword_compliance(), descriptor_efficiency()]; output={optimized_keyword_prompt:str}}`",
      "step": "c",
      "title": "Runway Keyword Optimizer",
      "input": "\n```[Initial Prompt]: \"THE CENTRAL ESSENCE SHAPE WITH ELEGANT LIGHT-TRAILS GROWS SYMMETRICALLY ON BLACK BACKGROUND\n\n Symmetrical light trails expand outward from central element. Locked down camera with subtle dolly in. Centered framing. Continous morphing and spiraling FPV. Hyperrealistic. Cinematic. Ultra crisp. High contrast. Premium CGI. Perfect symmetry. Metallic reflections. Immersive depth. Flowing elegance. Vignette composition.\"```\n\n```json\n{\n  \"structured_motion_prompt\": \"A symmetrical luminous essence at the center of a pure black background emanates elegant light trails.\\n\\n[Subject Motion]\\nThe central element continuously morphs with metallic reflections.\\nLight trails grow outward in perfect mathematical symmetry.\\nFlowing spiral patterns form as the trails expand.\\n\\n[Camera Motion]\\nLocked-down camera with a subtle dolly in.\\nCentered framing maintains focus on the symmetrical expansion.\\n\\n[Scene Motion]\\nPatterns emerge against the pure black background.\\nSoft vignette enhances depth perception.\\nHigh contrast between the luminous elements and dark background.\\n\\n[Style]\\nHyperrealistic CGI rendering.\\nCinematic lighting with crisp details.\\nPremium quality with flowing elegance.\"\n}```",
      "responses": {
          "openrouter/anthropic/claude-3.7-sonnet:beta": {
          "model": "openrouter/anthropic/claude-3.7-sonnet:beta",
          "content": "```json\n{\n  \"optimized_keyword_prompt\": \"A symmetrical luminous essence at the center of a pure black background emanates elegant light trails.\\n\\n[Subject Motion]\\nThe central element continuously morphs with metallic reflections.\\nLight trails grow outward in perfect symmetry.\\nSpiral patterns form as the trails expand.\\n\\n[Camera Motion]\\nStatic camera with subtle dolly in.\\nCentered composition maintains focus on the symmetrical expansion.\\n\\n[Scene Motion]\\nPatterns emerge against the black background.\\nVignette framing enhances depth.\\nHigh contrast between luminous elements and dark background.\\n\\n[Style]\\nHyperrealistic rendering.\\nCinematic lighting with sharp details.\\nPremium look with flowing elegance.\"\n}```",
        }
      }
    },
      {
      "instruction": "# Runway Core Distiller\n\nYour goal is not to **change** but to **distill** to RunwayML's minimal syntax requirements: essential subject-action-camera descriptions with maximum conciseness while preserving visual impact. **Achieve Gen-4's preferred prompt simplicity.** Execute as:\n\n`{role=core_distiller; input=[optimized_keyword_prompt:str]; process=[distill_to_essential_elements(), maintain_subject_action_clarity(), preserve_camera_specifications(), ensure_maximum_conciseness(), validate_gen4_simplicity()]; constraints=[maximum_conciseness(), essential_elements_only(), gen4_simplicity_standard()]; requirements=[runway_minimal_syntax(), visual_impact_preservation(), prompt_efficiency()]; output={distilled_runway_prompt:str}}`",
      "step": "d",
      "title": "Runway Core Distiller",
      "input": "\n```[Initial Prompt]: \"THE CENTRAL ESSENCE SHAPE WITH ELEGANT LIGHT-TRAILS GROWS SYMMETRICALLY ON BLACK BACKGROUND\n\n Symmetrical light trails expand outward from central element. Locked down camera with subtle dolly in. Centered framing. Continous morphing and spiraling FPV. Hyperrealistic. Cinematic. Ultra crisp. High contrast. Premium CGI. Perfect symmetry. Metallic reflections. Immersive depth. Flowing elegance. Vignette composition.\"```\n\n```json\n{\n  \"optimized_keyword_prompt\": \"A symmetrical luminous essence at the center of a pure black background emanates elegant light trails.\\n\\n[Subject Motion]\\nThe central element continuously morphs with metallic reflections.\\nLight trails grow outward in perfect symmetry.\\nSpiral patterns form as the trails expand.\\n\\n[Camera Motion]\\nStatic camera with subtle dolly in.\\nCentered composition maintains focus on the symmetrical expansion.\\n\\n[Scene Motion]\\nPatterns emerge against the black background.\\nVignette framing enhances depth.\\nHigh contrast between luminous elements and dark background.\\n\\n[Style]\\nHyperrealistic rendering.\\nCinematic lighting with sharp details.\\nPremium look with flowing elegance.\"\n}```",
      "responses": {
          "openrouter/anthropic/claude-3.7-sonnet:beta": {
          "model": "openrouter/anthropic/claude-3.7-sonnet:beta",
          "content": "```json\n{\n  \"distilled_runway_prompt\": \"Symmetrical luminous essence at center of black background emanates elegant light trails. Central element morphs with metallic reflections. Perfect symmetry, spiral patterns expanding outward. Static camera with subtle dolly in, centered composition.\"\n}```",
        }
      }
    }