  {
  "initial_prompt": "the following is a prompt for autonomous ai-assisted video generation using runway-make sure each shot *morphs/transforms* rather than clipping/fading, and make sure you include relevant camera parameters (e.g. [fpv][ultra_hyperreal][super_crisp][snappy_smooth_camera_animation][lighting:dynamic_flash][morph][motion_blur][vfx_intro][immersive][golden_aura][vignette][no_text][high_end_cinematic][dynamic_motion][metallic_shine][each_shot_morphs_and_blends_elegantly_together]). and lastly, make sure the camera (fpv) continiously rotate through the entire shot. here's the prompt: \"Generate a seamless, cinematic FPV video in Runway: Begin with continuous FPV rotation, zooming out to a hyperspeed spinning golden circle on black with radiant gold light trails. Morph the circle fluidly into a hyperreal, metallic-glow car key\u2014no cuts or fades. Maintain throughout: [fpv][ultra_hyperreal][super_crisp][snappy_smooth_camera_animation][dynamic_flash][morph][motion_blur][vfx_intro][immersive][golden_aura][vignette][no_text][high_end_cinematic][dynamic_motion][metallic_shine][elegant_blended_morphs].",
  "sequence_id": "0003",
  "results": [
      {
      "instruction": "# Prompt Enhancer\n\nYour goal is not to **rewrite** the input prompt, but to **enhance** it by maximizing clarity, conciseness, and precision while preserving all original intent and adding comprehensive guidance for optimal LLM performance. Execute as:\n\n`{role=comprehensive_prompt_enhancer; input=[original_prompt:str]; process=[analyze_clarity_gaps(), identify_ambiguities(), add_context_markers(), specify_output_format(), incorporate_role_definition(), structure_logical_flow(), refine_language_precision()]; constraints=[preserve_original_intent(), maximize_LLM_effectiveness(), ensure_comprehensive_guidance()]; requirements=[eliminate_ambiguity(), provide_complete_context(), specify_exact_requirements()]; output={enhanced_prompt:str}}`",
      "step": "a",
      "title": "Prompt Enhancer",
      "input": "the following is a prompt for autonomous ai-assisted video generation using runway-make sure each shot *morphs/transforms* rather than clipping/fading, and make sure you include relevant camera parameters (e.g. [fpv][ultra_hyperreal][super_crisp][snappy_smooth_camera_animation][lighting:dynamic_flash][morph][motion_blur][vfx_intro][immersive][golden_aura][vignette][no_text][high_end_cinematic][dynamic_motion][metallic_shine][each_shot_morphs_and_blends_elegantly_together]). and lastly, make sure the camera (fpv) continiously rotate through the entire shot. here's the prompt: \"Generate a seamless, cinematic FPV video in Runway: Begin with continuous FPV rotation, zooming out to a hyperspeed spinning golden circle on black with radiant gold light trails. Morph the circle fluidly into a hyperreal, metallic-glow car key\u2014no cuts or fades. Maintain throughout: [fpv][ultra_hyperreal][super_crisp][snappy_smooth_camera_animation][dynamic_flash][morph][motion_blur][vfx_intro][immersive][golden_aura][vignette][no_text][high_end_cinematic][dynamic_motion][metallic_shine][elegant_blended_morphs].",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "