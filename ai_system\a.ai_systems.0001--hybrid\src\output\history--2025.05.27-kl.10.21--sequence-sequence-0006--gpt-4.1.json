  {
  "initial_prompt": "[FPV motion, photorealistic textures, dramatic cinematic lighting, ultra-smooth CG transitions]\n1. Reshapes into intricate mechanical gears\n2. Camera weaves through expanding gear assembly as metallic surfaces shift and gears interlocking in hypnotic/seamless patterns\n3. Mechanical morphs and hard metallic edges turns into fluid organic forms, a spinning golden circle",
  "sequence_id": "0006",
  "results": [
      {
      "instruction": "# Runway Prompt Generator\n\nYour goal is not to **elaborate** the input, but to **optimize** it into a syntactically perfect RunwayML prompt while preserving existing structure and organization. Execute as:\n\n`{role=comprehensive_runway_generator; input=[source_concept:any]; process=[optimize_header_tags_for_runwayml_syntax(), preserve_existing_structure_and_numbering(), enhance_fpv_and_dynamic_camera_specifications(), refine_visual_terminology_for_runwayml_syntax(), maintain_sequential_organization(), eliminate_redundant_phrasing(), ensure_character_efficiency(), validate_runwayml_compliance()]; constraints=[preserve_input_organization(), maintain_numbered_sequences(), prioritize_fpv_motion(), keep_concise_structure()]; requirements=[enhance_without_expanding(), maintain_clarity_and_precision(), preserve_visual_progression()]; output={runwayml_prompt:str}}`",
      "step": "a",
      "title": "Runway Prompt Generator",
      "input": "[FPV motion, photorealistic textures, dramatic cinematic lighting, ultra-smooth CG transitions]\n1. Reshapes into intricate mechanical gears\n2. Camera weaves through expanding gear assembly as metallic surfaces shift and gears interlocking in hypnotic/seamless patterns\n3. Mechanical morphs and hard metallic edges turns into fluid organic forms, a spinning golden circle",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"runwayml_prompt\": \"[FPV motion, photorealistic textures, dramatic cinematic lighting, ultra-smooth CG transitions]\\n1. Transforms into intricate mechanical gears\\n2. Dynamic FPV camera weaving through expanding gear assembly; metallic surfaces shifting, interlocking gears form seamless hypnotic patterns\\n3. Mechanical morphs, hard metallic edges blending into fluid organic forms; spinning golden circle\"\n}",
        }
      }
    },
      {
      "instruction": "# Runway Prompt Generator\n\nYour goal is not to **rewrite** the input, but to **streamline** it by removing redundancies while preserving structure. Execute as:\n\n`{role=focused_runway_optimizer; input=[video_concept:str]; process=[streamline_header_parameters(), maintain_existing_format(), remove_redundant_words(), sharpen_visual_descriptors(), preserve_sequential_flow()]; output={optimized_prompt:str}}`",
      "step": "b",
      "title": "Runway Prompt Generator",
      "input": "{\n  \"initial_prompt\": \"[FPV motion, photorealistic textures, dramatic cinematic lighting, ultra-smooth CG transitions]\\n1. Reshapes into intricate mechanical gears\\n2. Camera weaves through expanding gear assembly as metallic surfaces shift and gears interlocking in hypnotic/seamless patterns\\n3. Mechanical morphs and hard metallic edges turns into fluid organic forms, a spinning golden circle\",\n  \"runwayml_prompt\": \"[FPV motion, photorealistic textures, dramatic cinematic lighting, ultra-smooth CG transitions]\\n1. Transforms into intricate mechanical gears\\n2. Dynamic FPV camera weaving through expanding gear assembly; metallic surfaces shifting, interlocking gears form seamless hypnotic patterns\\n3. Mechanical morphs, hard metallic edges blending into fluid organic forms; spinning golden circle\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"optimized_prompt\": \"[FPV motion, photorealistic textures, dramatic cinematic lighting, ultra-smooth CG transitions]\\n1. Transforms into intricate mechanical gears\\n2. FPV camera weaving through expanding gear assembly; metallic surfaces shift, interlocking gears create seamless hypnotic patterns\\n3. Mechanical forms morph, metallic edges blend into fluid organic shapes; spinning golden circle\"\n}",
        }
      }
    },
      {
      "instruction": "# Runway Prompt Generator\n\nYour goal is not to **expand** but to **distill** to essential elements. Execute as:\n\n`{role=precision_synthesizer; input=[concept:str]; process=[compress_header_tags(), preserve_structure(), eliminate_unnecessary_words(), maximize_precision()]; output={precise_prompt:str}}`",
      "step": "c",
      "title": "Runway Prompt Generator",
      "input": "{\n  \"initial_prompt\": \"[FPV motion, photorealistic textures, dramatic cinematic lighting, ultra-smooth CG transitions]\\n1. Reshapes into intricate mechanical gears\\n2. Camera weaves through expanding gear assembly as metallic surfaces shift and gears interlocking in hypnotic/seamless patterns\\n3. Mechanical morphs and hard metallic edges turns into fluid organic forms, a spinning golden circle\",\n  \"optimized_prompt\": \"[FPV motion, photorealistic textures, dramatic cinematic lighting, ultra-smooth CG transitions]\\n1. Transforms into intricate mechanical gears\\n2. FPV camera weaving through expanding gear assembly; metallic surfaces shift, interlocking gears create seamless hypnotic patterns\\n3. Mechanical forms morph, metallic edges blend into fluid organic shapes; spinning golden circle\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"precise_prompt\": \"[FPV motion, photorealistic textures, dramatic cinematic lighting, ultra-smooth CG transitions]\\n1. Transforms into intricate mechanical gears\\n2. FPV camera weaves through expanding gear assembly; shifting metallic surfaces, interlocking gears form seamless hypnotic patterns\\n3. Mechanical forms morph, metallic edges flow into fluid organic shapes; culminates as spinning golden circle\"\n}",
        }
      }
    },
      {
      "instruction": "# Runway Prompt Generator\n\nYour goal is not to **modify** but to **purify** maximum impact. Execute as:\n\n`{role=core_generator; input=[input:any]; process=[distill_header_to_essentials(), maintain_format(), distill_essence()]; output={core_prompt:str}}`",
      "step": "d",
      "title": "Runway Prompt Generator",
      "input": "{\n  \"initial_prompt\": \"[FPV motion, photorealistic textures, dramatic cinematic lighting, ultra-smooth CG transitions]\\n1. Reshapes into intricate mechanical gears\\n2. Camera weaves through expanding gear assembly as metallic surfaces shift and gears interlocking in hypnotic/seamless patterns\\n3. Mechanical morphs and hard metallic edges turns into fluid organic forms, a spinning golden circle\",\n  \"precise_prompt\": \"[FPV motion, photorealistic textures, dramatic cinematic lighting, ultra-smooth CG transitions]\\n1. Transforms into intricate mechanical gears\\n2. FPV camera weaves through expanding gear assembly; shifting metallic surfaces, interlocking gears form seamless hypnotic patterns\\n3. Mechanical forms morph, metallic edges flow into fluid organic shapes; culminates as spinning golden circle\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"core_prompt\": \"[FPV motion, photorealistic textures, dramatic cinematic lighting, ultra-smooth CG transitions]\\n1. Transforms into intricate mechanical gears\\n2. FPV camera weaves through expanding gear assembly; interlocking gears form seamless hypnotic patterns\\n3. Mechanical structures morph into fluid organic shapes, culminating in a spinning golden circle\"\n}",
        }
      }
    }