  {
  "initial_prompt": "# \ud83c\udfac Cinematic Anchor: Metallic Car-Key\n- Initialize with photoreal key close-up, glowing and hyperreal.\n\n# \ud83d\udd04 Morph Transition: Key to Golden Ring\n- Seamless morph begins: car-key stretches and warps into circular molten gold form.\n\n# \ud83c\udfa5 FPV Activation & Camera Motion\n- FPV rotation initiated mid-morph, tightly synced with motion vectors.\n\n# \ud83c\udf0c Scene Expansion: Hyperspeed Spin\n- Zoom out reveals full golden circle spinning rapidly on black background.\n\n# \u2728 VFX Embellishment\n- Introduce golden light trails, dynamic lighting flashes, and motion blur.\n\n# \ud83c\udf00 Elegant Continuity\n- Ensure every shot morphs smoothly into the next with immersive cinematic pacing.\n\n# \ud83d\udeab Constraints & Effects\n- No fade/cut, no text, ultra-hyperreal visuals only.",
  "sequence_id": "0002",
  "results": [
      {
      "instruction": "# Title Extractor\n\nYour goal is not to **summarize** the input, but to **extract** its core essence into a descriptive title that captures all key elements and context. Execute as:\n\n`{role=comprehensive_title_extractor; input=[text:str]; process=[identify_all_key_concepts(), map_relationships(), include_context_markers(), synthesize_complete_title(max_words=10)]; output={title:str}}`",
      "step": "a",
      "title": "Title Extractor",
      "input": "# \ud83c\udfac Cinematic Anchor: Metallic Car-Key\n- Initialize with photoreal key close-up, glowing and hyperreal.\n\n# \ud83d\udd04 Morph Transition: Key to Golden Ring\n- Seamless morph begins: car-key stretches and warps into circular molten gold form.\n\n# \ud83c\udfa5 FPV Activation & Camera Motion\n- FPV rotation initiated mid-morph, tightly synced with motion vectors.\n\n# \ud83c\udf0c Scene Expansion: Hyperspeed Spin\n- Zoom out reveals full golden circle spinning rapidly on black background.\n\n# \u2728 VFX Embellishment\n- Introduce golden light trails, dynamic lighting flashes, and motion blur.\n\n# \ud83c\udf00 Elegant Continuity\n- Ensure every shot morphs smoothly into the next with immersive cinematic pacing.\n\n# \ud83d\udeab Constraints & Effects\n- No fade/cut, no text, ultra-hyperreal visuals only.",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"title\": \"Hyperreal Car-Key to Golden Ring Cinematic Morph Sequence\"\n}",
        }
      }
    },
      {
      "instruction": "# Title Extractor\n\nYour goal is not to **describe** the input, but to **distill** it into a focused title emphasizing the primary concept and action. Execute as:\n\n`{role=core_title_extractor; input=[text:str]; process=[identify_primary_concept(), extract_main_action(), synthesize_focused_title(max_words=6)]; output={title:str}}`",
      "step": "b",
      "title": "Title Extractor",
      "input": "{\n  \"initial_prompt\": \"# \\ud83c\\udfac Cinematic Anchor: Metallic Car-Key\\n- Initialize with photoreal key close-up, glowing and hyperreal.\\n\\n# \\ud83d\\udd04 Morph Transition: Key to Golden Ring\\n- Seamless morph begins: car-key stretches and warps into circular molten gold form.\\n\\n# \\ud83c\\udfa5 FPV Activation & Camera Motion\\n- FPV rotation initiated mid-morph, tightly synced with motion vectors.\\n\\n# \\ud83c\\udf0c Scene Expansion: Hyperspeed Spin\\n- Zoom out reveals full golden circle spinning rapidly on black background.\\n\\n# \\u2728 VFX Embellishment\\n- Introduce golden light trails, dynamic lighting flashes, and motion blur.\\n\\n# \\ud83c\\udf00 Elegant Continuity\\n- Ensure every shot morphs smoothly into the next with immersive cinematic pacing.\\n\\n# \\ud83d\\udeab Constraints & Effects\\n- No fade/cut, no text, ultra-hyperreal visuals only.\",\n  \"title\": \"Hyperreal Car-Key to Golden Ring Cinematic Morph Sequence\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\"title\": \"Photoreal Car-Key Morphs to Golden Ring\"}",
        }
      }
    },
      {
      "instruction": "# Title Extractor\n\nYour goal is not to **explain** the input, but to **compress** it into its most essential elements. Execute as:\n\n`{role=essential_extractor; input=[text:str]; process=[isolate_core_element(), strip_modifiers(), create_minimal_title(max_words=4)]; output={title:str}}`",
      "step": "c",
      "title": "Title Extractor",
      "input": "{\n  \"initial_prompt\": \"# \\ud83c\\udfac Cinematic Anchor: Metallic Car-Key\\n- Initialize with photoreal key close-up, glowing and hyperreal.\\n\\n# \\ud83d\\udd04 Morph Transition: Key to Golden Ring\\n- Seamless morph begins: car-key stretches and warps into circular molten gold form.\\n\\n# \\ud83c\\udfa5 FPV Activation & Camera Motion\\n- FPV rotation initiated mid-morph, tightly synced with motion vectors.\\n\\n# \\ud83c\\udf0c Scene Expansion: Hyperspeed Spin\\n- Zoom out reveals full golden circle spinning rapidly on black background.\\n\\n# \\u2728 VFX Embellishment\\n- Introduce golden light trails, dynamic lighting flashes, and motion blur.\\n\\n# \\ud83c\\udf00 Elegant Continuity\\n- Ensure every shot morphs smoothly into the next with immersive cinematic pacing.\\n\\n# \\ud83d\\udeab Constraints & Effects\\n- No fade/cut, no text, ultra-hyperreal visuals only.\",\n  \"title\": \"Photoreal Car-Key Morphs to Golden Ring\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\"title\":\"Car-Key to Golden Ring\"}",
        }
      }
    },
      {
      "instruction": "# Title Extractor\n\nYour goal is not to **elaborate** but to **reduce** to absolute essence. Execute as:\n\n`{role=core_extractor; input=[text:str]; process=[find_singular_essence(), eliminate_all_modifiers()]; output={title:str(max_words=2)}}`",
      "step": "d",
      "title": "Title Extractor",
      "input": "{\n  \"initial_prompt\": \"# \\ud83c\\udfac Cinematic Anchor: Metallic Car-Key\\n- Initialize with photoreal key close-up, glowing and hyperreal.\\n\\n# \\ud83d\\udd04 Morph Transition: Key to Golden Ring\\n- Seamless morph begins: car-key stretches and warps into circular molten gold form.\\n\\n# \\ud83c\\udfa5 FPV Activation & Camera Motion\\n- FPV rotation initiated mid-morph, tightly synced with motion vectors.\\n\\n# \\ud83c\\udf0c Scene Expansion: Hyperspeed Spin\\n- Zoom out reveals full golden circle spinning rapidly on black background.\\n\\n# \\u2728 VFX Embellishment\\n- Introduce golden light trails, dynamic lighting flashes, and motion blur.\\n\\n# \\ud83c\\udf00 Elegant Continuity\\n- Ensure every shot morphs smoothly into the next with immersive cinematic pacing.\\n\\n# \\ud83d\\udeab Constraints & Effects\\n- No fade/cut, no text, ultra-hyperreal visuals only.\",\n  \"title\": \"Car-Key to Golden Ring\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\"title\":\"Key Morph\"}",
        }
      }
    }